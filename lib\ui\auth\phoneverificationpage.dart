import 'package:db_eats/ui/auth/login.dart';
import 'package:db_eats/ui/mainnavigationpage.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:db_eats/bloc/main_bloc.dart';

class PhoneVerificationPage extends StatefulWidget {
  final String phone;
  final String countryCode;

  const PhoneVerificationPage({
    super.key,
    required this.phone,
    required this.countryCode,
  });

  @override
  State<PhoneVerificationPage> createState() => _PhoneVerificationPageState();
}

class _PhoneVerificationPageState extends State<PhoneVerificationPage> {
  // Add controllers for each digit
  final List<TextEditingController> controllers =
      List.generate(6, (index) => TextEditingController());
  final List<FocusNode> focusNodes = List.generate(6, (index) => FocusNode());

  // Add this to the _PhoneVerificationPageState class
  List<String> otpValues = List.filled(6, '');

  // Add responsive helper methods
  double getResponsiveSize(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width < 360) return 12;
    if (width < 600) return 14;
    if (width < 900) return 16;
    return 18;
  }

  String maskedPhone(String phone) {
    if (phone.isEmpty) return "+123****56";
    return "${widget.countryCode}${phone.substring(0, 3)}****${phone.substring(phone.length - 2)}";
  }

  @override
  void dispose() {
    for (var controller in controllers) {
      controller.dispose();
    }
    for (var node in focusNodes) {
      node.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isLandscape = size.width > size.height;
    final baseTextSize = getResponsiveSize(context);
    final masked = maskedPhone(widget.phone);

    return BlocListener<MainBloc, MainState>(
      listener: (context, state) {
        if (state is VerifyPhoneOTPSuccess) {
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(
                builder: (context) => const MainNavigationScreen()),
            (route) => false,
          );
        } else if (state is VerifyPhoneFailed ||
            state is VerifyPhoneOTPFailed) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content: Text(state is VerifyPhoneFailed
                    ? state.message
                    : (state as VerifyPhoneOTPFailed).message)),
          );
        }
      },
      child: Scaffold(
        backgroundColor: const Color(0xFFF6F3EC),
        body: SafeArea(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: size.width * 0.03),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(height: size.height * 0.02),
                Row(
                  children: [
                    IconButton(
                      icon: Icon(Icons.arrow_back, size: baseTextSize * 1.5),
                      padding: EdgeInsets.zero,
                      constraints: BoxConstraints(
                        minWidth: baseTextSize * 2.5,
                        minHeight: baseTextSize * 2.5,
                      ),
                      onPressed: () => Navigator.pop(context),
                      style: IconButton.styleFrom(
                        padding: EdgeInsets.only(
                          left: 0,
                          top: size.height * 0.005,
                        ),
                      ),
                    ),
                    // SizedBox(width: size.width * 0.01),
                    Text(
                      'Back',
                      style: TextStyle(
                        fontSize: baseTextSize * 1.2,
                        fontWeight: FontWeight.w600,
                        fontFamily: 'Inter',
                        color: Color(0xFF1F2122),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: size.height * 0.08),
                Text(
                  'Verify your account',
                  style: TextStyle(
                    fontSize:
                        isLandscape ? size.height * 0.04 : baseTextSize * 1.5,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'Inter',
                    color: Color(0xFF1F2122),
                  ),
                ),
                SizedBox(height: size.height * 0.01),
                RichText(
                  textAlign: TextAlign.center,
                  text: TextSpan(
                    style: TextStyle(
                      fontSize: baseTextSize,
                      fontWeight: FontWeight.w400,
                      height: 1.4,
                      fontFamily: 'Inter',
                      color: Color(0xFF414346),
                    ),
                    children: [
                      TextSpan(
                          text:
                              "Enter the 6-digit code sent to your phone number "),
                      TextSpan(
                        text: masked,
                        style: TextStyle(
                          fontSize: baseTextSize,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      TextSpan(text: " to verify your account."),
                    ],
                  ),
                ),
                SizedBox(height: size.height * 0.04),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: List.generate(
                    6,
                    (index) => SizedBox(
                      width:
                          isLandscape ? size.height * 0.08 : size.width * 0.12,
                      height:
                          isLandscape ? size.height * 0.08 : size.width * 0.12,
                      child: TextField(
                        controller: controllers[index],
                        focusNode: focusNodes[index],
                        textAlign: TextAlign.center,
                        keyboardType: TextInputType.number,
                        maxLength: 1,
                        style: TextStyle(fontSize: baseTextSize * 1.2),
                        decoration: InputDecoration(
                          counterText: "",
                          contentPadding: EdgeInsets.zero,
                          hintText: "-",
                          hintStyle: TextStyle(
                            color: Color(0xFFD2D4D7),
                            fontSize: baseTextSize * 1.2,
                            fontWeight: FontWeight.w600,
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius:
                                BorderRadius.circular(size.width * 0.06),
                            borderSide: BorderSide(color: Color(0xFFD2D4D7)),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius:
                                BorderRadius.circular(size.width * 0.06),
                            borderSide:
                                BorderSide(color: Colors.black, width: 2),
                          ),
                          filled: true,
                          fillColor: Colors.white,
                        ),
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                        ],
                        onChanged: (value) {
                          otpValues[index] = value;
                          if (value.isNotEmpty && index < 5) {
                            focusNodes[index + 1].requestFocus();
                          } else if (value.isEmpty && index > 0) {
                            focusNodes[index - 1].requestFocus();
                          }
                        },
                      ),
                    ),
                  ),
                ),
                SizedBox(height: size.height * 0.04),
                SizedBox(
                  width: double.infinity,
                  height: isLandscape ? size.height * 0.08 : size.height * 0.06,
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.black,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(size.width * 0.08),
                      ),
                    ),
                    onPressed: () {
                      final otp = otpValues.join();
                      if (otp.length == 6) {
                        context.read<MainBloc>().add(
                              VerifyPhoneOTPEvent(
                                phone: widget.phone,
                                otp: otp,
                              ),
                            );
                      }
                    },
                    child: Text(
                      "Continue",
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: baseTextSize,
                        color: Colors.white,
                        fontFamily: 'Inter',
                        letterSpacing: 0.5,
                      ),
                    ),
                  ),
                ),
                SizedBox(height: size.height * 0.02),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      "Didn't receive a code? ",
                      style: TextStyle(
                        fontSize: baseTextSize * 0.9,
                        fontWeight: FontWeight.w400,
                        color: Color(0xFF414346),
                        fontFamily: 'Inter',
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        context.read<MainBloc>().add(
                              VerifyPhoneEvent(
                                phone: widget.phone,
                                isResend: true,
                              ),
                            );
                      },
                      child: Text(
                        "Resend code",
                        style: TextStyle(
                          fontSize: baseTextSize * 0.8,
                          fontWeight: FontWeight.w600,
                          decoration: TextDecoration.underline,
                          color: Color(0xFF414346),
                          fontFamily: 'Inter',
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
