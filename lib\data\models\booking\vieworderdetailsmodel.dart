class ViewOrderDetailsModel {
  bool? status;
  ViewOrderDetailsData? data;
  int? statusCode;

  ViewOrderDetailsModel({this.status, this.data, this.statusCode});

  ViewOrderDetailsModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = json['data'] != null
        ? ViewOrderDetailsData.fromJson(json['data'])
        : null;
    statusCode = json['status_code'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['status_code'] = statusCode;
    return data;
  }
}

class ViewOrderDetailsData {
  int? id;
  String? orderNumber;
  Chef? chef;
  List<Items>? items;
  double? subtotal;
  double? deliveryFee;
  double? discount;
  num? serviceFeePercentage; // New field
  num? serviceFee; // New field
  num? walletCredits;
  num? taxPercentage; // New field
  num? taxesAndFees;
  double? total;
  String? deliveryDate;
  String? deliveryTime;
  Address? address;
  DeliveryTimes? deliveryTimes;
  DropOffOption? dropOffOption;
  String? dropOffInstructions;
  String? status;
  String? paymentStatus;
  String? createdAt;
  String? updatedAt;

  ViewOrderDetailsData({
    this.id,
    this.orderNumber,
    this.chef,
    this.items,
    this.subtotal,
    this.deliveryFee,
    this.discount,
    this.serviceFeePercentage,
    this.serviceFee,
    this.walletCredits,
    this.taxPercentage,
    this.taxesAndFees,
    this.total,
    this.deliveryDate,
    this.deliveryTime,
    this.address,
    this.deliveryTimes,
    this.dropOffOption,
    this.dropOffInstructions,
    this.status,
    this.paymentStatus,
    this.createdAt,
    this.updatedAt,
  });

  ViewOrderDetailsData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    orderNumber = json['order_number'];
    chef = json['chef'] != null ? Chef.fromJson(json['chef']) : null;
    if (json['items'] != null) {
      items = <Items>[];
      json['items'].forEach((v) {
        items!.add(Items.fromJson(v));
      });
    }
    subtotal = json['subtotal'] != null ? json['subtotal'].toDouble() : null;
    deliveryFee =
        json['delivery_fee'] != null ? json['delivery_fee'].toDouble() : null;
    discount = json['discount'] != null ? json['discount'].toDouble() : null;
    serviceFeePercentage = json['service_fee_percentage'] != null
        ? json['service_fee_percentage']
        : null;
    serviceFee =
        json['service_fee'] != null ? json['service_fee'].toDouble() : null;
    walletCredits = json['wallet_credits'] != null
        ? json['wallet_credits'].toDouble()
        : null;
    taxPercentage = json['tax_percentage'] != null
        ? json['tax_percentage'].toDouble()
        : null;
    taxesAndFees = json['taxes_and_fees'] != null
        ? json['taxes_and_fees'].toDouble()
        : null;
    total = json['total'] != null ? json['total'].toDouble() : null;
    deliveryDate = json['delivery_date'];
    deliveryTime = json['delivery_time'];
    address =
        json['address'] != null ? Address.fromJson(json['address']) : null;
    deliveryTimes = json['delivery_times'] != null
        ? DeliveryTimes.fromJson(json['delivery_times'])
        : null;
    dropOffOption = json['drop_off_option'] != null
        ? DropOffOption.fromJson(json['drop_off_option'])
        : null;
    dropOffInstructions = json['drop_off_instructions'];
    status = json['status'];
    paymentStatus = json['payment_status'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['order_number'] = orderNumber;
    if (chef != null) {
      data['chef'] = chef!.toJson();
    }
    if (items != null) {
      data['items'] = items!.map((v) => v.toJson()).toList();
    }
    data['subtotal'] = subtotal;
    data['delivery_fee'] = deliveryFee;
    data['discount'] = discount;
    data['service_fee_percentage'] = serviceFeePercentage;
    data['service_fee'] = serviceFee;
    data['wallet_credits'] = walletCredits;
    data['tax_percentage'] = taxPercentage;
    data['taxes_and_fees'] = taxesAndFees;
    data['total'] = total;
    data['delivery_date'] = deliveryDate;
    data['delivery_time'] = deliveryTime;
    if (address != null) {
      data['address'] = address!.toJson();
    }
    if (deliveryTimes != null) {
      data['delivery_times'] = deliveryTimes!.toJson();
    }
    if (dropOffOption != null) {
      data['drop_off_option'] = dropOffOption!.toJson();
    }
    data['drop_off_instructions'] = dropOffInstructions;
    data['status'] = status;
    data['payment_status'] = paymentStatus;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    return data;
  }
}

class Chef {
  int? id;
  String? name;
  String? photo;

  Chef({this.id, this.name, this.photo});

  Chef.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    photo = json['photo'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['photo'] = photo;
    return data;
  }
}

class Items {
  int? id;
  Dish? dish;
  ServingSize? servingSize;
  int? quantity;
  double? price;
  double? totalPrice;
  String? notes;

  Items({
    this.id,
    this.dish,
    this.servingSize,
    this.quantity,
    this.price,
    this.totalPrice,
    this.notes,
  });

  Items.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    dish = json['dish'] != null ? Dish.fromJson(json['dish']) : null;
    servingSize = json['serving_size'] != null
        ? ServingSize.fromJson(json['serving_size'])
        : null;
    quantity = json['quantity'];
    price = json['price'] != null ? json['price'].toDouble() : null;
    totalPrice =
        json['total_price'] != null ? json['total_price'].toDouble() : null;
    notes = json['notes'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    if (dish != null) {
      data['dish'] = dish!.toJson();
    }
    if (servingSize != null) {
      data['serving_size'] = servingSize!.toJson();
    }
    data['quantity'] = quantity;
    data['price'] = price;
    data['total_price'] = totalPrice;
    data['notes'] = notes;
    return data;
  }
}

class Dish {
  int? id;
  String? name;
  String? photo;

  Dish({this.id, this.name, this.photo});

  Dish.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    photo = json['photo'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['photo'] = photo;
    return data;
  }
}

class ServingSize {
  int? id;

  ServingSize({this.id});

  ServingSize.fromJson(Map<String, dynamic> json) {
    id = json['id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    return data;
  }
}

class Address {
  String? addressText;
  String? buildingType;
  String? houseNumber;
  String? landmark;

  Address({
    this.addressText,
    this.buildingType,
    this.houseNumber,
    this.landmark,
  });

  Address.fromJson(Map<String, dynamic> json) {
    addressText = json['address_text'];
    buildingType = json['building_type'];
    houseNumber = json['house_number'];
    landmark = json['landmark'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['address_text'] = addressText;
    data['building_type'] = buildingType;
    data['house_number'] = houseNumber;
    data['landmark'] = landmark;
    return data;
  }
}

class DeliveryTimes {
  int? id;
  String? name;
  String? description;
  String? cost;

  DeliveryTimes({this.id, this.name, this.description, this.cost});

  DeliveryTimes.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    description = json['description'];
    cost = json['cost'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['description'] = description;
    data['cost'] = cost;
    return data;
  }
}

class DropOffOption {
  int? id;
  String? name;

  DropOffOption({this.id, this.name});

  DropOffOption.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    return data;
  }
}
