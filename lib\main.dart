import 'package:db_eats/bloc/account_bloc.dart';
import 'package:db_eats/bloc/catering_bloc.dart';
import 'package:db_eats/bloc/chef_block.dart';
import 'package:db_eats/bloc/home_bloc.dart';
import 'package:db_eats/bloc/main_bloc.dart';
import 'package:db_eats/bloc/mealplan_bloc.dart';
import 'package:db_eats/bloc/newmealplan_bloc.dart';
import 'package:db_eats/bloc/order_bloc.dart';
import 'package:db_eats/bloc/promocode_blok.dart';
import 'package:db_eats/bloc/saversplan_bloc.dart';
import 'package:db_eats/bloc/support_bloc.dart';
import 'package:db_eats/data/local/meal_plan_db_helper.dart';
import 'package:db_eats/firebase_options.dart';
import 'package:db_eats/ui/splashscreen.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await clearOnFirstInstall();

  // Initialize database
  try {
    await MealPlanDbHelper.instance.database;
    print('Database initialized successfully');
  } catch (e) {
    print('Error initializing database: $e');
  }

  // Initialize Firebase
  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    print('Firebase initialized successfully');
  } catch (e) {
    print('Error initializing Firebase: $e');
  }
  OneSignal.Debug.setLogLevel(OSLogLevel.verbose);
  // Initialize OneSignal
  OneSignal.initialize(
      "************************************"); // Replace with your OneSignal App ID

  // Optional: request permission on iOS
  OneSignal.Notifications.requestPermission(true);
  OneSignal.User.pushSubscription.addObserver((state) {
    print('Player ID changed: ${state.current.id}');
  });

  // Optionally, print the player ID after a short delay
  Future.delayed(const Duration(seconds: 2), () {
    final playerId = OneSignal.User.pushSubscription.id;
    print('Player ID: $playerId');
  });
  OneSignal.Notifications.addClickListener((event) {
    print('Notification clicked: ${event.notification.jsonRepresentation()}');
  });

  OneSignal.Notifications.addForegroundWillDisplayListener((event) {
    print(
        'Notification received in foreground: ${event.notification.jsonRepresentation()}');
  });

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => MainBloc()),
        BlocProvider(create: (context) => AccountBloc()),
        BlocProvider(create: (context) => HomeBloc()),
        BlocProvider(create: (context) => MealplanBloc()),
        BlocProvider(create: (context) => OrderBloc()),
        BlocProvider(create: (context) => CateringBloc()),
        BlocProvider(create: (context) => SupportBloc()),
        BlocProvider(create: (context) => ChefBloc()),
        BlocProvider(create: (context) => PromocodeBloc()),
        BlocProvider(create: (context) => SaversplanBloc()),
        BlocProvider(
          create: (context) => NewmealplanBloc(),
        ),
      ],
      child: MaterialApp(
        navigatorKey: NavigationService.navigatorKey,
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          primaryColor: Colors.black,
          colorScheme: ColorScheme.light(
            primary: Colors.black,
            secondary: const Color(0xFFF6F3EC),
          ),
        ),
        home: const SplashScreen(),
      ),
    );
  }
}

class NavigationService {
  static GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
}

Future<void> clearOnFirstInstall() async {
  final prefs = await SharedPreferences.getInstance();
  final isFirstLaunch = prefs.getBool('first_launch_done') ?? false;

  if (!isFirstLaunch) {
    await prefs.clear(); // Clear persisted tokens, etc.
    await prefs.setBool('first_launch_done', true);
  }
}
