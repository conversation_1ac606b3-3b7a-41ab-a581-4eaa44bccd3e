class ListIssueMessagesModel {
  bool? status;
  String? message;
  int? statusCode;
  IssueMessageData? data;

  ListIssueMessagesModel({
    this.status,
    this.message,
    this.statusCode,
    this.data,
  });

  ListIssueMessagesModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    statusCode = json['status_code'];
    data =
        json['data'] != null ? IssueMessageData.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    data['status_code'] = statusCode;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class IssueMessageData {
  int? issueId;
  String? ticketNumber;
  int? categoryid;
  String? categoryName;
  String? status;
  List<Chat>? chat;
  Pagination? pagination;

  IssueMessageData({
    this.issueId,
    this.ticketNumber,
    this.categoryid,
    this.categoryName,
    this.status,
    this.chat,
    this.pagination,
  });

  IssueMessageData.fromJson(Map<String, dynamic> json) {
    issueId = json['issue_id'];
    ticketNumber = json['ticket_number'];
    categoryid = json['categoryid'];
    categoryName = json['category_name'];
    status = json['status'];
    if (json['chat'] != null) {
      chat = <Chat>[];
      json['chat'].forEach((v) {
        chat!.add(Chat.fromJson(v));
      });
    }
    pagination = json['pagination'] != null
        ? Pagination.fromJson(json['pagination'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['issue_id'] = issueId;
    data['ticket_number'] = ticketNumber;
    data['categoryid'] = categoryid;
    data['category_name'] = categoryName;
    data['status'] = status;
    if (chat != null) {
      data['chat'] = chat!.map((v) => v.toJson()).toList();
    }
    if (pagination != null) {
      data['pagination'] = pagination!.toJson();
    }
    return data;
  }

  bool get hasMore {
    if (pagination == null) return false;
    final total = pagination!.totalLength ?? 0;
    final current = (pagination!.page ?? 1) * (pagination!.limit ?? 50);
    return total > current;
  }

  int get currentPage => pagination?.page ?? 1;
}

class Chat {
  bool? isDescription;
  String? message;
  String? sentAt;
  String? senderName;
  String? senderRole;
  String? categoryName;
  String? status; // Added missing status field

  Chat({
    this.isDescription,
    this.message,
    this.sentAt,
    this.senderName,
    this.senderRole,
    this.categoryName,
    this.status,
  });

  Chat.fromJson(Map<String, dynamic> json) {
    isDescription = json['is_description'];
    message = json['message'];
    sentAt = json['sent_at'];
    senderName = json['sender_name'];
    senderRole = json['sender_role'];
    categoryName = json['category_name'];
    status = json['status']; // Added missing status field
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['is_description'] = isDescription;
    data['message'] = message;
    data['sent_at'] = sentAt;
    data['sender_name'] = senderName;
    data['sender_role'] = senderRole;
    data['category_name'] = categoryName;
    if (status != null) {
      data['status'] = status;
    }
    return data;
  }
}

class Pagination {
  int? totalLength;
  int? page;
  int? limit;

  Pagination({
    this.totalLength,
    this.page,
    this.limit,
  });

  Pagination.fromJson(Map<String, dynamic> json) {
    totalLength = json['totalLength'];
    page = json['page'];
    limit = json['limit'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['totalLength'] = totalLength;
    data['page'] = page;
    data['limit'] = limit;
    return data;
  }
}

// Extension methods for convenience
extension ChatExtensions on Chat {
  bool get isCustomerMessage => senderRole == 'CUSTOMER';
  bool get isAdminMessage => senderRole == 'ADMIN';
  bool get isRead => status == 'READ';
  bool get isSent => status == 'SENT';

  DateTime? get sentAtDateTime {
    if (sentAt == null) return null;
    try {
      return DateTime.parse(sentAt!);
    } catch (e) {
      return null;
    }
  }
}

extension IssueMessageDataExtensions on IssueMessageData {
  List<Chat> get customerMessages =>
      chat?.where((c) => c.isCustomerMessage).toList() ?? [];

  List<Chat> get adminMessages =>
      chat?.where((c) => c.isAdminMessage).toList() ?? [];

  Chat? get initialDescription =>
      chat?.firstWhere((c) => c.isDescription == true, orElse: () => Chat());
}
