import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:db_eats/bloc/support_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:shimmer/shimmer.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'dart:async';

class ChatScreen extends StatefulWidget {
  final int issueId;

  const ChatScreen({
    super.key,
    required this.issueId,
  });

  @override
  _ChatScreenState createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> with WidgetsBindingObserver {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  List<ChatMessage> messages = [];
  String issueTitle = 'Support Chat';
  String chatStartTime = '';
  String ticketNumber = '';

  Timer? _pollingTimer;
  bool _isInitialLoad = true; // Track if this is the first load
  bool _isPolling = false; // Track if we're currently polling
  bool _isLoadingMore = false;
  int _currentPage = 1;
  int _previousMessagesCount = 0; // Add this line

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    // Initial load with pagination
    context.read<SupportBloc>().add(ListIssueMessagesEvent(widget.issueId));
    // Start polling after initial load
    _startPollingAfterDelay();
  }

  void _startPollingAfterDelay() {
    // Start polling after 5 seconds to allow initial load
    Timer(Duration(seconds: 5), () {
      if (mounted) {
        _startPolling();
      }
    });
  }

  void _startPolling() {
    _pollingTimer?.cancel(); // Cancel existing timer if any
    _pollingTimer = Timer.periodic(Duration(seconds: 3), (timer) {
      if (mounted && !_isPolling) {
        _isPolling = true;
        _silentRefresh();
      }
    });
  }

  void _silentRefresh() async {
    try {
      context
          .read<SupportBloc>()
          .add(ListIssueMessagesEventSilent(widget.issueId));
    } finally {
      _isPolling = false;
    }
  }

  void _loadMoreMessages() {
    if (!_isLoadingMore) {
      setState(() => _isLoadingMore = true);
      context.read<SupportBloc>().add(ListIssueMessagesEvent(
            widget.issueId,
            page: _currentPage + 1,
            loadMore: true,
          ));
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.resumed:
        // Resume polling and do a silent refresh
        if (_pollingTimer == null || !_pollingTimer!.isActive) {
          _startPolling();
        }
        _silentRefresh();
        break;
      case AppLifecycleState.paused:
        _pollingTimer?.cancel();
        break;
      default:
        break;
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _pollingTimer?.cancel();
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  void _autoScrollIfNeeded() {
    if (_scrollController.hasClients) {
      final position = _scrollController.position;
      // Auto-scroll if user is within 100 pixels of bottom
      if (position.maxScrollExtent - position.pixels < 100) {
        WidgetsBinding.instance.addPostFrameCallback((_) => _scrollToBottom());
      }
    }
  }

  void _sendMessage() {
    final messageText = _messageController.text.trim();
    if (messageText.isNotEmpty) {
      // Add the message to local state immediately for better UX
      setState(() {
        messages.add(ChatMessage(
          text: messageText,
          isUser: true,
          timestamp: DateTime.now().toIso8601String(),
          senderName: '',
        ));
      });

      // Clear the input field
      _messageController.clear();

      // Scroll to bottom to show the new message
      WidgetsBinding.instance.addPostFrameCallback((_) => _scrollToBottom());

      // Send message via bloc
      context.read<SupportBloc>().add(
            SendIssueMessageEvent({
              "issue_id": widget.issueId,
              "message": messageText,
            }),
          );
    }
  }

  // void _handleMessagesUpdate(List<ChatMessage> newMessages) {
  //   final previousCount = messages.length;

  //   if (newMessages.length > previousCount) {
  //     // Check for new support messages
  //     final newSupportMessages =
  //         newMessages.skip(previousCount).where((msg) => !msg.isUser).toList();

  //     if (newSupportMessages.isNotEmpty && !_isInitialLoad) {
  //       // Show subtle notification for new messages
  //       _showNewMessageIndicator(newSupportMessages.length);
  //     }
  //   }

  //   setState(() {
  //     messages = newMessages;

  //     // Set issue details only on initial load
  //     if (_isInitialLoad) {
  //       issueTitle = Initializer.listIssueMessagesModel.data?.categoryName ??
  //           'Support Chat';
  //       ticketNumber =
  //           Initializer.listIssueMessagesModel.data?.ticketNumber ?? '';

  //       if (messages.isNotEmpty) {
  //         final firstMessage = messages.first;
  //         chatStartTime = _formatTimestamp(firstMessage.timestamp);
  //       }
  //       _isInitialLoad = false;
  //     }
  //   });

  //   // Smart auto-scroll
  //   _autoScrollIfNeeded();
  // }

  void _showNewMessageIndicator(int messageCount) {
    // Option 1: Subtle SnackBar
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
              ),
            ),
            SizedBox(width: 8),
            Text(
              messageCount > 1
                  ? '$messageCount new messages from support'
                  : 'New message from support',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
        duration: Duration(seconds: 2),
        backgroundColor: Color(0xFF0265DC).withOpacity(0.9),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        margin: EdgeInsets.only(
          bottom: 100, // Above the message input
          left: 16,
          right: 16,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<SupportBloc, SupportState>(
      listener: (context, state) {
        if (state is ListIssueMessagesSuccess) {
          final chatData = Initializer.listIssueMessagesModel.data;
          final newMessages = chatData?.chat
                  ?.map((chat) {
                    return ChatMessage(
                      text: chat.message ?? '',
                      isUser: chat.senderRole == 'CUSTOMER',
                      timestamp: chat.sentAt ?? '',
                      senderName: chat.senderRole == 'CUSTOMER'
                          ? ''
                          : (chat.senderName ?? 'Support'),
                    );
                  })
                  .toList()
                  ?.reversed
                  .toList() ??
              [];

          setState(() {
            if (state.loadMore) {
              messages = [...newMessages, ...messages];
              _isLoadingMore = false;
            } else {
              messages = newMessages;
              if (chatData?.chat?.isNotEmpty == true) {
                chatStartTime =
                    _formatTimestamp(chatData!.chat!.first.sentAt ?? '');
              }
              issueTitle = chatData?.categoryName ?? 'Support Chat';
              ticketNumber = chatData?.ticketNumber ?? '';
            }
            _currentPage = chatData?.currentPage ?? 1;
          });
        } else if (state is ListIssueMessagesSuccessSilent) {
          final chatData = Initializer.listIssueMessagesModel.data;
          final newMessages = chatData?.chat
                  ?.map((chat) {
                    return ChatMessage(
                      text: chat.message ?? '',
                      isUser: chat.senderRole == 'CUSTOMER',
                      timestamp: chat.sentAt ?? '',
                      senderName: chat.senderRole == 'CUSTOMER'
                          ? ''
                          : (chat.senderName ?? 'Support'),
                    );
                  })
                  .toList()
                  ?.reversed
                  .toList() ??
              [];

          setState(() {
            _previousMessagesCount = messages.length;
            messages = newMessages;
            // Update chat start time here too for consistency
            if (chatData?.chat?.isNotEmpty == true) {
              chatStartTime =
                  _formatTimestamp(chatData!.chat!.first.sentAt ?? '');
            }
          });

          if (messages.length > _previousMessagesCount) {
            _showNewMessageIndicator(messages.length - _previousMessagesCount);
          }
        }
      },
      builder: (context, state) {
        return Scaffold(
          backgroundColor: Colors.white,
          appBar: AppBar(
            backgroundColor: Colors.white,
            elevation: 0,
            scrolledUnderElevation: 0,
            shadowColor: Colors.transparent,
            leading: IconButton(
              icon: Icon(Icons.arrow_back, color: Colors.black),
              onPressed: () => Navigator.pop(context),
            ),
          ),
          body: Column(
            children: [
              // Header section below AppBar
              Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: Colors.white,
                ),
                child: (state is ListIssueMessagesLoading && _isInitialLoad)
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Shimmer.fromColors(
                            baseColor: Colors.grey[300]!,
                            highlightColor: Colors.grey[100]!,
                            child: Container(
                              height: 24,
                              width: 200,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ),
                          ),
                          SizedBox(height: 4),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              Shimmer.fromColors(
                                baseColor: Colors.grey[300]!,
                                highlightColor: Colors.grey[100]!,
                                child: Container(
                                  height: 16,
                                  width: 150,
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      )
                    : Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            issueTitle,
                            style: TextStyle(
                              color: Colors.black,
                              fontSize: 18,
                              fontFamily: 'Inter-Bold',fontWeight: FontWeight.w600
                            ),
                          ),
                          SizedBox(height: 4),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              Text(
                                chatStartTime.isNotEmpty
                                    ? 'Chat Started | $chatStartTime'
                                    : 'Chat Started',
                                style: TextStyle(
                                  color: Color(0xFF414346),
                                  fontSize: 12,fontWeight: FontWeight.w400,
                                  fontFamily: 'Inter',
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
              ),
              Expanded(
                child: (state is ListIssueMessagesLoading && _isInitialLoad)
                    ? _buildLoadingShimmer()
                    : messages.isEmpty
                        ? Center(
                            child: Text(
                              'No messages yet',
                              style: TextStyle(
                                color: Colors.grey[500],
                                fontSize: 16,
                                fontFamily: 'Inter',
                              ),
                            ),
                          )
                        : Stack(
                            children: [
                              ListView.builder(
                                controller: _scrollController,
                                padding: EdgeInsets.symmetric(
                                    horizontal: 16, vertical: 8),
                                reverse:
                                    true, // Add this to show newest messages at bottom
                                itemCount: messages.length +
                                    (Initializer.listIssueMessagesModel.data
                                                ?.hasMore ==
                                            true
                                        ? 1
                                        : 0),
                                itemBuilder: (context, index) {
                                  if (index == messages.length &&
                                      Initializer.listIssueMessagesModel.data
                                              ?.hasMore ==
                                          true) {
                                    return Center(
                                      child: TextButton.icon(
                                        onPressed: _loadMoreMessages,
                                        icon: Icon(Icons.arrow_upward),
                                        label: Text('Load previous messages'),
                                      ),
                                    );
                                  }
                                  return ChatBubble(message: messages[index]);
                                },
                              ),
                              if (_isLoadingMore)
                                Positioned(
                                  top: 0,
                                  left: 0,
                                  right: 0,
                                  child: Center(
                                    child: Container(
                                      padding: EdgeInsets.all(8),
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(16),
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.black12,
                                            blurRadius: 4,
                                          ),
                                        ],
                                      ),
                                      child: CircularProgressIndicator(),
                                    ),
                                  ),
                                ),
                            ],
                          ),
              ),
              _buildMessageInput(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildLoadingShimmer() {
    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemCount: 5,
      itemBuilder: (context, index) {
        return Padding(
          padding: EdgeInsets.only(bottom: 16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment:
                index.isEven ? MainAxisAlignment.end : MainAxisAlignment.start,
            children: [
              if (!index.isEven)
                Shimmer.fromColors(
                  baseColor: Colors.grey[300]!,
                  highlightColor: Colors.grey[100]!,
                  child: Container(
                    width: 32,
                    height: 32,
                    margin: EdgeInsets.only(right: 8),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
              Flexible(
                child: Shimmer.fromColors(
                  baseColor: Colors.grey[300]!,
                  highlightColor: Colors.grey[100]!,
                  child: Container(
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(18),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  String _formatTimestamp(String timestamp) {
    try {
      final dateTime = DateTime.parse(timestamp);
      final hour = dateTime.hour;
      final minute = dateTime.minute.toString().padLeft(2, '0');
      final period = hour >= 12 ? 'PM' : 'AM';
      final displayHour = hour == 0 ? 12 : (hour > 12 ? hour - 12 : hour);
      return '$displayHour:$minute $period';
    } catch (e) {
      return timestamp;
    }
  }

  Widget _buildMessageInput() {
    return BlocListener<SupportBloc, SupportState>(
      listener: (context, state) {
        if (state is SendIssueMessageSuccess) {
          // Use silent refresh instead of regular refresh
          context.read<SupportBloc>().add(ListIssueMessagesEventSilent(widget.issueId));
        } else if (state is SendIssueMessageFailed) {
          Fluttertoast.showToast(
            msg: state.message,
            backgroundColor: Colors.red,
            textColor: Colors.white,
          );
        }
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border(
            top: BorderSide(color: Colors.grey[200]!, width: 1),
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(8),
              child: Icon(
                Icons.attach_file,
                color: Color(0xFF1F2122),
                size: 20,
              ),
            ),
            Expanded(
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 0),
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _messageController,
                        decoration: InputDecoration(
                          hintText: 'Type in your message',
                          border: InputBorder.none,
                          hintStyle: TextStyle(
                            color: Colors.grey[500],
                            fontSize: 16,
                            fontFamily: 'Inter',
                          ),
                          contentPadding: EdgeInsets.zero,
                        ),
                        maxLines: null,
                        style: TextStyle(
                          fontSize: 16,
                          fontFamily: 'Inter',
                        ),
                        onSubmitted: (_) => _sendMessage(),
                      ),
                    ),
                    BlocBuilder<SupportBloc, SupportState>(
                      builder: (context, state) {
                        if (state is SendIssueMessageLoading) {
                          return SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Color(0xFF0265DC),
                              ),
                            ),
                          );
                        }
                        return IconButton(
                          icon: Icon(
                            Icons.send,
                            color: _messageController.text.trim().isNotEmpty
                                ? Color(0xFF0265DC)
                                : Color.fromARGB(255, 170, 172, 173),
                            size: 20,
                          ),
                          padding: EdgeInsets.zero,
                          constraints: BoxConstraints(),
                          onPressed: _sendMessage,
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Rest of your existing ChatBubble and ChatMessage classes remain the same...
class ChatBubble extends StatelessWidget {
  final ChatMessage message;

  const ChatBubble({Key? key, required this.message}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment:
            message.isUser ? CrossAxisAlignment.end : CrossAxisAlignment.start,
        children: [
          if (!message.isUser && message.senderName.isNotEmpty)
            Padding(
              padding: EdgeInsets.only(bottom: 4, left: 40),
              child: Text(
                message.senderName,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                  fontFamily: 'Inter',
                ),
              ),
            ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisAlignment: message.isUser
                ? MainAxisAlignment.end
                : MainAxisAlignment.start,
            children: [
              if (!message.isUser)
                Container(
                  width: 32,
                  height: 32,
                  margin: EdgeInsets.only(right: 8),
                  decoration: BoxDecoration(
                    color: Color(0xFFE1E3E6),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.support_agent,
                    size: 16,
                    color: Color(0xFF1F2122),
                  ),
                ),
              Flexible(
                child: Container(
                  constraints: BoxConstraints(
                    maxWidth: MediaQuery.of(context).size.width * 0.75,
                  ),
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    color:
                        message.isUser ? Color(0xFF0265DC) : Color(0xFFE1E3E6),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(18),
                      topRight: Radius.circular(18),
                      bottomLeft: message.isUser
                          ? Radius.circular(18)
                          : Radius.circular(4),
                      bottomRight: message.isUser
                          ? Radius.circular(4)
                          : Radius.circular(18),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 4,
                        offset: Offset(0, 1),
                      ),
                    ],
                  ),
                  child: Text(
                    message.text,
                    style: TextStyle(
                      color: message.isUser ? Colors.white : Color(0xFF1F2122),
                      fontSize: 14,
                      fontFamily: 'Inter',
                      height: 1.4,fontWeight: FontWeight.w400
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class ChatMessage {
  final String text;
  final bool isUser;
  final String timestamp;
  final String senderName;

  ChatMessage({
    required this.text,
    required this.isUser,
    required this.timestamp,
    this.senderName = '',
  });
}
