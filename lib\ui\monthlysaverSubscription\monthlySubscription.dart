import 'package:db_eats/ui/monthlysaverSubscription/subsribe.dart';
import 'package:flutter/material.dart';

class MonthlySubscriptionPage extends StatefulWidget {
  const MonthlySubscriptionPage({super.key});

  @override
  State<MonthlySubscriptionPage> createState() =>
      _MonthlySubscriptionPageState();
}

class _MonthlySubscriptionPageState extends State<MonthlySubscriptionPage> {
  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xfff6f3ec),
      appBar: AppBar(
        automaticallyImplyLeading: false,
        backgroundColor: const Color(0xfff6f3ec),
        elevation: 0,
        titleSpacing: 0,
        centerTitle: false,
        title: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            IconButton(
              icon: const Icon(Icons.arrow_back, color: Colors.black),
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
              onPressed: () => Navigator.pop(context),
            ),
          ],
        ),
      ),
      body: Padding(
        padding:  EdgeInsets.only(left: sixteen, right: sixteen),
        child: SingleChildScrollView(
          // <-- Add this
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
               SizedBox(height: ten/5),
               Text(
                'Monthly Saver Plan',
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w600,
                  fontSize: eighteen,
                  color: Color(0xff1F2122),
                ),
              ),
              ListView(
                  shrinkWrap: true,
                  padding: EdgeInsets.zero,
                  physics: const NeverScrollableScrollPhysics(),
                  children: [
                     SizedBox(height: sixteen),
                    // Your Plan Card
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(eighteen),
                      ),
                      padding:  EdgeInsets.all(eighteen),
                      child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                             Text(
                              'Your Plan',
                              style: TextStyle(
                                  fontFamily: "inter",
                                  fontWeight: FontWeight.w600,
                                  fontSize: twenty,
                                  color: Color(0xff1F2122)),
                            ),
                             SizedBox(height: forteen),
                            Column(
                              children: [
                                Container(
                                  decoration: BoxDecoration(
                                    color: const Color(0xFFF1F2F3),
                                    borderRadius: BorderRadius.circular(twelve),
                                  ),
                                  child: Column(
                                    children: [
                                      Padding(
                                        padding:  EdgeInsets.all(forteen),
                                        child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Row(
                                                children: [
                                                   Text(
                                                    'Regular',
                                                    style: TextStyle(
                                                        fontWeight:
                                                            FontWeight.w600,
                                                        fontSize: forteen,
                                                        color:
                                                            Color(0xff1F2122)),
                                                  ),
                                                   SizedBox(width: sixteen/2),
                                                  Container(
                                                    padding:  EdgeInsets
                                                        .symmetric(
                                                        horizontal: sixteen/2,
                                                        vertical: ten/5),
                                                    decoration: BoxDecoration(
                                                      color: const Color(
                                                          0xFFCEF8E0),
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              sixteen/2),
                                                    ),
                                                    child:  Text(
                                                      'Active',
                                                      style: TextStyle(
                                                        color:
                                                            Color(0xFF007A4D),
                                                        fontWeight:
                                                            FontWeight.w500,
                                                        fontSize: twelve,
                                                      ),
                                                    ),
                                                  ),
                                                  const Spacer(),
                                                   Text(
                                                    '\$45/year',
                                                    style: TextStyle(
                                                        fontWeight:
                                                            FontWeight.w600,
                                                        fontSize: forteen,
                                                        color:
                                                            Color(0xff414346)),
                                                  ),
                                                ],
                                              ),
                                               SizedBox(height: ten),
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.end,
                                                children: [
                                                   Text(
                                                    'Renewal on: Jun 03,2027',
                                                    textAlign: TextAlign.end,
                                                    style: TextStyle(
                                                      color: Color(0xff414346),
                                                      fontSize: twelve,
                                                      fontWeight:
                                                          FontWeight.w400,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              Divider(
                                                color: Color(0xffE1E3E6),
                                                thickness: 1,
                                                height: twentyFour,
                                              ),
                                               SizedBox(height: twelve/2),
                                               Text(
                                                'Exclusive offers',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.w400,
                                                  fontSize: forteen,
                                                  color: Color(0xff1F2122),
                                                ),
                                              ),
                                               SizedBox(height: twelve/2),
                                               Row(
                                                children: [
                                                  Icon(Icons.check,
                                                      size: forteen,
                                                      color: Color(0xff1F2122)),
                                                  SizedBox(width: twelve/2),
                                                  Expanded(
                                                    child: Text(
                                                      '20% Off Combo: Use code COM20',
                                                      style: TextStyle(
                                                          fontSize: twelve,
                                                          fontWeight:
                                                              FontWeight.w400,
                                                          color: Color(
                                                              0xff1F2122)),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                               SizedBox(height: sixteen/4),
                                               Row(
                                                children: [
                                                  Icon(Icons.check,
                                                      size: forteen,
                                                      color: Color(0xff1F2122)),
                                                  SizedBox(width: twelve/2),
                                                  Expanded(
                                                    child: Text(
                                                      '20% Off Desert: Use code DES20',
                                                      style: TextStyle(
                                                          fontSize: twelve,
                                                          fontWeight:
                                                              FontWeight.w400,
                                                          color: Color(
                                                              0xff1F2122)),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ]),
                                      ),
                                      ClipRRect(
                                        borderRadius:  BorderRadius.only(
                                          bottomLeft: Radius.circular(twelve),
                                          bottomRight: Radius.circular(twelve),
                                        ),
                                        child: Container(
                                          color: const Color(0xFFE1E3E6),
                                          padding:  EdgeInsets.symmetric(
                                              vertical: ten),
                                          child: Row(
                                            children: [
                                               SizedBox(width: twelve),
                                               Icon(
                                                  Icons.celebration_outlined,
                                                  size: eighteen,
                                                  color: Color(0xff1F2122)),
                                               SizedBox(width: sixteen/2),
                                              RichText(
                                                text:  TextSpan(
                                                  style: TextStyle(
                                                    color: Color(0xff414346),
                                                    fontSize: forteen,
                                                    fontWeight: FontWeight.w400,
                                                  ),
                                                  children: [
                                                    TextSpan(
                                                        text:
                                                            'Delivery Fee Saved: ',
                                                        style: TextStyle(
                                                            fontWeight:
                                                                FontWeight.w400,
                                                            fontSize: twelve,
                                                            color: Color(
                                                                0xff414346))),
                                                    TextSpan(
                                                      text: '\$240',
                                                      style: TextStyle(
                                                        fontWeight:
                                                            FontWeight.w500,
                                                        fontSize: twelve,
                                                        color:
                                                            Color(0xff1F2122),
                                                      ),
                                                    )
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ]),
                    ),
                  ]),
              SizedBox(
                height: twenty,
              ),
              // Other Plans
               Text(
                'Other Plans',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: twenty,
                  color: Color(0xff1F2122),
                ),
              ),
               SizedBox(height: forteen),
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(eighteen),
                ),
                child: Padding(
                  padding:  EdgeInsets.all(eighteen),
                  child: Column(
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          color: const Color(0xFFF1F2F3),
                          borderRadius: BorderRadius.circular(twelve),
                        ),
                        child: Padding(
                          padding:  EdgeInsets.all(forteen),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                   Text(
                                    'Student Saver',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      fontSize: forteen,
                                      color: Color(0xff1F2122),
                                    ),
                                  ),
                                  const Spacer(),
                                   Text(
                                    '\$45/year',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      fontSize: forteen,
                                      color: Color(0xff414346),
                                    ),
                                  ),
                                ],
                              ),
                              Divider(
                                color: const Color(0xffE1E3E6),
                                thickness: 1,
                                height: twentyFour,
                              ),
                               Text(
                                'Exclusive offers',
                                style: TextStyle(
                                  fontWeight: FontWeight.w400,
                                  fontSize: forteen,
                                  color: Color(0xff1F2122),
                                ),
                              ),
                               SizedBox(height: twelve/2),
                               Row(
                                children: [
                                  Icon(Icons.check,
                                      size: forteen, color: Color(0xff1F2122)),
                                  SizedBox(width: twelve/2),
                                  Expanded(
                                    child: Text(
                                      '20% Off Combo: Use code COM20',
                                      style: TextStyle(
                                          fontSize: twelve,
                                          fontWeight: FontWeight.w400,
                                          color: Color(0xff1F2122)),
                                    ),
                                  ),
                                ],
                              ),
                               SizedBox(height: sixteen/4),
                               Row(
                                children: [
                                  Icon(Icons.check,
                                      size: forteen, color: Color(0xff1F2122)),
                                  SizedBox(width: twelve/2),
                                  Expanded(
                                    child: Text(
                                      '20% Off Desert: Use code DES20',
                                      style: TextStyle(
                                          fontSize: twelve,
                                          fontWeight: FontWeight.w400,
                                          color: Color(0xff1F2122)),
                                    ),
                                  ),
                                ],
                              ),
                               SizedBox(height: ten),
                              Align(
                                alignment: Alignment.centerRight,
                                child: TextButton(
                                  onPressed: () {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) =>
                                            const VerifyStudentStatusPage(),
                                      ),
                                    );
                                  },
                                  style: TextButton.styleFrom(
                                    padding: EdgeInsets.zero,
                                    minimumSize: const Size(0, 0),
                                    tapTargetSize:
                                        MaterialTapTargetSize.shrinkWrap,
                                  ),
                                  child: IntrinsicWidth(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'Verify As Student',
                                          style: TextStyle(
                                            color: Color(0xff1F2122),
                                            fontWeight: FontWeight.w400,
                                            fontSize: twelve,
                                          ),
                                        ),
                                        SizedBox(height: ten/5),
                                        Container(
                                          height: 1,
                                          width: double
                                              .infinity, // underline matches text width
                                          color: Color(0xff1F2122),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                               SizedBox(height: sixteen/2),
                            ],
                          ),
                        ),
                      ),
                      Column(
                        children: [
                          SizedBox(
                            height: twenty,
                          ),
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton(
                              onPressed: () {},
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.black,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(ten*3),
                                ),
                                padding:
                                     EdgeInsets.symmetric(vertical: sixteen),
                                elevation: 0,
                              ),
                              child:  Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    'Subscribe',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w400,
                                      fontSize: twelve,
                                    ),
                                  ),
                                  SizedBox(width: sixteen/2),
                                  Icon(Icons.arrow_forward,
                                      color: Colors.white, size: sixteen),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
               SizedBox(height: ten*3),
            ],
          ),
        ),
      ),
    );
  }
}
