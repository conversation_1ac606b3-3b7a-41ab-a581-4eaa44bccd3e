import 'dart:developer';

import 'package:db_eats/bloc/main_bloc.dart';
import 'package:db_eats/ui/auth/completesignup.dart';
import 'package:db_eats/ui/mainnavigationpage.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

class Signup extends StatefulWidget {
  const Signup({super.key});

  @override
  State<Signup> createState() => _SignupState();
}

class _SignupState extends State<Signup> {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn();

  User? _user;
  bool _emailError = false;
  final TextEditingController _emailController = TextEditingController();

  double getResponsiveSize(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width < 360) return 12;
    if (width < 600) return 14;
    if (width < 900) return 16;
    return 18;
  }

  Future<void> _signInWithGoogle(BuildContext context) async {
    try {
      await _googleSignIn.signOut();
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        // The user canceled the sign-in
        return;
      }
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      final AuthCredential credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );
      log(" Token: ${credential.token}");
      credential.token;
      log("Access Token: ${googleAuth.accessToken}");
      log("ID Token: ${googleAuth.idToken}");

      final UserCredential userCredential =
          await _auth.signInWithCredential(credential);
      final displayName = userCredential.user?.displayName ?? '';
      final names = displayName.split(' ');
      final firstName = names.isNotEmpty ? names.first : '';
      final lastName = names.length > 1 ? names.sublist(1).join(' ') : '';
      log("First Name: $firstName");
      log("Last Name: $lastName");
      // It's possible that the UserCredential object doesn't have the tokens directly
      // You need to get the idToken from the user object instead
      final User? user = userCredential.user;
      if (!mounted) return;

      if (user != null) {
        final idToken = await user.getIdToken();
        log("ID Token from User: $idToken");
        context.read<MainBloc>().add(SocialSignInEvent(
              signintype: 'GOOGLE',
              firstName: firstName,
              lastName: lastName,
              idToken: idToken.toString(),
            ));
      }
    } catch (e) {
      log('Google sign-in error: $e');
    }
  }

  Future<UserCredential?> signInWithApple(BuildContext context) async {
    try {
      final appleCredential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      final oauthCredential = OAuthProvider("apple.com").credential(
        idToken: appleCredential.identityToken,
        accessToken: appleCredential.authorizationCode,
      );

      final UserCredential userCredential =
          await _auth.signInWithCredential(oauthCredential);
      final User? user = userCredential.user;
      if (user != null) {
        final idToken = await user.getIdToken();
        // log("ID Token from User: $idToken");

        context.read<MainBloc>().add(SocialSignInEvent(
              signintype: 'APPLE',
              firstName: user.displayName?.split(' ').first ?? '',
              lastName: '',
              idToken: idToken.toString(),
            ));
      }

      return userCredential;
    } catch (e) {
      log('Apple sign-in error: $e');
      return null;
    }
  }

  Future<UserCredential?> signInWithAppleAndroid(BuildContext context) async {
    await FirebaseAuth.instance.signOut();

    AppleAuthProvider appleProvider = AppleAuthProvider();
    appleProvider = appleProvider.addScope('email');
    appleProvider = appleProvider.addScope('name');

    try {
      final UserCredential userCredential =
          await FirebaseAuth.instance.signInWithProvider(appleProvider);
      final User? user = userCredential.user;

      if (!mounted) return null;

      // Extract first and last name from displayName if available
      String firstName = '';
      String lastName = '';
      if (user?.displayName != null) {
        final names = user!.displayName!.split(' ');
        firstName = names.isNotEmpty ? names.first : '';
        lastName = names.length > 1 ? names.sublist(1).join(' ') : '';
      }

      if (user != null) {
        final idToken = await user.getIdToken();
        context.read<MainBloc>().add(GoogleSignInEvent(
              signintype: 'APPLE',
              firstName: firstName,
              lastName: lastName,
              idToken: idToken.toString(),
            ));
        return userCredential;
      }
      return null;
    } catch (e) {
      log('Apple sign-in error: $e');
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isLandscape = size.width > size.height;
    final baseTextSize = getResponsiveSize(context);

    return BlocListener<MainBloc, MainState>(
      listener: (context, state) {
        if (state is CheckingEmailSuccess) {
          // Email already registered
          setState(() {
            _emailError = true;
          });
        } else if (state is CheckingEmailFailed) {
          // Email not registered, proceed with signup
          String email = _emailController.text;
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => CompleteSignup(email: email),
            ),
          );
        }
        if (state is SocialSignUpSuccess) {
          // Navigate to main navigation screen after successful social sign up
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(
                builder: (context) => const MainNavigationScreen()),
            (route) => false,
          );
        }
        if (state is SocialSignUpFailed) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                state.message ?? 'Social sign up failed. Please try again.',
              ),
              backgroundColor: const Color.fromARGB(255, 49, 49, 49),
            ),
          );
        }
        // if (state is SignUpSuccess) {
        //   Navigator.of(context).pushAndRemoveUntil(
        //     MaterialPageRoute(
        //         builder: (context) => const MainNavigationScreen()),
        //     (route) => false,
        //   );
        // }
        if (state is SignUpFailed) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                state.message ?? 'Sign up failed. Please try again.',
              ),
              backgroundColor: const Color.fromARGB(255, 49, 49, 49),
            ),
          );
        }
      },
      child: Stack(
        children: [
          Scaffold(
            backgroundColor: const Color(0xFFF6F3EC),
            body: SingleChildScrollView(
              child: SafeArea(
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: size.width * 0.05,
                    vertical: size.height * 0.02,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: size.width * 0.1,
                        height: size.width * 0.1,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.05),
                              blurRadius: 2,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                        child: IconButton(
                          icon: Icon(
                            Icons.close,
                            size: baseTextSize * 1.2,
                            color: Colors.black,
                          ),
                          onPressed: () => Navigator.pop(context),
                          padding: EdgeInsets.all(size.width * 0.02),
                          constraints: const BoxConstraints(),
                        ),
                      ),

                      SizedBox(height: size.height * 0.08),

                      // Sign up text
                      Text(
                        'Sign up',
                        style: TextStyle(
                          fontSize: isLandscape
                              ? size.height * 0.04
                              : baseTextSize * 1.7, // gives 23.8
                          //baseTextSize * 1.5,
                          fontWeight: FontWeight.w400,
                          fontFamily: 'Inter',
                          color: Colors.black,
                        ),
                      ),

                      SizedBox(height: size.height * 0.02),

                      // Email label
                      Text(
                        'Email Address',
                        style: TextStyle(
                          fontSize: baseTextSize,
                          fontWeight: FontWeight.w500,
                          fontFamily: 'Inter',
                          color: Color(0xFF1F2122),
                        ),
                      ),

                      SizedBox(height: size.height * 0.015),

                      TextField(
                        controller: _emailController,
                        onChanged: (value) {
                          if (_emailError) {
                            setState(() {
                              _emailError = false;
                            });
                          }
                        },
                        decoration: InputDecoration(
                          hintText: 'Enter your email',
                          hintStyle: TextStyle(
                            fontSize: baseTextSize,
                            fontWeight: FontWeight.w400,
                            fontFamily: 'Inter',
                            color: Color(0xFF66696D),
                          ),
                          filled: true,
                          fillColor: Colors.white,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(25),
                            borderSide: BorderSide.none,
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(25),
                            borderSide: _emailError
                                ? const BorderSide(color: Colors.red)
                                : const BorderSide(color: Color(0xFFE1E3E6)),
                          ),
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: size.width * 0.05,
                            vertical: size.height * 0.02,
                          ),
                          isDense: true,
                        ),
                        keyboardType: TextInputType.emailAddress,
                        style: TextStyle(
                          fontSize: baseTextSize,
                          height: 1.0,
                        ),
                      ),

                      if (_emailError) ...[
                        Container(
                          margin: EdgeInsets.only(top: size.height * 0.015),
                          padding: EdgeInsets.symmetric(
                            horizontal: size.width * 0.03,
                            vertical: size.height * 0.01,
                          ),
                          decoration: BoxDecoration(
                            color: const Color(0xFFFFEBE7),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.error_outline,
                                  color: Colors.red, size: baseTextSize * 1.2),
                              SizedBox(width: size.width * 0.02),
                              Expanded(
                                child: Text(
                                  'Email already registered. Please try logging in.',
                                  style: TextStyle(
                                    fontSize: baseTextSize * 0.9,
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Inter',
                                    color: Colors.black,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],

                      SizedBox(height: size.height * 0.025),
                      SizedBox(
                        width: double.infinity,
                        height: isLandscape
                            ? size.height * 0.08
                            : size.height * 0.06,
                        child: ElevatedButton(
                          onPressed: () {
                            String email = _emailController.text;
                            if (email.isEmpty || !email.contains('@')) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text(
                                      'Please enter a valid email address.'),
                                ),
                              );
                            } else {
                              context.read<MainBloc>().add(CheckEmailEvent(
                                  email: email, issignin: false));
                            }
                          },
                               style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.black,
                                  foregroundColor: Colors.white,
                                  padding: EdgeInsets.symmetric(
                                      vertical: ResponsiveSizes.hp(context, 1)),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(
                                        ResponsiveSizes.hp(context, 25)),
                                  ),
                                ),
                          child: FittedBox(
                            fit: BoxFit.scaleDown,
                            child: Text(
                              'Continue',
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                fontSize: ResponsiveSizes.wp(context, 4),
                                fontFamily: 'Inter',
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                      ),

                      Padding(
                        padding:
                            EdgeInsets.symmetric(vertical: size.height * 0.025),
                        child: Row(
                          children: [
                            Expanded(
                                child: Divider(
                                    color: Color(0xFFD2D4D7), thickness: 1)),
                            Padding(
                              padding: EdgeInsets.symmetric(
                                  horizontal: size.width * 0.04),
                              child: Text(
                                'or',
                                style: TextStyle(
                                  color: Color(0xFF414346),
                                  fontSize: baseTextSize *
                                      1.15 // = 16.1 (very close to 16)
                                  ,
                                  fontFamily: 'Inter',
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ),
                            Expanded(
                                child: Divider(
                                    color: Color(0xFFD2D4D7), thickness: 1)),
                          ],
                        ),
                      ),

                      _buildSocialButton(
                        context: context,
                        icon: 'assets/icons/apple.png',
                        label: 'Continue With Apple',
                        baseTextSize: baseTextSize,
                        size: size,
                        isLandscape: isLandscape,
                      ),

                      SizedBox(height: size.height * 0.02),

                      _buildSocialButton(
                        context: context,
                        icon: 'assets/icons/google.png',
                        label: 'Continue With Google',
                        baseTextSize: baseTextSize,
                        size: size,
                        isLandscape: isLandscape,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          BlocBuilder<MainBloc, MainState>(
            builder: (context, state) {
              if (state is SigningUp) {
                return Container(
                  color: Colors.black.withOpacity(0.8),
                  child: const Center(
                    child: SizedBox(
                      width: 40,
                      height: 40,
                      child: CupertinoActivityIndicator(),
                    ),
                  ),
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSocialButton({
    required BuildContext context,
    required String icon,
    required String label,
    required double baseTextSize,
    required Size size,
    required bool isLandscape,
  }) {
    return SizedBox(
      width: double.infinity,
      height: isLandscape ? size.height * 0.08 : size.height * 0.06,
      child: OutlinedButton.icon(
        icon: Image.asset(
          icon,
          width: baseTextSize,
          height: baseTextSize,
        ),
        label: Text(
          label,
          style: TextStyle(
            color: Colors.black,
            fontSize: baseTextSize * 1.15 // = 16.1 (very close to 16)
            ,
            fontFamily: 'Inter',
            fontWeight: FontWeight.w600,
          ),
        ),
        onPressed: () {
          if (label == "Continue With Apple") {
            if (Theme.of(context).platform == TargetPlatform.iOS) {
              signInWithApple(context);
            } else if (Theme.of(context).platform == TargetPlatform.android) {
              signInWithAppleAndroid(context);
            }
          } else {
            _signInWithGoogle(context);
          }

          // Social sign in functionality
        },
        style: OutlinedButton.styleFrom(
          side: const BorderSide(color: Color(0xFF1F2122), width: 1.0),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(size.width * 0.06),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }
}

// Add ResponsiveSizes utility class if not already imported
class ResponsiveSizes {
  static double wp(BuildContext context, double percentage) =>
      MediaQuery.of(context).size.width * (percentage / 100);

  static double hp(BuildContext context, double percentage) =>
      MediaQuery.of(context).size.height * (percentage / 100);
}
