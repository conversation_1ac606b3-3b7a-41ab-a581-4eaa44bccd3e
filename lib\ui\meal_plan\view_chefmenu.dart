import 'package:db_eats/bloc/mealplan_bloc.dart';
import 'package:db_eats/data/models/chef/viewchefdetailsmodel.dart';
import 'package:db_eats/data/models/meal_plan/filtereddishmodel.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/ui/catering/dishdetail.dart';
import 'package:db_eats/ui/meal_plan/meal_plan.dart';
import 'package:db_eats/ui/meal_plan/checkout_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:db_eats/bloc/home_bloc.dart';
import 'package:shimmer/shimmer.dart';

class ViewChefmenu extends StatefulWidget {
  final int id;
  final int selectedDay;
  final String deliveryTime;
  final String selectedDate;
  final int mealPlanId;
  final int mealPlanDuration; // Add this field
  final bool isEditing;
  final int? dayId;
  final dynamic viewDayData;
  final String? chefLocation; // Add chef location parameter

  const ViewChefmenu({
    super.key,
    required this.id,
    required this.selectedDay,
    required this.deliveryTime,
    required this.selectedDate,
    required this.mealPlanId,
    required this.mealPlanDuration, // Add this parameter
    this.isEditing = false,
    this.dayId,
    this.viewDayData,
    this.chefLocation, // Add chef location parameter
  });

  @override
  State<ViewChefmenu> createState() => _ViewChefmenuState();
}

class _ViewChefmenuState extends State<ViewChefmenu>
    with TickerProviderStateMixin {
  // Change to TickerProviderStateMixin
  ChefDetailsModel? chefDetails;
  FilteredDishesModel? dishesData;
  TabController? _tabController; // Make nullable
  List<String> _categories = [];
  String _deliveryTime = '9:00AM-10:00AM';
  String _startDate = DateTime.now().toString();
  List<String> _nextDatesFromStartDate = []; // Keep this field
  bool _isLoading = false;
  bool _isDishesLoading = true; // Track dishes loading state
  int _selectedDate = 0;
  int _currentDay = 0;
  int _mealPlanDuration = 5; // Add this field

  @override
  void initState() {
    super.initState();
    _startDate = widget.selectedDate;
    _deliveryTime = widget.deliveryTime;
    _selectedDate = widget.selectedDay;
    _currentDay = widget.selectedDay;
    // Remove TabController initialization here
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<HomeBloc>().add(ViewChefDetailsEvent(chefId: widget.id));
    });
  }

  @override
  void dispose() {
    _tabController?.dispose(); // Add disposal
    super.dispose();
  }

  int _selectedIndex = 0;

  final List<Widget> _pages = [
    const Center(child: Text('Home Page')),
    const Center(child: Text('Orders Page')),
    const Center(child: Text('Catering Page')),
    const Center(child: Text('Messages Page')),
    const Center(child: Text('Account Page')),
  ];

  void _onNavItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  void _submit() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => DishDetailPage(dishId: "1"),
      ),
    );
  }

  void _navigateBack() {
    Navigator.pop(context);
  }

  void _selectChefForDay() {
    if (chefDetails?.data?.chef == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Chef details not available'),
          backgroundColor: Color(0xFFE11900),
        ),
      );
      return;
    }

    Map<String, dynamic> chefData = {
      'chef_id': chefDetails?.data?.chef?.chefId ?? 0,
      'name':
          '${chefDetails?.data?.chef?.chef?.firstName ?? ''} ${chefDetails?.data?.chef?.chef?.lastName ?? ''}'
              .trim(),
      'image': chefDetails?.data?.chef?.profilePhoto ?? '',
      'tags': chefDetails?.data?.chef?.searchTags ?? [],
      'date': widget.selectedDate,
     'rating': (() {
  final rating = chefDetails?.data?.chef?.ratingPercentage;
  final totalRatings = chefDetails?.data?.chef?.totalRatings ?? 0;
  
  if (rating == null) return '0% (0)';
  
  final ratingStr = rating % 1 == 0 ? rating.toInt().toString() : rating.toString();
  return '$ratingStr% ($totalRatings)';
})(),
    //  chefDetails?.data?.chef?.ratingPercentage != null
    // ? '${chefDetails?.data?.chef?.ratingPercentage!.toStringAsFixed(0)}% (${chefDetails?.data?.chef?.totalRatings.toString() ?? 0})'
    // : '0% (0)',
     // 'rating': chefDetails?.data?.chef?.ratingPercentage ?? '0',
    };

    if (widget.isEditing) {
      // In edit mode, call AddEditMealPlanEvent to update the chef
      // Extract just the date part (remove day name if present)
      String dateOnly = widget.selectedDate.split(',')[0].trim();

      Map<String, dynamic> requestData = {
        'mealPlanId': widget.mealPlanId,
        'chef_id': chefDetails?.data?.chef?.chefId ?? 0,
        'date': dateOnly, // Send only the date part
      };

      // Add day_id when in edit mode
      if (widget.dayId != null) {
        requestData["day_id"] = widget.dayId!;
      }
      context.read<MealplanBloc>().add(AddEditMealPlanEvent(requestData));
    } else if (widget.selectedDay == widget.mealPlanDuration) {
      final previousSelections = ModalRoute.of(context)?.settings.arguments
              as List<Map<String, dynamic>>? ??
          [];
      previousSelections.add(chefData);
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => MealPlan(
            selectedChefsWithDetails: previousSelections,
            mealPlanId: widget.mealPlanId,
          ),
        ),
      );
    } else {
      Navigator.pop(context, chefData);
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isTablet = size.width >= 600;
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;

    return MultiBlocListener(
      listeners: [
        BlocListener<HomeBloc, HomeState>(
          listener: (context, state) {
            if (state is ChefDetailsSuccess) {
              setState(() {
                chefDetails = state.data;
              });

              // After getting chef details, call MealPlanProgressEvent
              context
                  .read<MealplanBloc>()
                  .add(MealPlanProgressEvent(widget.mealPlanId));
            }
            if (state is DishesListSuccess) {
              setState(() {
                dishesData = state.data as FilteredDishesModel?;
                _categories = dishesData?.data?.categoryBasedList
                        ?.map((cat) => cat.category?.name ?? '')
                        .toList() ??
                    [];
                _tabController =
                    TabController(length: _categories.length, vsync: this);
              });
            }
          },
        ),
        BlocListener<MealplanBloc, MealPlanState>(
          listener: (context, state) {
            if (state is MealPlanProgressSuccess) {
              final progressData = state.data; // Remove 'as progress.Data'
              // First update the state with progress data
              final List<String> nextDates =
                  _generateNextDates(progressData.startDate ?? '');
              setState(() {
                _isLoading = false;
                // Always update the start date for generating the date sequence
                // but preserve the display date for edit mode
                final mealPlanStartDate = progressData.startDate ?? '';
                if (!widget.isEditing) {
                  _startDate = mealPlanStartDate;
                } else {
                  // In edit mode, use meal plan start date for generating dates
                  // but keep the selected date for display
                  _startDate = mealPlanStartDate;
                }
                _mealPlanDuration =
                    progressData.mealPlanDuration ?? widget.mealPlanDuration;
                _nextDatesFromStartDate = nextDates;
                if (progressData.timeSlot != null) {
                  _deliveryTime =
                      '${_formatTimeToAmPm(progressData.timeSlot?.startTime)}-${_formatTimeToAmPm(progressData.timeSlot?.endTime)}';
                }
              });

              // Set loading state and call ListFilterdDishes
              setState(() {
                _isDishesLoading = true;
              });

              // Build request data conditionally to avoid sending empty strings for numeric fields
              Map<String, dynamic> requestData = {
                'chef_id': widget.id.toString(),
                'serving_size_id': progressData.servingSizeId,
                'cuisine_ids':
                    progressData.cuisines?.map((c) => c.id).toList() ?? [],
                'sub_cuisine_ids':
                    progressData.subcuisines?.map((c) => c.id).toList() ?? [],
                'local_cuisine_ids':
                    progressData.localcuisines?.map((c) => c.id).toList() ?? [],
                'time_slot_id': progressData.timeSlotId?.toString() ?? '',
                'date': _nextDatesFromStartDate.isNotEmpty
                    ? _nextDatesFromStartDate[_currentDay - 1]
                    : progressData.startDate ?? ''
              };

              // Only add dietary_id if it has a valid value
              if (progressData.dietaryPreferenceId != null) {
                requestData['dietary_id'] =
                    progressData.dietaryPreferenceId.toString();
              }

              // Only add spice_level_id if it has a valid value
              if (progressData.spiceLevelId != null) {
                requestData['spice_level_id'] =
                    progressData.spiceLevelId.toString();
              }

              context.read<MealplanBloc>().add(ListFilterdDishes(requestData));
            } else if (state is ListFilterdDishesLoading) {
              setState(() {
                _isDishesLoading = true;
              });
            } else if (state is ListFilterdDishesSuccess) {
              final filteredDishesData = state.data as FilteredDishesModel;
              setState(() {
                _isDishesLoading = false; // Set loading to false
                _categories = filteredDishesData.data?.categoryBasedList
                        ?.map((cat) => cat.category?.name ?? '')
                        .where((name) => name.isNotEmpty)
                        .toList() ??
                    [];

                dishesData = filteredDishesData;

                // Dispose old controller if exists
                _tabController?.dispose();
                // Create new controller only if we have categories
                if (_categories.isNotEmpty) {
                  _tabController = TabController(
                    length: _categories.length,
                    vsync: this,
                  );
                }
              });
            } else if (state is ListFilterdDishesFailed) {
              setState(() {
                _isDishesLoading =
                    false; // Set loading to false even on failure
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message ?? 'Failed to load dishes'),
                  backgroundColor: const Color(0xFFE11900),
                ),
              );
            } else if (state is AddEditMealPlanSuccess) {
              // Handle successful chef update in edit mode
              if (widget.isEditing) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Chef updated successfully'),
                    backgroundColor: Colors.green,
                  ),
                );
                // Navigate back to checkout page
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(
                    builder: (context) => CheckoutPage(
                      mealPlanId: widget.mealPlanId,
                    ),
                  ),
                );
              }
            } else if (state is AddEditMealPlanFailed) {
              // Handle failed chef update in edit mode
              if (widget.isEditing) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.message ?? 'Failed to update chef'),
                    backgroundColor: const Color(0xFFE11900),
                  ),
                );
              }
            }
          },
        ),
      ],
      child: BlocBuilder<MealplanBloc, MealPlanState>(
        builder: (context, state) {
          final isLoading = _isDishesLoading || dishesData == null;
          return SafeArea(
            child: Scaffold(
              backgroundColor: const Color(0xFFF6F3EC),
              appBar: AppBar(
                backgroundColor: const Color(0xFFF6F3EC),
                centerTitle: false,
                scrolledUnderElevation: 0,
                titleSpacing: 0,
                automaticallyImplyLeading: false,
                leading: IconButton(
                  icon: Image.asset(
                    'assets/icons/left_arrow.png',
                    width: size.width * 0.03,
                    height: size.width * 0.03,
                  ),
                  onPressed: _navigateBack,
                ),
                elevation: 0,
              ),
              body: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: size.width * 0.04,
                        //vertical: size.height * 0.01
                      ),
                      child: Text(
                        "Select Chef",
                        style: TextStyle(
                          fontSize: isTablet ? size.width * 0.05 : eighteen,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'Inter',
                          color: const Color(0xFF1F2122),
                        ),
                      ),
                    ),
                    SizedBox(height: size.height * 0.015),
                    Padding(
                      padding:
                          EdgeInsets.symmetric(horizontal: size.width * 0.04),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Date row with number selector
                          Row(
                            children: [
                              Image.asset(
                                'assets/icons/date_range.png',
                                width: twelve,
                                height: twelve,
                                color: const Color(0xFF1F2222),
                              ),
                              SizedBox(width: size.width * 0.02),
                              Text(
                                _isLoading
                                    ? "Loading..."
                                    : widget.isEditing
                                        ? _formatDate(
                                            widget.selectedDate ?? _startDate)
                                        : (_currentDay > 0 &&
                                                _nextDatesFromStartDate
                                                    .isNotEmpty)
                                            ? _formatDate(
                                                _nextDatesFromStartDate[
                                                    _currentDay - 1])
                                            : _formatDate(_startDate),
                                style: TextStyle(
                                  fontSize: twelve,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: 'Inter',
                                  color: const Color(0xFF414346),
                                ),
                              ),
                              const Spacer(),
                              _buildDateSelector(
                                size,
                              ),
                            ],
                          ),
                          SizedBox(height: size.height * 0.01),
                          Row(
                            children: [
                              Icon(Icons.access_time,
                                  size: twelve, color: const Color(0xFF1F2222)),
                              SizedBox(width: size.width * 0.02),
                              Text(
                                _isLoading ? "Loading..." : _deliveryTime,
                                style: TextStyle(
                                  fontSize: twelve,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: 'Inter',
                                  color: const Color(0xFF414346),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: size.height * 0.03),
                    Padding(
                      padding:
                          EdgeInsets.symmetric(horizontal: size.width * 0.04),
                      child: Row(
                        children: [
                          CircleAvatar(
                            radius: twelve,
                            backgroundImage: NetworkImage(
                              ServerHelper.imageUrl +
                                  (chefDetails?.data?.chef?.profilePhoto ?? ''),
                            ),
                            backgroundColor: Colors.grey[200],
                            onBackgroundImageError: (_, __) => Container(),
                          ),
                          SizedBox(
                            width: twelve,
                          ),
                          Expanded(
                            child: Text(
                              '${chefDetails?.data?.chef?.chef?.firstName ?? ''} ${chefDetails?.data?.chef?.chef?.lastName ?? ''}',
                              style: TextStyle(
                                fontSize: sixteen,
                                fontWeight: FontWeight.w600,
                                color: const Color(0xFF1F2122),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(
                      height: twelve,
                    ),
                    Padding(
                      padding:
                          EdgeInsets.symmetric(horizontal: size.width * 0.04),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              // Container(
                              //   padding: EdgeInsets.symmetric(
                              //       horizontal: twelve / 2, vertical: 0),
                              //   constraints: BoxConstraints(
                              //       minWidth: ten * 5, minHeight: ten * 3.4),
                              //   decoration: BoxDecoration(
                              //     color: const Color(0xFFF6F3EC),
                              //     borderRadius: BorderRadius.circular(ten),
                              //     border: Border.all(
                              //         color: const Color(0xFFB9B6AD)),
                              //   ),
                              //   child: Row(
                              //     children: [
                              //       Icon(Icons.access_time,
                              //           size: sixteen,
                              //           color: Color(0xFF414346)),
                              //       SizedBox(width: sixteen / 4),
                              //       Text(
                              //         "35 mins",
                              //         style: TextStyle(
                              //           fontFamily: 'Inter',
                              //           fontSize: forteen,
                              //           fontWeight: FontWeight.w600,
                              //           height: 24 / 16,
                              //           color: Color(0xFF1F2122),
                              //         ),
                              //       ),
                              //     ],
                              //   ),
                              // ),
                              SizedBox(width: sixteen / 2),
                              Container(
                                padding: EdgeInsets.symmetric(
                                    horizontal: twelve / 2, vertical: 0),
                                constraints: BoxConstraints(
                                    minWidth: ten * 6.1, minHeight: ten * 3.4),
                                decoration: BoxDecoration(
                                  color: const Color(0xFFF6F3EC),
                                  borderRadius: BorderRadius.circular(ten),
                                  border: Border.all(
                                      color: const Color(0xFFB9B6AD)),
                                ),
                                child: Row(
                                  children: [
                                    Icon(Icons.star,
                                        size: sixteen,
                                        color: Color(0xFF414346)),
                                    SizedBox(width: sixteen / 4),
                                    Text(
                                      chefDetails?.data?.chef?.averageRating ??
                                          '0',
                                      style: TextStyle(
                                        fontFamily: 'Inter',
                                        fontSize: forteen,
                                        fontWeight: FontWeight.w500,
                                        height: 24 / 16,
                                        color: Color(0xFF1F2122),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(width: sixteen / 2),
                              Container(
                                padding: EdgeInsets.symmetric(
                                    horizontal: twelve / 2, vertical: 0),
                                constraints: BoxConstraints(
                                    minWidth: ten * 8.7, minHeight: ten * 3.4),
                                decoration: BoxDecoration(
                                  color: const Color(0xFFF6F3EC),
                                  borderRadius: BorderRadius.circular(10),
                                  border: Border.all(
                                      color: const Color(0xFFB9B6AD)),
                                ),
                                child: Row(
                                  children: [
                                    Icon(Icons.location_on_outlined,
                                        size: ten * 1.7,
                                        color: Color(0xFF414346)),
                                    SizedBox(width: sixteen / 4),
                                    Text(
                                      _formatDistance(
                                          chefDetails?.data?.chef?.distance),
                                      style: TextStyle(
                                        fontFamily: 'Inter',
                                        fontSize: forteen,
                                        fontWeight: FontWeight.w500,
                                        height: 24 / 16,
                                        color: Color(0xFF1F2122),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(width: sixteen / 2),
                              Container(
                                width: size.width * 0.08,
                                height: size.width * 0.08,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius:
                                      BorderRadius.circular(size.width * 0.02),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.05),
                                      blurRadius: 4,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: IconButton(
                                  icon: Image.asset(
                                    'assets/icons/favorites.png',
                                    width: twenty,
                                    height: twenty,
                                    color: const Color(0xFF1F2122),
                                  ),
                                  onPressed: () {},
                                  padding: EdgeInsets.zero,
                                ),
                              ),
                              // const SizedBox(width: sixteen/2),
                              // Container(
                              //   width: 34,
                              //   height: 34,
                              //   decoration: BoxDecoration(
                              //     color: Colors.white,
                              //     borderRadius: BorderRadius.circular(10),
                              //     boxShadow: [
                              //       BoxShadow(
                              //         color: Colors.black.withOpacity(0.05),
                              //         blurRadius: sixteen/4,
                              //         offset: const Offset(0, 2),
                              //       ),
                              //     ],
                              //   ),
                              //   child: IconButton(
                              //     icon: Image.asset(
                              //       'assets/icons/favorites.png',
                              //       width: twentyFour
                              //       height: twentyFour
                              //       color: Color(0xFF1F2122),
                              //     ),
                              //     onPressed: () {},
                              //     padding: EdgeInsets.zero,
                              //   ),
                              // ),
                            ],
                          ),
                          // Wrap(
                          //   spacing: size.width * 0.02,
                          //   children: [
                          //     _buildMetricContainer(
                          //         size, Icons.access_time, "35 mins"),
                          //     _buildMetricContainer(size, Icons.star, "4.9"),
                          //     _buildMetricContainer(
                          //         size,
                          //         Icons.location_on_outlined,
                          //         widget.chefLocation ?? "_"),
                          //     Container(
                          //       width: size.width * 0.1,
                          //       height: size.width * 0.1,
                          //       decoration: BoxDecoration(
                          //         color: Colors.white,
                          //         borderRadius:
                          //             BorderRadius.circular(size.width * 0.02),
                          //         boxShadow: [
                          //           BoxShadow(
                          //             color: Colors.black.withOpacity(0.05),
                          //             blurRadius: 4,
                          //             offset: const Offset(0, 2),
                          //           ),
                          //         ],
                          //       ),
                          //       child: IconButton(
                          //         icon: Image.asset(
                          //           'assets/icons/favorites.png',
                          //           width: size.width * 0.06,
                          //           height: size.width * 0.06,
                          //           color: const Color(0xFF1F2122),
                          //         ),
                          //         onPressed: () {},
                          //         padding: EdgeInsets.zero,
                          //       ),
                          //     ),
                          //   ],
                          // ),
                          SizedBox(height: screenHeight * 0.016),
                          Wrap(
                            spacing: screenWidth * 0.03,
                            runSpacing: screenHeight * 0.005,
                            children:
                                (chefDetails?.data?.chef?.searchTags ?? [])
                                    .map((tag) {
                              return Text(
                                tag,
                                style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontSize: forteen,
                                  fontWeight: FontWeight.w400,
                                  color: const Color(0xFF414346),
                                ),
                              );
                            }).toList(),
                          ),
                          SizedBox(height: screenHeight * 0.02),
                          Text(
                            chefDetails?.data?.chef?.description ?? '',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontSize: forteen,
                              fontWeight: FontWeight.w400,
                              color: const Color(0xFF414346),
                            ),
                            textAlign: TextAlign.justify,
                          ),
                          SizedBox(height: screenHeight * 0.017),
                        ],
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: size.width * 0.04,
                          vertical: size.height * 0.015),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _categories.isNotEmpty
                              ? SizedBox(
                                  height: screenHeight * 0.04,
                                  child: ListView(
                                    scrollDirection: Axis.horizontal,
                                    children: _categories
                                        .asMap()
                                        .entries
                                        .map((entry) {
                                      final index = entry.key;
                                      final category = entry.value;
                                      final isSelected =
                                          _tabController!.index == index;
                                      return GestureDetector(
                                        onTap: () {
                                          setState(() {
                                            _tabController!.animateTo(index);
                                          });
                                        },
                                        child: Container(
                                          margin: EdgeInsets.only(
                                              right: screenWidth * 0.02),
                                          padding: EdgeInsets.symmetric(
                                            horizontal: screenWidth * 0.03,
                                            vertical: screenHeight * 0.005,
                                          ),
                                          decoration: BoxDecoration(
                                            color: isSelected
                                                ? const Color(0xFFB9B6AD)
                                                : const Color(0xFFE1DDD5),
                                            borderRadius: BorderRadius.circular(
                                                screenWidth * 0.05),
                                          ),
                                          child: Center(
                                            child: Text(
                                              category,
                                              style: TextStyle(
                                                fontFamily: 'Inter',
                                                fontSize: forteen,
                                                fontWeight: FontWeight.w500,
                                                color: const Color(0xFF1F2122),
                                              ),
                                            ),
                                          ),
                                        ),
                                      );
                                    }).toList(),
                                  ),
                                )
                              : const SizedBox(),
                          SizedBox(height: screenHeight * 0.033),
                          // Padding(
                          //   // padding: EdgeInsets.symmetric(
                          //   //     vertical: size.height * 0.02),
                          //              padding: EdgeInsets.zero,
                          //   child: Container(
                          //     height: size.height * 0.05,
                          //     decoration: BoxDecoration(
                          //       borderRadius:
                          //           BorderRadius.circular(size.width * 0.06),
                          //       border:
                          //           Border.all(color: const Color(0xFF1F2122)),
                          //     ),
                          //     child: InkWell(
                          //       borderRadius:
                          //           BorderRadius.circular(size.width * 0.06),
                          //       onTap: () {},
                          //       child: Row(
                          //         mainAxisAlignment: MainAxisAlignment.center,
                          //         children: [
                          //           Icon(Icons.tune,
                          //                size: twelve,
                          //               color: const Color(0xFF1F2122)),
                          //           SizedBox(width: size.width * 0.02),
                          //           Text(
                          //             "View Filters",
                          //             style: TextStyle(
                          //               fontSize: twelve,
                          //               fontWeight: FontWeight.w600,
                          //               color: const Color(0xFF1F2122),
                          //             ),
                          //           ),
                          //         ],
                          //       ),
                          //     ),
                          //   ),
                          // ),
                          SizedBox(height: screenHeight * 0.02),
                          Text(
                            "Featured Items",
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontSize: twenty,
                              fontWeight: FontWeight.w600,
                              color: const Color(0xFF1F2122),
                            ),
                          ),
                          SizedBox(height: screenHeight * 0.017),
                          isLoading
                              ? _buildShimmerCards(size, isTablet)
                              : _buildFeaturedItems(
                                  size, isTablet, isLandscape),
                          SizedBox(height: screenHeight * 0.01),
                          isLoading
                              ? _buildShimmerCategoryContent(size)
                              : _buildCategoryContent(
                                  size, isTablet, isLandscape),
                          SizedBox(height: screenHeight * 0.03),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              bottomNavigationBar: Container(
                padding: EdgeInsets.all(size.width * 0.04),
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      offset: const Offset(0, -2),
                      blurRadius: 8,
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    const Spacer(),
                    ElevatedButton(
                      onPressed: _selectChefForDay,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF1F2122),
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.symmetric(
                          horizontal: size.width * 0.06,
                          vertical: size.height * 0.02,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.circular(size.width * 0.07),
                        ),
                      ),
                      child: Text(
                        widget.isEditing
                            ? 'Edit Chef'
                            : 'Select Chef for Day ${widget.selectedDay}',
                        style: TextStyle(
                          fontSize: forteen,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildDateSelector(Size size) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final circleSize = (size.width * 0.06).clamp(20.0, 25.0);
        final connectorWidth = (size.width * 0.02).clamp(5.0, 7.0);
        // In edit mode, generate dates from the meal plan start date, not the selected date
        final dateForGeneration =
            widget.isEditing && _nextDatesFromStartDate.isEmpty
                ? _startDate
                : _startDate;
        _nextDatesFromStartDate = _generateNextDates(dateForGeneration);

        return Row(
          mainAxisSize: MainAxisSize.min,
          children: List.generate(widget.mealPlanDuration, (index) {
            final number = index + 1;

            // In edit mode, highlight the day that corresponds to the selected date
            bool isSelected = false;
            if (widget.isEditing) {
              // Find which day corresponds to the selected date
              if (index < _nextDatesFromStartDate.length) {
                final dayDate = _nextDatesFromStartDate[index];
                final selectedDateOnly =
                    widget.selectedDate.split(',')[0].trim();
                isSelected = dayDate == selectedDateOnly;
              }
            } else {
              isSelected =
                  _selectedDate == number || number <= widget.selectedDay;
            }

            final isFirst = index == 0;
            final isLast = index == widget.mealPlanDuration - 1;
            String displayDate = '';
            if (!_isLoading && _nextDatesFromStartDate.isNotEmpty) {
              if (index < _nextDatesFromStartDate.length) {
                final date = DateTime.parse(_nextDatesFromStartDate[index]);
                displayDate = '${date.day}/${date.month}';
              }
            }
            return Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (!isFirst)
                  Container(
                    width: connectorWidth,
                    height: 1,
                    color: const Color(0xFFB9B6AD),
                  ),
                Tooltip(
                  message: displayDate,
                  child: Container(
                    width: circleSize,
                    height: circleSize,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: isSelected
                          ? const Color(0xFF1F2122)
                          : Colors.transparent,
                      border: Border.all(
                        color: isSelected
                            ? const Color(0xFF1F2122)
                            : const Color(0xFFB9B6AD),
                        width: 1,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        "$number",
                        style: TextStyle(
                          fontSize: circleSize * 0.5,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'Inter',
                          color: isSelected
                              ? Colors.white
                              : const Color(0xFF1F2122),
                        ),
                      ),
                    ),
                  ),
                ),
                if (!isLast)
                  Container(
                    width: connectorWidth,
                    height: 1,
                    color: const Color(0xFFB9B6AD),
                  ),
              ],
            );
          }),
        );
      },
    );
  }

  // Update method name and logic to handle variable duration
  List<String> _generateNextDates(String startDate) {
    if (startDate.isEmpty) return [];
    List<String> dates = [];
    DateTime currentDate;
    try {
      // Extract just the date part if it contains a comma
      String dateOnly = startDate.split(',')[0].trim();
      currentDate = DateTime.parse(dateOnly);
    } catch (e) {
      currentDate = DateTime.now();
    }
    for (int i = 0; i < widget.mealPlanDuration; i++) {
      if (i == 0) {
        dates.add(currentDate.toString().split(' ')[0]);
        continue;
      }
      do {
        currentDate = currentDate.add(const Duration(days: 1));
      } while (currentDate.weekday == DateTime.saturday ||
          currentDate.weekday == DateTime.sunday);
      dates.add(currentDate.toString().split(' ')[0]);
    }
    return dates;
  }

  String _formatTimeToAmPm(String? time) {
    if (time == null) return '';
    try {
      final timeParts = time.split(':');
      int hour = int.parse(timeParts[0]);
      int minute = int.parse(timeParts[1]);
      String period = hour >= 12 ? 'PM' : 'AM';
      hour = hour > 12 ? hour - 12 : hour;
      hour = hour == 0 ? 12 : hour;
      return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}$period';
    } catch (e) {
      return time;
    }
  }

  Widget _buildMetricContainer(Size size, IconData icon, String text) {
    return Container(
      padding: EdgeInsets.symmetric(
          horizontal: size.width * 0.015, vertical: size.height * 0.005),
      constraints: BoxConstraints(
        minWidth: size.width * 0.15,
        minHeight: size.height * 0.05,
      ),
      decoration: BoxDecoration(
        color: const Color(0xFFF6F3EC),
        borderRadius: BorderRadius.circular(size.width * 0.02),
        border: Border.all(color: const Color(0xFFB9B6AD)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: size.width * 0.04, color: const Color(0xFF414346)),
          SizedBox(width: size.width * 0.01),
          Text(
            text,
            style: TextStyle(
              fontSize: size.width * 0.035,
              fontWeight: FontWeight.w500,
              fontFamily: 'Inter',
              color: const Color(0xFF1F2122),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturedItems(Size size, bool isTablet, bool isLandscape) {
    final featuredList = dishesData?.data?.featuredList;

    // If no featured items available, show "no featured meals available" message
    if (featuredList == null || featuredList.isEmpty) {
      return Container(
        height: ten * 26 + sixteen,
        width: double.infinity,
        margin: EdgeInsets.symmetric(horizontal: size.width * 0.04),
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(size.width * 0.04),
          border: Border.all(color: Colors.grey[300]!, width: 1),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.star_outline,
                size: size.width * 0.08,
                color: Colors.grey[400],
              ),
              SizedBox(height: size.height * 0.01),
              Text(
                'No featured meals available',
                style: TextStyle(
                  fontSize: size.width * 0.04,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[600],
                  fontFamily: 'Inter',
                ),
              ),
            ],
          ),
        ),
      );
    }

    return SizedBox(
      height: ten * 26 + sixteen,
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.only(right: size.width * 0.04),
        children: featuredList
            .map((dish) => Container(
                  width: ten * 20 + eighteen,
                  margin: EdgeInsets.only(right: screenWidth * 0.04),
                  child: _buildDishCard({
                    'name': dish.name ?? '',
                    'photo': dish.photo ?? '',
                    'price': dish.servingSizePrices?.first?.price ?? '0.00',
                    'serving_size':
                        dish.servingSizePrices?.first?.servingSize?.title ?? '',
                  }, size),
                ))
            .toList(),
      ),
    );
  }

  Widget _buildCategorySection(
      String category, Size size, bool isTablet, bool isLandscape) {
    final categoryList = dishesData?.data?.categoryBasedList
        ?.firstWhere((cat) => cat.category?.name == category,
            orElse: () => CategoryBasedList())
        .dishList;

    // If no dishes available, show "no meals available" message
    if (categoryList == null || categoryList.isEmpty) {
      return Container(
        height: ten * 26 + sixteen,
        width: double.infinity,
        margin: EdgeInsets.symmetric(horizontal: size.width * 0.04),
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(size.width * 0.04),
          border: Border.all(color: Colors.grey[300]!, width: 1),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.restaurant_menu,
                size: size.width * 0.08,
                color: Colors.grey[400],
              ),
              SizedBox(height: size.height * 0.01),
              Text(
                'No meals available',
                style: TextStyle(
                  fontSize: size.width * 0.04,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[600],
                  fontFamily: 'Inter',
                ),
              ),
            ],
          ),
        ),
      );
    }

    return SizedBox(
      height: ten * 26 + sixteen,
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.only(right: size.width * 0.04),
        children: categoryList
            .map((dish) => Container(
                  width: isTablet ? size.width * 0.4 : ten * 20 + eighteen,
                  margin: EdgeInsets.only(right: size.width * 0.04),
                  child: _buildDishCard({
                    'name': dish.name ?? '',
                    'photo': dish.photo ?? '',
                    'price': dish.servingSizePrices?.first?.price ?? '0.00',
                    'serving_size':
                        dish.servingSizePrices?.first?.servingSize?.title ?? '',
                  }, size),
                ))
            .toList(),
      ),
    );
  }

  Widget _buildCategoryContent(Size size, bool isTablet, bool isLandscape) {
    if (dishesData?.data?.categoryBasedList == null) return const SizedBox();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: dishesData!.data!.categoryBasedList!.map((category) {
        final categoryName = category.category?.name ?? '';

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(vertical: size.height * 0.01),
              child: Text(
                categoryName,
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontSize: twenty,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF1F2122),
                ),
              ),
            ),
            SizedBox(height: size.height * 0.01),
            _buildCategorySection(categoryName, size, isTablet, isLandscape),
            SizedBox(height: size.height * 0.02),
          ],
        );
      }).toList(),
    );
  }

  Widget _buildShimmerCards(Size size, bool isTablet) {
    return SizedBox(
      height: size.height * (isTablet ? 0.5 : 0.45),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: 3,
        itemBuilder: (context, index) {
          return Container(
            width: isTablet ? size.width * 0.4 : size.width * 0.75,
            margin: EdgeInsets.only(right: size.width * 0.04),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(size.width * 0.04),
            ),
            child: Shimmer.fromColors(
              baseColor: Colors.grey[300]!,
              highlightColor: Colors.grey[100]!,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    height: size.height * 0.25,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.vertical(
                          top: Radius.circular(size.width * 0.04)),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.all(size.width * 0.04),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: size.width * 0.5,
                          height: size.height * 0.03,
                          color: Colors.white,
                        ),
                        SizedBox(height: size.height * 0.015),
                        Container(
                          width: size.width * 0.3,
                          height: size.height * 0.02,
                          color: Colors.white,
                        ),
                        SizedBox(height: size.height * 0.015),
                        Container(
                          width: size.width * 0.2,
                          height: size.height * 0.02,
                          color: Colors.white,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildShimmerCategoryContent(Size size) {
    return Column(
      children: List.generate(
        3,
        (index) => Padding(
          padding: EdgeInsets.symmetric(
              horizontal: size.width * 0.04, vertical: size.height * 0.01),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(
                  width: size.width * 0.4,
                  height: size.height * 0.03,
                  color: Colors.white,
                ),
              ),
              SizedBox(height: size.height * 0.015),
              _buildShimmerCards(size, false),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDishCard(Map<String, dynamic> dish, Size size) {
    String rating = "90";
    final priceDouble = double.tryParse(dish['price'] ?? '0.00') ?? 0.0;
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(screenWidth * 0.04),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ClipRRect(
            borderRadius:
                BorderRadius.vertical(top: Radius.circular(screenWidth * 0.04)),
            child: Image.network(
              ServerHelper.imageUrl + (dish['photo'] ?? ''),
              height: ten * 15 + twelve,
              width: double.infinity,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  height: ten * 15 + twelve,
                  width: double.infinity,
                  color: Colors.grey[200],
                  child: const Center(child: Text('Image not available')),
                );
              },
            ),
          ),
          Padding(
            padding: EdgeInsets.only(
                top: screenWidth * 0.025,
                bottom: screenWidth * 0.015,
                right: screenWidth * 0.04,
                left: screenWidth * 0.04),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  dish['name'] ?? 'Unknown Dish',
                  style: TextStyle(
                    fontSize: forteen,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFF1F2122),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: screenHeight * 0.015),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Wrap(
                            spacing: screenWidth * 0.02,
                            runSpacing: screenHeight * 0.01,
                            children: [
                              Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: screenWidth * 0.015,
                                  vertical: screenWidth * 0.005,
                                ),
                                decoration: BoxDecoration(
                                  color: const Color(0xFFE1E3E6),
                                  borderRadius:
                                      BorderRadius.circular(screenWidth * 0.03),
                                ),
                                child: Text(
                                  "${dish['serving_size'].split(' ').first} Servings",
                                  style: TextStyle(
                                    fontSize: ten,
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Inter',
                                  ),
                                ),
                              ),
                              Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: screenWidth * 0.015,
                                  vertical: screenWidth * 0.005,
                                ),
                                decoration: BoxDecoration(
                                  color: const Color(0xFFE1E3E6),
                                  borderRadius:
                                      BorderRadius.circular(screenWidth * 0.03),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Image.asset(
                                      'assets/icons/thump.png',
                                      width: screenWidth * 0.03,
                                      height: screenWidth * 0.0275,
                                      color: Colors.black54,
                                    ),
                                    SizedBox(width: screenWidth * 0.01),
                                    Text(
                                      "$rating%",
                                      style: TextStyle(
                                        fontSize: ten,
                                        fontWeight: FontWeight.w500,
                                        fontFamily: 'Inter',
                                      ),
                                    ),
                                    SizedBox(width: screenWidth * 0.005),
                                    Text(
                                      "($rating)",
                                      style: TextStyle(
                                        fontSize: ten,
                                        fontWeight: FontWeight.w500,
                                        fontFamily: 'Inter',
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: screenHeight * 0.02),
                          Text.rich(
                            TextSpan(
                              children: [
                                TextSpan(
                                  text: '\$',
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontSize: twelve,
                                    fontWeight: FontWeight.w600,
                                    color: const Color(0xFF1F2122),
                                  ),
                                ),
                                TextSpan(
                                  text: priceDouble.toStringAsFixed(2),
                                  style: TextStyle(
                                    fontFamily: 'Roboto',
                                    fontSize: twelve,
                                    fontWeight: FontWeight.w600,
                                    color: const Color(0xFF1F2122),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(
                            height: 4,
                          )
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // String _formatDay() {
  //   return 'Day 1';
  // }

  // String _formatOperationTimes(List<dynamic>? operationTimes) {
  //   if (operationTimes == null || operationTimes.isEmpty) return 'Open: N/A';
  //   var time = operationTimes.first;
  //   String startTime = time.timing?.startTime ?? 'N/A';
  //   String endTime = time.timing?.endTime ?? 'N/A';
  //   String formattedStart = _formatTimeString(startTime);
  //   String formattedEnd = _formatTimeString(endTime);
  //   return 'Open $formattedStart-$formattedEnd';
  // }

  String _formatTimeString(String timeStr) {
    if (timeStr == 'N/A') return timeStr;
    try {
      List<String> parts = timeStr.split(':');
      if (parts.length < 2) return timeStr;
      int hour = int.parse(parts[0]);
      bool isPM = hour >= 12;
      if (hour > 12) hour -= 12;
      if (hour == 0) hour = 12;
      return '$hour${isPM ? 'PM' : 'AM'}';
    } catch (e) {
      return timeStr;
    }
  }

  // String _formatOperationDays(List<dynamic>? operationDays) {
  //   if (operationDays == null || operationDays.isEmpty) return '';
  //   Map<String, String> dayAbbreviations = {
  //     'Monday': 'M',
  //     'Tuesday': 'T',
  //     'Wednesday': 'W',
  //     'Thursday': 'Th',
  //     'Friday': 'F',
  //     'Saturday': 'Sat',
  //     'Sunday': 'Sun',
  //   };
  //   List formattedDays = operationDays
  //       .map((day) =>
  //           dayAbbreviations[day.day?.name ?? ''] ?? day.day?.name ?? '')
  //       .where((day) => day.isNotEmpty)
  //       .toList();
  //   return formattedDays.join(', ');
  // }

  // List<String> _generateNextFiveDates(String startDate) {
  //   List<String> dates = [];
  //   DateTime currentDate = DateTime.parse(startDate);
  //   for (int i = 0; i < widget.mealPlanDuration; i++) {
  //     dates.add(currentDate.toString().split(' ')[0]);
  //     currentDate = currentDate.add(const Duration(days: 1));
  //   }
  //   return dates;
  // }

  String _formatDate(String date) {
    if (date.isEmpty) return '';

    try {
      // If the date already contains a comma, it might be already formatted
      // Extract just the date part before the comma
      String dateOnly = date.split(',')[0].trim();

      // Try to parse the date
      final DateTime dateTime = DateTime.parse(dateOnly);
      final months = [
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'May',
        'Jun',
        'Jul',
        'Aug',
        'Sep',
        'Oct',
        'Nov',
        'Dec'
      ];
      final days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
      return "${months[dateTime.month - 1]} ${dateTime.day}, ${dateTime.year}, ${days[dateTime.weekday - 1]}";
    } catch (e) {
      // If parsing fails, return the original date or a fallback
      return date; // Return the original string if parsing fails
    }
  }
}

String _formatDistance(num? distance) {
  if (distance == null) return 'Unknown';

  // Convert meters to kilometers
  final kilometers = distance / 1000;

  // Format to one decimal place
  return '${kilometers.toStringAsFixed(1)} km';
}
