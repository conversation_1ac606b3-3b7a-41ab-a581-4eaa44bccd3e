import 'dart:developer';

import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/data/models/chef/viewchefdetailsmodel.dart';
import 'package:db_eats/data/models/chef/viewdishesmodel.dart';
import 'package:db_eats/data/models/data/filterdatamodel.dart';
import 'package:db_eats/data/models/data/recentpopularsearchmodel.dart';
import 'package:db_eats/data/models/globalsearch/searchdishesmodel.dart';
import 'package:db_eats/data/models/guesthome/guesthomemodel.dart';
import 'package:db_eats/data/models/guesthome/homemodel.dart';
import 'package:db_eats/data/models/guesthome/popularchefsnearmodel.dart';
import 'package:db_eats/data/models/guesthome/recommendedchefsmodel.dart';
import 'package:db_eats/data/models/guesthome/topratedchefsmodel.dart';
import 'package:db_eats/data/models/verifyrefreshtokenmodel.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/storage/localstorage.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class HomeBloc extends Bloc<HomeEvent, HomeState> {
  HomeModel? homeData;
  GuestHomeModel? guestHomeData;
  HomeBloc() : super(GuestHomeInitial()) {
    on<GetGuestHomeDataEvent>(_getGuestHomeData);
    on<GetHomeDataEvent>(_getHomeData);
    on<GetTopRatedChefsEvent>(_getTopRatedChefs);
    on<GetRecommendedChefsEvent>(_getRecommendedChefs);
    on<GetPopularChefsNearEvent>(_getPopularChefsNear);
    on<ViewChefDetailsEvent>(_viewChefDetails);
    on<GetDishesListEvent>(_getDishesList);
    on<GetFilterDataEvent>(_getFilterData);
    on<GetRecentPopularSearchEvent>(_getRecentPopularSearch);
    on<SearchDishesEvent>(_searchDishes);
    on<RefreshTokenEvent>(_refreshToken); // <-- add handler
  }

  Future<void> _getGuestHomeData(
    GetGuestHomeDataEvent event,
    Emitter<HomeState> emit,
  ) async {
    try {
      emit(GuestHomeLoading());

      final response = await ServerHelper.get1(
        '/v1/guest/home/<USER>',
      );
      log('Guest Home Data: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));
        return;
      }

      if (response['status'] == true) {
        final guestHomeData = GuestHomeModel.fromJson(response);
        emit(GuestHomeSuccess(guestHomeData));
      } else {
        emit(GuestHomeFailed(
            response['message'] ?? 'Failed to fetch home data'));
      }
    } catch (e) {
      emit(GuestHomeFailed(e.toString()));
    }
  }

  Future<void> _getHomeData(
    GetHomeDataEvent event,
    Emitter<HomeState> emit,
  ) async {
    try {
      emit(HomeDataLoading());

      final response = await ServerHelper.post1(
        '/v1/customer/home/<USER>',
        event.data,
      );
      log('Home Data: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));
        return;
      }

      if (response['status'] == true) {
        final homeData = HomeModel.fromJson(response);
        emit(HomeDataSuccess(homeData));
      } else {
        emit(
            HomeDataFailed(response['message'] ?? 'Failed to fetch home data'));
      }
    } catch (e) {
      emit(HomeDataFailed(e.toString()));
    }
  }

  Future<void> _getTopRatedChefs(
    GetTopRatedChefsEvent event,
    Emitter<HomeState> emit,
  ) async {
    try {
      emit(TopRatedChefsLoading());

      final response = await ServerHelper.get1(
        '/v1/customer/home/<USER>',
      );
      log('Top Rated Chefs: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));
        return;
      }

      if (response['status'] == true) {
        final topratedchefsmodel = TopRatedChefsModel.fromJson(response);
        emit(TopRatedChefsSuccess(topratedchefsmodel));
      } else {
        emit(TopRatedChefsFailed(
            response['message'] ?? 'Failed to fetch top rated chefs'));
      }
    } catch (e) {
      emit(TopRatedChefsFailed(e.toString()));
    }
  }

  Future<void> _getRecommendedChefs(
    GetRecommendedChefsEvent event,
    Emitter<HomeState> emit,
  ) async {
    try {
      emit(RecommendedChefsLoading());

      final response = await ServerHelper.get1(
        '/v1/customer/home/<USER>',
      );
      log('Recommended Chefs: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));
        return;
      }

      if (response['status'] == true) {
        final recommendedchefsmodel = RecommendedChefsModel.fromJson(response);
        emit(RecommendedChefsSuccess(recommendedchefsmodel));
      } else {
        emit(RecommendedChefsFailed(
            response['message'] ?? 'Failed to fetch recommended chefs'));
      }
    } catch (e) {
      emit(RecommendedChefsFailed(e.toString()));
    }
  }

  Future<void> _getPopularChefsNear(
    GetPopularChefsNearEvent event,
    Emitter<HomeState> emit,
  ) async {
    try {
      emit(PopularChefsNearLoading());

      final response = await ServerHelper.get1(
        '/v1/customer/home/<USER>',
      );
      log('Popular Chefs Near: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));
        return;
      }

      if (response['status'] == true) {
        final popularchefsnearmodel = PopularchefsNearModel.fromJson(response);
        emit(PopularChefsNearSuccess(popularchefsnearmodel));
      } else {
        emit(PopularChefsNearFailed(
            response['message'] ?? 'Failed to fetch popular chefs near'));
      }
    } catch (e) {
      emit(PopularChefsNearFailed(e.toString()));
    }
  }

  Future<void> _viewChefDetails(
    ViewChefDetailsEvent event,
    Emitter<HomeState> emit,
  ) async {
    try {
      emit(LoadingChefDetails());

      final response = await ServerHelper.get1(
        '/v1/customer/chef/get?chef_id=${event.chefId}',
      );
      log('Chef Details: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));
        return;
      }

      if (response['status'] == true) {
        final chefDetailsModel = ChefDetailsModel.fromJson(response);
        emit(ChefDetailsSuccess(chefDetailsModel));
      } else {
        emit(ChefDetailsFailed(
            response['message'] ?? 'Failed to fetch chef details'));
      }
    } catch (e) {
      emit(ChefDetailsFailed(e.toString()));
    }
  }

  Future<void> _getDishesList(
    GetDishesListEvent event,
    Emitter<HomeState> emit,
  ) async {
    try {
      emit(DishesListLoading());

      final response = await ServerHelper.post1(
        '/v1/customer/dish/list-by-category',
        event.data,
      );
      log('Dishes List: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));
        return;
      }

      if (response['status'] == true) {
        final dishesListModel = DishesListModel.fromJson(response);
        emit(DishesListSuccess(dishesListModel));
      } else {
        emit(DishesListFailed(
            response['message'] ?? 'Failed to fetch dishes list'));
      }
    } catch (e) {
      emit(DishesListFailed(e.toString()));
    }
  }

  Future<void> _getFilterData(
    GetFilterDataEvent event,
    Emitter<HomeState> emit,
  ) async {
    try {
      emit(FilterDataLoading());

      final response = await ServerHelper.get1(
        '/v1/common/home/<USER>',
      );
      log('Filter Data: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));
        return;
      }

      Initializer.filterdataModel = FilterdataModel.fromJson(response);

      if (Initializer.filterdataModel.status == true) {
        emit(FilterDataSuccess(Initializer.filterdataModel));
      } else {
        emit(FilterDataFailed(
            response['message'] ?? 'Failed to fetch filter data'));
      }
    } catch (e) {
      emit(FilterDataFailed(e.toString()));
    }
  }

  Future<void> _getRecentPopularSearch(
    GetRecentPopularSearchEvent event,
    Emitter<HomeState> emit,
  ) async {
    try {
      emit(RecentPopularSearchLoading());

      final response = await ServerHelper.post1(
        '/v1/customer/recent_search/list',
        event.data,
      );
      log('Recent Popular Search: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));
        return;
      }

      Initializer.recentPopularSearchModel =
          RecentPopularSearchModel.fromJson(response);

      if (Initializer.recentPopularSearchModel.status == true) {
        emit(RecentPopularSearchSuccess(Initializer.recentPopularSearchModel));
      } else {
        emit(RecentPopularSearchFailed(
            response['message'] ?? 'Failed to fetch recent popular search'));
      }
    } catch (e) {
      emit(RecentPopularSearchFailed(e.toString()));
    }
  }

  Future<void> _searchDishes(
    SearchDishesEvent event,
    Emitter<HomeState> emit,
  ) async {
    try {
      emit(SearchDishesLoading());

      final response = await ServerHelper.post1(
        '/v1/customer/home/<USER>',
        event.data,
      );
      log('Search Dishes: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: await LocalStorage.getRefreshToken() ?? ""));
        return;
      }
      Initializer.searchDishesModel = SearchDishesModel.fromJson(response);

      if (Initializer.searchDishesModel.status == true) {
        emit(SearchDishesSuccess(Initializer.searchDishesModel));
      } else {
        emit(SearchDishesFailed(
            response['message'] ?? 'Failed to search dishes'));
      }
    } catch (e) {
      emit(SearchDishesFailed(e.toString()));
    }
  }

  Future<void> _refreshToken(
      RefreshTokenEvent event, Emitter<HomeState> emit) async {
    emit(RefreshTokenLoading());
    try {
      Initializer.verifyRefreshTokenModel = VerifyRefreshTokenModel.fromJson(
          await ServerHelper.getrefresh(
              '/v1/customer/auth/verify-refresh-token'));
      if (Initializer.verifyRefreshTokenModel.status == true) {
        await LocalStorage.setAccessToken(
            Initializer.verifyRefreshTokenModel.data?.accessToken ?? "");
        emit(RefreshTokenSuccess());
        if (event.nextEvent != null) {
          add(event.nextEvent!);
        }
      } else if (Initializer.verifyRefreshTokenModel.statusCode == 401) {
        emit(RefreshTokenFailed());
        // await LocalStorage.setAccessToken('');
        // await LocalStorage.setRefreshToken('');
        // Navigator.of(context).pushAndRemoveUntil(
        //   MaterialPageRoute(builder: (context) => const Home()),
        //   (route) => false,
        // );
      }
    } catch (e) {
      emit(RefreshTokenFailed());
    }
  }
}

// Events
abstract class HomeEvent {}

class GetGuestHomeDataEvent extends HomeEvent {
  final double latitude;
  final double longitude;

  GetGuestHomeDataEvent({required this.latitude, required this.longitude});
}

class GetHomeDataEvent extends HomeEvent {
  final Map<String, dynamic> data;

  GetHomeDataEvent({required this.data});
}

class GetTopRatedChefsEvent extends HomeEvent {
  final double latitude;
  final double longitude;

  GetTopRatedChefsEvent({
    required this.latitude,
    required this.longitude,
  });
}

class GetRecommendedChefsEvent extends HomeEvent {
  final double latitude;
  final double longitude;

  GetRecommendedChefsEvent({required this.latitude, required this.longitude});
}

class GetPopularChefsNearEvent extends HomeEvent {
  final double latitude;
  final double longitude;

  GetPopularChefsNearEvent({required this.latitude, required this.longitude});
}

class ViewChefDetailsEvent extends HomeEvent {
  final int chefId;

  ViewChefDetailsEvent({required this.chefId});
}

class GetDishesListEvent extends HomeEvent {
  final Map<String, dynamic> data;

  GetDishesListEvent({required this.data});
}

class GetFilterDataEvent extends HomeEvent {}

class GetRecentPopularSearchEvent extends HomeEvent {
  final Map<String, dynamic> data;

  GetRecentPopularSearchEvent({required this.data});
}

class SearchDishesEvent extends HomeEvent {
  final Map<String, dynamic> data;

  SearchDishesEvent({required this.data});
}

class RefreshTokenEvent extends HomeEvent {
  final String refreshToken;
  final HomeEvent? nextEvent;

  RefreshTokenEvent({required this.refreshToken, required this.nextEvent});
}

// States
abstract class HomeState {}

class GuestHomeInitial extends HomeState {}

class GuestHomeLoading extends HomeState {}

class GuestHomeSuccess extends HomeState {
  final GuestHomeModel data;
  GuestHomeSuccess(this.data);
}

class GuestHomeFailed extends HomeState {
  final String message;
  GuestHomeFailed(this.message);
}

class HomeDataLoading extends HomeState {}

class HomeDataSuccess extends HomeState {
  final HomeModel data;
  HomeDataSuccess(this.data);
}

class HomeDataFailed extends HomeState {
  final String message;
  HomeDataFailed(this.message);
}

class GetTopRatedChefsLoading extends HomeState {}

class GetTopRatedChefsSuccess extends HomeState {}

class GetTopRatedChefsFailed extends HomeState {
  final String message;
  GetTopRatedChefsFailed(this.message);
}

class TopRatedChefsLoading extends HomeState {}

class TopRatedChefsSuccess extends HomeState {
  final TopRatedChefsModel data;
  TopRatedChefsSuccess(this.data);
}

class TopRatedChefsFailed extends HomeState {
  final String message;
  TopRatedChefsFailed(this.message);
}

class RecommendedChefsLoading extends HomeState {}

class RecommendedChefsSuccess extends HomeState {
  final RecommendedChefsModel data;
  RecommendedChefsSuccess(this.data);
}

class RecommendedChefsFailed extends HomeState {
  final String message;
  RecommendedChefsFailed(this.message);
}

class PopularChefsNearLoading extends HomeState {}

class PopularChefsNearSuccess extends HomeState {
  final PopularchefsNearModel data;
  PopularChefsNearSuccess(this.data);
}

class PopularChefsNearFailed extends HomeState {
  final String message;
  PopularChefsNearFailed(this.message);
}

class LoadingChefDetails extends HomeState {}

class ChefDetailsSuccess extends HomeState {
  final ChefDetailsModel data;
  ChefDetailsSuccess(this.data);
}

class ChefDetailsFailed extends HomeState {
  final String message;
  ChefDetailsFailed(this.message);
}

class DishesListLoading extends HomeState {}

class DishesListSuccess extends HomeState {
  final DishesListModel data;
  DishesListSuccess(this.data);
}

class DishesListFailed extends HomeState {
  final String message;
  DishesListFailed(this.message);
}

class FilterDataLoading extends HomeState {}

class FilterDataSuccess extends HomeState {
  final FilterdataModel data;
  FilterDataSuccess(this.data);
}

class FilterDataFailed extends HomeState {
  final String message;
  FilterDataFailed(this.message);
}

class RecentPopularSearchLoading extends HomeState {}

class RecentPopularSearchSuccess extends HomeState {
  final RecentPopularSearchModel data;
  RecentPopularSearchSuccess(this.data);
}

class RecentPopularSearchFailed extends HomeState {
  final String message;
  RecentPopularSearchFailed(this.message);
}

class RefreshTokenLoading extends HomeState {}

class RefreshTokenSuccess extends HomeState {}

class RefreshTokenFailed extends HomeState {}

class SearchDishesLoading extends HomeState {}

class SearchDishesSuccess extends HomeState {
  final SearchDishesModel data;
  SearchDishesSuccess(this.data);
}

class SearchDishesFailed extends HomeState {
  final String message;
  SearchDishesFailed(this.message);
}
