import 'dart:developer';

import 'package:db_eats/bloc/account_bloc.dart';
import 'package:db_eats/bloc/order_bloc.dart';
import 'package:db_eats/ui/cart/add_item.dart';
import 'package:db_eats/ui/cart/select_address.dart';
import 'package:db_eats/ui/chef/view_chef2.dart';
import 'package:db_eats/ui/coupon/applycoupon.dart';
import 'package:db_eats/ui/home2.dart';
import 'package:db_eats/ui/mainnavigationpage.dart';
import 'package:db_eats/ui/paymentgateway/paymentwebview.dart';
import 'package:db_eats/utils/customtoggle.dart';
import 'package:db_eats/utils/dotteddivider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:db_eats/bloc/mealplan_bloc.dart';
import 'package:shimmer/shimmer.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/data/models/meal_plan/dropoffoptionmodel.dart';
import 'package:db_eats/data/models/meal_plan/deliverytimemodel.dart';
import 'package:db_eats/data/models/guesthome/listaddressmodel.dart';
import 'package:lottie/lottie.dart';

class CartCheckoutPage extends StatefulWidget {
  final int chef_id;
  final double subtotal;
  final Map<String, dynamic> selectedChefsWithDetails;

  const CartCheckoutPage({
    super.key,
    required this.chef_id,
    required this.subtotal,
    required this.selectedChefsWithDetails,
  });

  @override
  State<CartCheckoutPage> createState() => _CartCheckoutPageState();
}

class _CartCheckoutPageState extends State<CartCheckoutPage> {
  String? dropOffOption;
  String? instructions;
  String? selectedDeliveryOption;
  dynamic orderSummaryData;

  List<DropoffOptionItem> _dropoffOptions = [];
  List<DeliveryTimeItem> _deliveryTimeOptions = [];
  DropoffOptionItem? _selectedDropoffOption;
  DeliveryTimeItem? _selectedDeliveryTime;
  String? _dropOffInstructions = "";

  bool _showDropoffError = false;
  bool _showDeliveryTimeError = false;
  bool _isPlacingOrder = false;
  bool _useWalletCredits = false;
  late TextEditingController _instructionsController;

  var chef;
  List<dynamic>? cartItems;
  double subtotal = 0.0;
  AddressData? currentAddressData;
  String? currentAddress;

  Map<String, dynamic>? appliedCoupon;

  String _getCurrentAddress(List<AddressData>? addresses) {
    if (addresses == null || addresses.isEmpty) return 'No address';
    final address = addresses.firstWhere(
      (address) => address.isCurrent == true,
      orElse: () => addresses.first,
    );
    currentAddressData = address;
    return address.addressText ?? 'No address';
  }

  @override
  void initState() {
    super.initState();
    context.read<AccountBloc>().add(ListAddressesEvent());
    context
        .read<OrderBloc>()
        .add(GetOrgerSummaryEvent({"chef_id": widget.chef_id.toString()}));
    _refreshCartDetails();
    _instructionsController = TextEditingController();
    context.read<MealplanBloc>().add(ListDropoffOptionEvent());
    context.read<MealplanBloc>().add(ListDeliveryTimeEvent());
  }

  void _refreshCartDetails() {
    final details = widget.selectedChefsWithDetails;
    setState(() {
      chef = details['chef'];
      cartItems = details['items'];
      subtotal = details['totalPrice'] ?? 0.0;
      currentAddress = details['currentAddress'];
    });
  }

  @override
  void dispose() {
    _instructionsController.dispose();
    super.dispose();
  }

  String _formatTime(String? time) {
    if (time == null) return '00:00 AM';
    final parts = time.split(':');
    if (parts.length < 2) return '00:00 AM';

    int hour = int.tryParse(parts[0]) ?? 0;
    String minute = parts[1];
    String period = hour >= 12 ? 'PM' : 'AM';

    if (hour > 12) hour -= 12;
    if (hour == 0) hour = 12;

    return '$hour:$minute $period';
  }

  String _formatTimeFromString(String? timeStr) {
    if (timeStr == null) return '00:00 AM';
    try {
      // Check if time already contains AM/PM
      if (timeStr.contains('AM') || timeStr.contains('PM')) {
        return timeStr;
      }

      final timeParts = timeStr.split(':');
      int hour = int.parse(timeParts[0]);
      String minute = timeParts[1];
      String period = hour >= 12 ? 'PM' : 'AM';

      if (hour > 12) hour -= 12;
      if (hour == 0) hour = 12;

      return '${hour.toString().padLeft(2, '0')}:$minute $period';
    } catch (e) {
      return '00:00 AM';
    }
  }

  Widget _buildLoadingShimmer(Size screenSize) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: screenSize.width * 0.2,
            height: screenSize.height * 0.01,
            margin: const EdgeInsets.symmetric(vertical: 4),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(6),
            ),
          ),
        ],
      ),
    );
  }

  DateTime selectedDate = DateTime.now();
  String selectedTime = '';
  TimeOfDay selectedTimeOfDay = TimeOfDay.now();

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 7)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Color(0xFF1F2122),
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Color(0xFF1F2122),
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null && picked != selectedDate) {
      setState(() {
        selectedDate = picked;
      });
    }
  }

  Future<void> _selectTime(BuildContext context) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: selectedTimeOfDay,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Color(0xFF1F2122),
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Color(0xFF1F2122),
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null && picked != selectedTimeOfDay) {
      setState(() {
        selectedTimeOfDay = picked;
        // Convert to 12-hour format for display
        final hour = selectedTimeOfDay.hourOfPeriod;
        final minute = selectedTimeOfDay.minute.toString().padLeft(2, '0');
        final period = selectedTimeOfDay.period == DayPeriod.am ? 'AM' : 'PM';
        final displayHour = (hour == 0 ? 12 : hour).toString().padLeft(2, '0');
        selectedTime =
            '$displayHour:$minute $period'; // Store in 12-hour format

        // Store 24-hour format for backend
        final backendHour = selectedTimeOfDay.hour.toString().padLeft(2, '0');
        final backendMinute =
            selectedTimeOfDay.minute.toString().padLeft(2, '0');
        orderSummaryData?.customerTimePreference?.startTime =
            '$backendHour:$backendMinute';
      });
    }
  }

  void _saveDeliveryDetails() {
    if (_selectedDropoffOption == null) {
      setState(() => _showDropoffError = true);
      return;
    }
    if (_selectedDeliveryTime == null) {
      setState(() => _showDeliveryTimeError = true);
      return;
    }
    final orderData = {
      "chef_id": widget.chef_id,
      "delivery_time_id": _selectedDeliveryTime?.id,
      "drop_off_option_id": _selectedDropoffOption?.id,
      "address_id": currentAddressData?.id,
      "drop_off_instructions": _dropOffInstructions ?? "",
      "payment_method": "ONLINE",
      "delivery_date":
          "${selectedDate.year}-${selectedDate.month.toString().padLeft(2, '0')}-${selectedDate.day.toString().padLeft(2, '0')}",
      "delivery_time":
          orderSummaryData?.customerTimePreference?.startTime ?? "",
      'use_wallet_credits': _useWalletCredits,
      if (appliedCoupon != null) "coupon_code": appliedCoupon!['code'],
    };

    context.read<OrderBloc>().add(CheckoutOrderEvent(orderData));
  }

  Widget _buildDropoffOptionDropdown(Size screenSize, bool isLargeScreen) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Drop-Off Options',
          style: TextStyle(
            fontFamily: 'Inter',
            fontSize: isLargeScreen ? 16 : screenSize.width * 0.035,
            fontWeight: FontWeight.w500,
            height: 1.14,
            letterSpacing: 0.28,
            color: const Color(0xFF1F2122),
          ),
        ),
        SizedBox(height: screenSize.height * 0.015),
        Container(
          height: isLargeScreen ? 48 : screenSize.height * 0.05,
          padding: EdgeInsets.symmetric(horizontal: screenSize.width * 0.045),
          decoration: BoxDecoration(
            border: Border.all(
              color: _showDropoffError ? Colors.red : const Color(0xFFE1E3E6),
            ),
            borderRadius: BorderRadius.circular(35),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<DropoffOptionItem>(
              value: _selectedDropoffOption,
              isExpanded: true,
              hint: Text(
                'Select drop-off option',
                style: TextStyle(
                  color:
                      _showDropoffError ? Colors.red : const Color(0xFF66696D),
                  fontSize: isLargeScreen ? 18 : screenSize.width * 0.04,
                ),
              ),
              icon: Icon(
                Icons.keyboard_arrow_down,
                color: const Color(0xFF1F2122),
                size: isLargeScreen ? 24 : screenSize.width * 0.06,
              ),
              items: _dropoffOptions
                  .map<DropdownMenuItem<DropoffOptionItem>>((item) {
                return DropdownMenuItem<DropoffOptionItem>(
                  value: item,
                  child: Text(
                    item.name ?? '',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w400,
                      fontSize: isLargeScreen ? 18 : screenSize.width * 0.04,
                      height: 1.5,
                      color: const Color(0xFF66696D),
                    ),
                  ),
                );
              }).toList(),
              onChanged: (DropoffOptionItem? newValue) {
                setState(() {
                  _selectedDropoffOption = newValue;
                  _showDropoffError = false;
                });
              },
            ),
          ),
        ),
        if (_showDropoffError)
          Padding(
            padding: EdgeInsets.only(top: screenSize.height * 0.01),
            child: Text(
              'Please select a drop-off option',
              style: TextStyle(
                color: Colors.red,
                fontSize: isLargeScreen ? 14 : screenSize.width * 0.03,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildDeliveryTimeOptions(Size screenSize, bool isLargeScreen) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Delivery Time',
          style: TextStyle(
            fontFamily: 'Inter',
            fontSize: isLargeScreen ? 18 : screenSize.width * 0.04,
            fontWeight: FontWeight.w500,
            color: const Color(0xFF1F2122),
          ),
        ),
        SizedBox(height: screenSize.height * 0.015),
        ..._deliveryTimeOptions.map((option) => Padding(
              padding: EdgeInsets.only(bottom: screenSize.height * 0.01),
              child: InkWell(
                onTap: () {
                  setState(() {
                    _selectedDeliveryTime = option;
                    _showDeliveryTimeError = false;
                  });

                  // Call GetOrgerSummaryEvent with delivery_time_id to refresh order summary
                  Map<String, dynamic> requestData = {
                    "chef_id": widget.chef_id.toString(),
                    "delivery_time_id": option.id.toString(),
                  };

                  // Include use_wallet_credits if wallet credits are enabled
                  if (_useWalletCredits) {
                    requestData["use_wallet_credits"] = true;
                  }

                  context
                      .read<OrderBloc>()
                      .add(GetOrgerSummaryEvent(requestData));
                },
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: screenSize.width * 0.04,
                    vertical: screenSize.height * 0.015,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: _selectedDeliveryTime?.id == option.id
                          ? const Color(0xFF1F2122)
                          : _showDeliveryTimeError
                              ? Colors.red
                              : const Color(0xFFE1E3E6),
                    ),
                    color: _selectedDeliveryTime?.id == option.id
                        ? const Color(0xFFF1F2F3)
                        : Colors.white,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            option.name ?? '',
                            style: TextStyle(
                              fontSize:
                                  isLargeScreen ? 18 : screenSize.width * 0.04,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          if (option.description != null)
                            SizedBox(
                              width: MediaQuery.of(context).size.width * 0.6,
                              child: Text(
                                option.description!,
                                style: TextStyle(
                                  fontSize: isLargeScreen
                                      ? 16
                                      : screenSize.width * 0.035,
                                  color: const Color(0xFF66696D),
                                ),
                              ),
                            ),
                        ],
                      ),
                      Text(
                        '+\$${double.tryParse(option.cost ?? '0')?.toStringAsFixed(2) ?? '0.00'}',
                        style: TextStyle(
                          fontSize:
                              isLargeScreen ? 18 : screenSize.width * 0.04,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            )),
        if (_showDeliveryTimeError)
          Padding(
            padding: EdgeInsets.only(top: screenSize.height * 0.01),
            child: Text(
              'Please select a delivery time',
              style: TextStyle(
                color: Colors.red,
                fontSize: isLargeScreen ? 14 : screenSize.width * 0.03,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildPlaceOrderButton(Size screenSize, bool isLargeScreen) {
    final hasError = orderSummaryData?.errorMessage != null &&
        orderSummaryData!.errorMessage!.isNotEmpty;
    final isDisabled = _isPlacingOrder || hasError;

    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          height: isLargeScreen ? 60 : screenSize.height * 0.06,
          child: ElevatedButton(
            onPressed: isDisabled ? null : _saveDeliveryDetails,
            style: ElevatedButton.styleFrom(
              backgroundColor: isDisabled
                  ? const Color(0xFFE1E3E6)
                  : const Color(0xFF1F2122),
              foregroundColor:
                  isDisabled ? const Color(0xFF66696D) : Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(28),
              ),
              padding: EdgeInsets.symmetric(
                horizontal: _isPlacingOrder
                    ? screenSize.width * 0.08
                    : screenSize.width * 0.05,
                vertical: screenSize.height * 0.02,
              ),
            ),
            child: _isPlacingOrder
                ? SizedBox(
                    width: isLargeScreen ? 24 : screenSize.width * 0.05,
                    height: isLargeScreen ? 24 : screenSize.width * 0.05,
                    child: const CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : Text(
                    'Place Order',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w400,
                      fontSize: isLargeScreen ? 16 : screenSize.width * 0.035,
                      color:
                          isDisabled ? const Color(0xFF66696D) : Colors.white,
                    ),
                  ),
          ),
        ),
      ],
    );
  }

  // Widget _buildOrderTotalSection(Size screenSize, bool isLargeScreen) {
  //   return Column(
  //     children: [
  //       Row(
  //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //         children: [
  //           Text(
  //             'Order Total',
  //             style: TextStyle(
  //               fontFamily: 'Inter',
  //               fontSize: isLargeScreen ? 18 : screenSize.width * 0.04,
  //               fontWeight: FontWeight.w500,
  //               color: const Color(0xFF1F2122),
  //             ),
  //           ),
  //           // Text(
  //           //   '\$${orderSummaryData?.totalPrice?.toStringAsFixed(2) ?? "0.00"}',
  //           //   style: TextStyle(
  //           //     fontFamily: 'Inter',
  //           //     fontSize: isLargeScreen ? 18 : screenSize.width * 0.04,
  //           //     fontWeight: FontWeight.w600,
  //           //     color: const Color(0xFF1F2122),
  //           //   ),
  //           // ),
  //         ],
  //       ),
  //       SizedBox(height: screenSize.height * 0.015),
  //       Row(
  //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //         children: [
  //           Text(
  //             'Subtotal',
  //             style: TextStyle(
  //               fontFamily: 'Inter',
  //               fontSize: isLargeScreen ? 16 : screenSize.width * 0.035,
  //               fontWeight: FontWeight.w400,
  //               color: const Color(0xFF1F2122),
  //             ),
  //           ),
  //           Text(
  //             orderSummaryData == null
  //                 ? '...'
  //                 : '\$${orderSummaryData!.totalPrice?.toStringAsFixed(2) ?? "0.00"}',
  //             style: TextStyle(
  //               fontFamily: 'Inter',
  //               fontSize: isLargeScreen ? 14 : screenSize.width * 0.03,
  //               fontWeight: FontWeight.w400,
  //               color: const Color(0xFF414346),
  //             ),
  //           ),
  //         ],
  //       ),
  //       SizedBox(height: screenSize.height * 0.01),
  //       Row(
  //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //         children: [
  //           Text(
  //             'Delivery fee',
  //             style: TextStyle(
  //               fontFamily: 'Inter',
  //               fontSize: isLargeScreen ? 16 : screenSize.width * 0.035,
  //               fontWeight: FontWeight.w400,
  //               color: const Color(0xFF1F2122),
  //             ),
  //           ),
  //           Text(
  //             orderSummaryData == null
  //                 ? '...'
  //                 : '\$${orderSummaryData!.deliveryFee?.toStringAsFixed(2) ?? "0.00"}',
  //             style: TextStyle(
  //               fontFamily: 'Inter',
  //               fontSize: isLargeScreen ? 14 : screenSize.width * 0.03,
  //               fontWeight: FontWeight.w400,
  //               color: const Color(0xFF414346),
  //             ),
  //           ),
  //         ],
  //       ),
  //       SizedBox(height: screenSize.height * 0.01),
  //       Row(
  //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //         children: [
  //           Text(
  //             'Service Fee',
  //             style: TextStyle(
  //               fontFamily: 'Inter',
  //               fontSize: isLargeScreen ? 16 : screenSize.width * 0.035,
  //               fontWeight: FontWeight.w400,
  //               color: const Color(0xFF1F2122),
  //             ),
  //           ),
  //           Text(
  //             orderSummaryData == null
  //                 ? '...'
  //                 : '\$${orderSummaryData!.serviceFee?.toStringAsFixed(2) ?? "0.00"}',
  //             style: TextStyle(
  //               fontFamily: 'Inter',
  //               fontSize: isLargeScreen ? 14 : screenSize.width * 0.03,
  //               fontWeight: FontWeight.w400,
  //               color: const Color(0xFF414346),
  //             ),
  //           ),
  //         ],
  //       ),
  //       SizedBox(height: screenSize.height * 0.01),
  //       Row(
  //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //         children: [
  //           Text(
  //             'DB Wallet Credits',
  //             style: TextStyle(
  //               fontFamily: 'Inter',
  //               fontSize: isLargeScreen ? 16 : screenSize.width * 0.035,
  //               fontWeight: FontWeight.w400,
  //               color: const Color(0xFF1F2122),
  //             ),
  //           ),
  //           Text(
  //             '-\$${orderSummaryData?.walletCredits?.toStringAsFixed(2) ?? "_"}',
  //             style: TextStyle(
  //               fontFamily: 'Inter',
  //               fontSize: isLargeScreen ? 16 : screenSize.width * 0.035,
  //               fontWeight: FontWeight.w500,
  //               color: const Color(0xFFD31510),
  //             ),
  //           ),
  //         ],
  //       ),
  //       SizedBox(height: screenSize.height * 0.01),
  //       Row(
  //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //         children: [
  //           Text(
  //             'Discounts',
  //             style: TextStyle(
  //               fontFamily: 'Inter',
  //               fontSize: isLargeScreen ? 16 : screenSize.width * 0.035,
  //               fontWeight: FontWeight.w400,
  //               color: const Color(0xFF1F2122),
  //             ),
  //           ),
  //           Text(
  //             '-\$${orderSummaryData?.discount?.toStringAsFixed(2) ?? "_"}',
  //             style: TextStyle(
  //               fontFamily: 'Inter',
  //               fontSize: isLargeScreen ? 16 : screenSize.width * 0.035,
  //               fontWeight: FontWeight.w500,
  //               color: const Color(0xFFD31510),
  //             ),
  //           ),
  //         ],
  //       ),
  //       SizedBox(height: screenSize.height * 0.01),
  //       Row(
  //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //         children: [
  //           Text(
  //             'Sales Tax',
  //             style: TextStyle(
  //               fontFamily: 'Inter',
  //               fontSize: isLargeScreen ? 16 : screenSize.width * 0.035,
  //               fontWeight: FontWeight.w400,
  //               color: const Color(0xFF1F2122),
  //             ),
  //           ),
  //           Text(
  //             '\$${orderSummaryData?.taxesAndFees?.toStringAsFixed(2) ?? "_"}',
  //             style: TextStyle(
  //               fontFamily: 'Inter',
  //               fontSize: isLargeScreen ? 16 : screenSize.width * 0.035,
  //               fontWeight: FontWeight.w500,
  //               color: const Color(0xFF1F2122),
  //             ),
  //           ),
  //         ],
  //       ),
  //       SizedBox(height: screenSize.height * 0.01),
  //       DottedDivider(),
  //       SizedBox(height: screenSize.height * 0.01),
  //       Row(
  //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //         children: [
  //           Text(
  //             'Total',
  //             style: TextStyle(
  //               fontFamily: 'Inter',
  //               fontSize: isLargeScreen ? 18 : screenSize.width * 0.04,
  //               fontWeight: FontWeight.w600,
  //               color: const Color(0xFF1F2122),
  //             ),
  //           ),
  //           Text(
  //             orderSummaryData == null
  //                 ? '...'
  //                 : '\$${orderSummaryData!.finalTotal?.toStringAsFixed(2) ?? "_"}',
  //             style: TextStyle(
  //               fontFamily: 'Inter',
  //               fontSize: isLargeScreen ? 18 : screenSize.width * 0.04,
  //               fontWeight: FontWeight.w600,
  //               color: const Color(0xFF1F2122),
  //             ),
  //           ),
  //         ],
  //       ),
  //       // Show error message if present
  //       if (orderSummaryData?.errorMessage != null &&
  //           orderSummaryData!.errorMessage!.isNotEmpty) ...[
  //         SizedBox(height: screenSize.height * 0.015),
  //         Container(
  //           width: double.infinity,
  //           padding: EdgeInsets.symmetric(
  //             horizontal: screenSize.width * 0.04,
  //             vertical: screenSize.height * 0.015,
  //           ),
  //           decoration: BoxDecoration(
  //             color: const Color(0xFFFFEBEE),
  //             borderRadius: BorderRadius.circular(8),
  //             border: Border.all(color: const Color(0xFFE57373)),
  //           ),
  //           child: Row(
  //             children: [
  //               Icon(
  //                 Icons.error_outline,
  //                 color: const Color(0xFFD32F2F),
  //                 size: isLargeScreen ? 18 : screenSize.width * 0.04,
  //               ),
  //               SizedBox(width: screenSize.width * 0.02),
  //               Expanded(
  //                 child: Text(
  //                   orderSummaryData!.errorMessage!,
  //                   style: TextStyle(
  //                     fontFamily: 'Inter',
  //                     fontSize: isLargeScreen ? 14 : screenSize.width * 0.035,
  //                     fontWeight: FontWeight.w500,
  //                     color: const Color(0xFFD32F2F),
  //                   ),
  //                 ),
  //               ),
  //             ],
  //           ),
  //         ),
  //       ],
  //     ],
  //   );
  // }
  Widget _buildOrderTotalSection(Size screenSize, bool isLargeScreen) {
    return Column(
      children: [
        SizedBox(height: screenSize.height * 0.015),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Subtotal',
              style: TextStyle(
                fontFamily: 'Inter',
                fontSize: isLargeScreen ? 16 : screenSize.width * 0.035,
                fontWeight: FontWeight.w400,
                color: const Color(0xFF1F2122),
              ),
            ),
            BlocBuilder<OrderBloc, OrderState>(
              builder: (context, state) {
                if (state is GetOrgerSummaryLoading) {
                  return _buildLoadingShimmer(screenSize);
                }
                return Text(
                  orderSummaryData == null
                      ? '...'
                      : '\$${orderSummaryData!.totalPrice?.toStringAsFixed(2) ?? "_"}',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontSize: isLargeScreen ? 14 : screenSize.width * 0.03,
                    fontWeight: FontWeight.w400,
                    color: const Color(0xFF414346),
                  ),
                );
              },
            ),
          ],
        ),
        SizedBox(height: screenSize.height * 0.01),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Delivery fee',
              style: TextStyle(
                fontFamily: 'Inter',
                fontSize: isLargeScreen ? 16 : screenSize.width * 0.035,
                fontWeight: FontWeight.w400,
                color: const Color(0xFF1F2122),
              ),
            ),
            BlocBuilder<OrderBloc, OrderState>(
              builder: (context, state) {
                if (state is GetOrgerSummaryLoading) {
                  return _buildLoadingShimmer(screenSize);
                }
                return Text(
                  orderSummaryData == null
                      ? '...'
                      : '\$${orderSummaryData!.deliveryFee?.toStringAsFixed(2) ?? "_"}',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontSize: isLargeScreen ? 14 : screenSize.width * 0.03,
                    fontWeight: FontWeight.w400,
                    color: const Color(0xFF414346),
                  ),
                );
              },
            ),
          ],
        ),
        SizedBox(height: screenSize.height * 0.01),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Service Fee',
              style: TextStyle(
                fontFamily: 'Inter',
                fontSize: isLargeScreen ? 16 : screenSize.width * 0.035,
                fontWeight: FontWeight.w400,
                color: const Color(0xFF1F2122),
              ),
            ),
            BlocBuilder<OrderBloc, OrderState>(
              builder: (context, state) {
                if (state is GetOrgerSummaryLoading) {
                  return _buildLoadingShimmer(screenSize);
                }
                return Text(
                  orderSummaryData == null
                      ? '...'
                      : '\$${orderSummaryData!.serviceFee?.toStringAsFixed(2) ?? "_"}',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontSize: isLargeScreen ? 14 : screenSize.width * 0.03,
                    fontWeight: FontWeight.w400,
                    color: const Color(0xFF414346),
                  ),
                );
              },
            ),
          ],
        ),
        SizedBox(height: screenSize.height * 0.01),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'DB Wallet Credits',
              style: TextStyle(
                fontFamily: 'Inter',
                fontSize: isLargeScreen ? 16 : screenSize.width * 0.035,
                fontWeight: FontWeight.w400,
                color: const Color(0xFF1F2122),
              ),
            ),
            BlocBuilder<OrderBloc, OrderState>(
              builder: (context, state) {
                if (state is GetOrgerSummaryLoading) {
                  return _buildLoadingShimmer(screenSize);
                }
                return Text(
                  '-\$${orderSummaryData?.walletCredits?.toStringAsFixed(2) ?? "_"}',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontSize: isLargeScreen ? 16 : screenSize.width * 0.035,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFFD31510),
                  ),
                );
              },
            ),
          ],
        ),
        SizedBox(height: screenSize.height * 0.01),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Discounts',
              style: TextStyle(
                fontFamily: 'Inter',
                fontSize: isLargeScreen ? 16 : screenSize.width * 0.035,
                fontWeight: FontWeight.w400,
                color: const Color(0xFF1F2122),
              ),
            ),
            BlocBuilder<OrderBloc, OrderState>(
              builder: (context, state) {
                if (state is GetOrgerSummaryLoading) {
                  return _buildLoadingShimmer(screenSize);
                }
                return Text(
                  '-\$${orderSummaryData?.discount?.toStringAsFixed(2) ?? "_"}',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontSize: isLargeScreen ? 16 : screenSize.width * 0.035,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFFD31510),
                  ),
                );
              },
            ),
          ],
        ),
        SizedBox(height: screenSize.height * 0.01),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Sales Tax',
              style: TextStyle(
                fontFamily: 'Inter',
                fontSize: isLargeScreen ? 16 : screenSize.width * 0.035,
                fontWeight: FontWeight.w400,
                color: const Color(0xFF1F2122),
              ),
            ),
            BlocBuilder<OrderBloc, OrderState>(
              builder: (context, state) {
                if (state is GetOrgerSummaryLoading) {
                  return _buildLoadingShimmer(screenSize);
                }
                return Text(
                  '\$${orderSummaryData?.taxesAndFees?.toStringAsFixed(2) ?? "_"}',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontSize: isLargeScreen ? 16 : screenSize.width * 0.035,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFF1F2122),
                  ),
                );
              },
            ),
          ],
        ),
        SizedBox(height: screenSize.height * 0.01),
        DottedDivider(),
        SizedBox(height: screenSize.height * 0.01),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Total',
              style: TextStyle(
                fontFamily: 'Inter',
                fontSize: isLargeScreen ? 18 : screenSize.width * 0.04,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF1F2122),
              ),
            ),
            BlocBuilder<OrderBloc, OrderState>(
              builder: (context, state) {
                if (state is GetOrgerSummaryLoading) {
                  return _buildLoadingShimmer(screenSize);
                }
                return Text(
                  orderSummaryData == null
                      ? '...'
                      : '\$${orderSummaryData!.finalTotal?.toStringAsFixed(2) ?? "_"}',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontSize: isLargeScreen ? 18 : screenSize.width * 0.04,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF1F2122),
                  ),
                );
              },
            ),
          ],
        ),
        // Show error message if present
        if (orderSummaryData?.errorMessage != null &&
            orderSummaryData!.errorMessage!.isNotEmpty) ...[
          SizedBox(height: screenSize.height * 0.015),
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(
              horizontal: screenSize.width * 0.04,
              vertical: screenSize.height * 0.015,
            ),
            decoration: BoxDecoration(
              color: const Color(0xFFFFEBEE),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: const Color(0xFFE57373)),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.error_outline,
                  color: const Color(0xFFD32F2F),
                  size: isLargeScreen ? 18 : screenSize.width * 0.04,
                ),
                SizedBox(width: screenSize.width * 0.02),
                Expanded(
                  child: Text(
                    orderSummaryData!.errorMessage!,
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontSize: isLargeScreen ? 14 : screenSize.width * 0.035,
                      fontWeight: FontWeight.w500,
                      color: const Color(0xFFD32F2F),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  String _formatDisplayDate(DateTime date) {
    final now = DateTime.now();
    if (date.year == now.year &&
        date.month == now.month &&
        date.day == now.day) {
      return 'Today';
    }
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  Widget _buildDeliveryTimeText(Size screenSize, bool isLargeScreen) {
    final startTime = orderSummaryData?.customerTimePreference?.startTime;
    final isLoading = orderSummaryData == null;

    return Row(
      children: [
        GestureDetector(
          onTap: isLoading ? null : () => _selectDate(context),
          child: isLoading
              ? Row(
                  children: [
                    SizedBox(
                      width: isLargeScreen ? 16 : screenSize.width * 0.035,
                      height: isLargeScreen ? 16 : screenSize.width * 0.035,
                      child: CircularProgressIndicator(
                        strokeWidth: 1,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          const Color(0xFF1F2122),
                        ),
                      ),
                    ),
                    SizedBox(width: screenSize.width * 0.02),
                    Text(
                      'Loading delivery time...',
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontSize: isLargeScreen ? 16 : screenSize.width * 0.035,
                        fontWeight: FontWeight.w500,
                        height: 1.43,
                        color: const Color(0xFF66696D),
                      ),
                    ),
                  ],
                )
              : Text(
                  '${_formatDisplayDate(selectedDate)}, ${selectedTime.isNotEmpty ? _formatTimeFromString(selectedTime) : _formatTimeFromString(startTime)}',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontSize: isLargeScreen ? 16 : screenSize.width * 0.035,
                    fontWeight: FontWeight.w500,
                    height: 1.43,
                    color: const Color(0xFF1F2122),
                  ),
                ),
        ),
        if (!isLoading) ...[
          SizedBox(width: screenSize.width * 0.01),
          Image.asset(
            'assets/icons/chevron-down.png',
            width: isLargeScreen ? 18 : screenSize.width * 0.04,
            height: isLargeScreen ? 18 : screenSize.width * 0.04,
            color: const Color(0xFF1F2122),
          ),
        ],
        if (!isLoading)
          TextButton(
              onPressed: () {
                _selectDate(context).then((_) {
                  _selectTime(context);
                });
              },
              style: TextButton.styleFrom(
                padding: EdgeInsets.zero,
                minimumSize:
                    Size(screenSize.width * 0.075, screenSize.height * 0.025),
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
              child: IntrinsicWidth(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      'Edit',
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w400,
                        fontSize: isLargeScreen ? 14 : screenSize.width * 0.03,
                        height: 1.0,
                        color: Color(0xFF1F2122),
                        letterSpacing: 0.5,
                        decoration:
                            TextDecoration.none, // remove default underline
                      ),
                    ),
                    SizedBox(height: 2), // spacing between text and line
                    Container(
                      height:
                          1, // thickness of underline (same as decorationThickness)
                      width: double.infinity, // expands to match text width
                      color: Color(0xFF1F2122), // underline color
                    ),
                  ],
                ),
              )),
      ],
    );
  }

  Widget _buildDabbaWalletToggle(Size screenSize, bool isLargeScreen) {
    final hasWalletCredits = (orderSummaryData?.walletBalance ?? 0) > 0;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            Container(
              width: isLargeScreen ? 28 : screenSize.width * 0.06,
              height: isLargeScreen ? 20 : screenSize.width * 0.045,
              decoration: BoxDecoration(
                color: const Color(0xFF1F2122),
                borderRadius: BorderRadius.circular(4),
              ),
              alignment: Alignment.center,
              child: Text(
                'DB',
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontSize: isLargeScreen ? 12 : screenSize.width * 0.025,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
            SizedBox(width: screenSize.width * 0.025),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Dabba Wallet Credits',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontSize: isLargeScreen ? 16 : screenSize.width * 0.035,
                    fontWeight: FontWeight.w400,
                    color: const Color(0xFF1F2122),
                  ),
                ),
                Text(
                  '\$ ${orderSummaryData?.walletBalance?.toStringAsFixed(2) ?? "0.00"}',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontSize: isLargeScreen ? 14 : screenSize.width * 0.03,
                    fontWeight: FontWeight.w400,
                    color: const Color(0xFF66696D),
                  ),
                ),
              ],
            ),
          ],
        ),
        GestureDetector(
          onTap: () {
            if (!hasWalletCredits) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('No available credits in your Dabba wallet'),
                  backgroundColor: Color(0xFFE11900),
                  duration: Duration(seconds: 2),
                ),
              );
              return;
            }
            setState(() {
              _useWalletCredits = !_useWalletCredits;
            });

            // Call GetOrgerSummaryEvent with use_wallet_credits to refresh order summary
            Map<String, dynamic> requestData = {
              "chef_id": widget.chef_id.toString(),
              "use_wallet_credits": !_useWalletCredits,
            };

            // Include delivery_time_id if a delivery time is selected
            if (_selectedDeliveryTime != null) {
              requestData["delivery_time_id"] =
                  _selectedDeliveryTime!.id.toString();
            }

            context.read<OrderBloc>().add(GetOrgerSummaryEvent(requestData));
          },
          child: Opacity(
            opacity: hasWalletCredits ? 1.0 : 0.5,
            child: CustomToggle(
              value: _useWalletCredits,
              onChanged:
                  (_) {}, // Empty callback since we're handling tap in GestureDetector
            ),
          ),
        ),
      ],
    );
  }

  void _showPaymentSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          contentPadding: EdgeInsets.all(16),
          content: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                alignment: Alignment.center,
                width: 220,
                child: Lottie.asset(
                  'assets/success.json',
                  repeat: true,
                  animate: true,
                ),
              ),
              const Text(
                'Payment Successful!',
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF1F2122),
                ),
              ),
              const SizedBox(height: 12),
              const Text(
                'Your order has been placed successfully. \nWe\'re getting your order ready!',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  color: Color(0xFF66696D),
                ),
              ),
              const SizedBox(height: 24),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop(); // Close dialog
                    Navigator.pushAndRemoveUntil(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const MainNavigationScreen(),
                      ),
                      (route) => false,
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF1F2122),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(28),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 14),
                  ),
                  child: const Text(
                    'Continue',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w500,
                      fontSize: 14,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _showPaymentFailedDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          contentPadding: EdgeInsets.all(16),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                width: 220,
                alignment: Alignment.center,
                child: Lottie.asset(
                  'assets/failed.json',
                  repeat: true,
                  animate: true,
                ),
              ),
              const Text(
                'Payment Failed!',
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF1F2122),
                ),
              ),
              const SizedBox(height: 12),
              const Text(
                'Your payment could not be processed. Please try again or use a different payment method.',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  color: Color(0xFF66696D),
                ),
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        Navigator.of(context).pop(); // Close dialog only
                      },
                      style: OutlinedButton.styleFrom(
                        side: const BorderSide(color: Color(0xFF1F2122)),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(28),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 14),
                      ),
                      child: const Text(
                        'Cancel',
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontWeight: FontWeight.w500,
                          fontSize: 16,
                          color: Color(0xFF1F2122),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isLargeScreen = screenSize.width > 600;

    return MultiBlocListener(
      listeners: [
        BlocListener<AccountBloc, AccountState>(
          listener: (context, state) {
            if (state is ListAddressesSuccess) {
              setState(() {
                currentAddress = _getCurrentAddress(state.data);
              });
            }
          },
        ),
        BlocListener<MealplanBloc, MealPlanState>(
          listener: (context, state) {
            if (state is MealPlanProgressSuccess) {
              setState(() {
                _dropOffInstructions = _instructionsController.text;
              });
            } else if (state is ListDropoffOptionSuccess) {
              final dropoffData = state.data as DropoffOptionsModel;
              setState(() {
                _dropoffOptions = dropoffData.data?.data ?? [];
              });
            } else if (state is ListDeliveryTimeSuccess) {
              final deliveryTimeData = state.data as DeliveryTimeModel;
              setState(() {
                _deliveryTimeOptions = deliveryTimeData.data?.data ?? [];
              });
            } else if (state is Step7MealPlanLoading) {
              setState(() {
                _isPlacingOrder = true;
              });
            } else if (state is Step7MealPlanSuccess) {
              setState(() {
                _isPlacingOrder = false;
              });
              Navigator.pushAndRemoveUntil(
                context,
                MaterialPageRoute(builder: (context) => const Home2()),
                (route) => false,
              );
            } else if (state is Step7MealPlanFailed) {
              setState(() {
                _isPlacingOrder = false;
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: const Color(0xFFE11900),
                ),
              );
            }
          },
        ),
        BlocListener<OrderBloc, OrderState>(
          listener: (context, state) {
            if (state is GetOrgerSummarySuccess) {
              setState(() {
                orderSummaryData = state.data;
              });
            } else if (state is CheckoutOrderLoading) {
              setState(() {
                _isPlacingOrder = true;
              });
            } else if (state is CheckoutOrderSuccess) {
              String paymentUrl = state.paymenturl ?? "";
              String orderId = state.orderId ?? "";
              num total = state.total ?? 0;

              setState(() {
                _isPlacingOrder = false;
              });

              total > 0
                  ? Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => PaymentWebView(
                          paymentUrl: paymentUrl,
                          onPaymentComplete: () {
                            _showPaymentSuccessDialog(); // Show Lottie success dialog
                          },
                          onPaymentCancelled: () {
                            _showPaymentFailedDialog(); // Show Lottie failure dialog
                          },
                        ),
                      ),
                    )
                  :
                  // Show success snackbar

                  showDialog(
                      context: context,
                      barrierDismissible: false,
                      builder: (BuildContext context) {
                        return AlertDialog(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                            contentPadding: EdgeInsets.all(16),
                            content: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Container(
                                  alignment: Alignment.center,
                                  width: 220, // Adjust the width as needed
                                  // height: 150, // Adjust the height as needed

                                  child: Lottie.asset(
                                    'assets/success.json',
                                    fit: BoxFit.cover,
                                    repeat: true,
                                    animate: true,
                                  ),
                                ),
                                const Text(
                                  'Order Placed Successfully!',
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontSize: 20,
                                    fontWeight: FontWeight.w600,
                                    color: Color(0xFF1F2122),
                                  ),
                                ),
                                const SizedBox(height: 12),
                                const Text(
                                  'Your order has been placed successfully. \nWe\'re getting your order ready!',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400,
                                    color: Color(0xFF66696D),
                                  ),
                                ),
                                const SizedBox(height: 24),
                                SizedBox(
                                  width: double.infinity,
                                  child: ElevatedButton(
                                    onPressed: () {
                                      Navigator.of(context)
                                          .pop(); // Close dialog
                                      Navigator.pushAndRemoveUntil(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) =>
                                              const MainNavigationScreen(),
                                        ),
                                        (route) => false,
                                      );
                                    },
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: const Color(0xFF1F2122),
                                      foregroundColor: Colors.white,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(28),
                                      ),
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 14),
                                    ),
                                    child: const Text(
                                      'Continue',
                                      style: TextStyle(
                                        fontFamily: 'Inter',
                                        fontWeight: FontWeight.w500,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ));
                      },
                    );

              // Navigate to main page after a short dela
            } else if (state is CheckoutOrderFailed) {
              setState(() {
                _isPlacingOrder = false;
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: const Color(0xFFE11900),
                ),
              );
            }
          },
        ),
      ],
      child: BlocBuilder<MealplanBloc, MealPlanState>(
        builder: (context, state) {
          return Scaffold(
            backgroundColor: const Color(0xFFF6F3EC),
            appBar: AppBar(
              backgroundColor: const Color(0xFFF6F3EC),
              scrolledUnderElevation: 0,
              surfaceTintColor: Colors.transparent,
              centerTitle: false,
              elevation: 0,
              automaticallyImplyLeading: false,
              leading: IconButton(
                icon: Icon(
                  Icons.arrow_back,
                  color: const Color(0xFF1F2122),
                  size: isLargeScreen ? 32 : screenSize.width * 0.06,
                ),
                onPressed: () => Navigator.of(context).pop(),
              ),
              title: Text(
                'Checkout',
                style: TextStyle(
                  color: const Color(0xFF1F2122),
                  fontSize: isLargeScreen ? 20 : screenSize.width * 0.045,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Inter',
                  height: 1.24,
                ),
              ),
            ),
            body: SafeArea(
              child: SingleChildScrollView(
                padding: EdgeInsets.all(screenSize.width * 0.04),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            blurRadius: 2,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                      padding: EdgeInsets.all(screenSize.width * 0.04),
                      child: state is MealPlanProgressLoading
                          ? Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: List.generate(
                                5,
                                (index) => Padding(
                                  padding: EdgeInsets.symmetric(
                                    vertical: screenSize.height * 0.01,
                                  ),
                                  child: _buildLoadingShimmer(screenSize),
                                ),
                              ),
                            )
                          : Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Delivery Details',
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontSize: isLargeScreen
                                        ? 26
                                        : screenSize.width * 0.05,
                                    fontWeight: FontWeight.w600,
                                    height: 1.24,
                                    color: const Color(0xFF1F2122),
                                  ),
                                ),
                                SizedBox(height: screenSize.height * 0.02),
                                Text(
                                  'Address',
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontSize: isLargeScreen
                                        ? 16
                                        : screenSize.width * 0.035,
                                    fontWeight: FontWeight.w500,
                                    height: 1.14,
                                    letterSpacing: 0.28,
                                    color: const Color(0xFF1F2122),
                                  ),
                                ),
                                SizedBox(height: screenSize.height * 0.018),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Expanded(
                                      child: Row(
                                        children: [
                                          Icon(
                                            Icons.location_on_outlined,
                                            size: isLargeScreen
                                                ? 14
                                                : screenSize.width * 0.035,
                                            color: const Color(0xFF414346),
                                          ),
                                          SizedBox(
                                              width: screenSize.width * 0.02),
                                          Expanded(
                                            child: Text(
                                              currentAddress ?? 'No address',
                                              style: TextStyle(
                                                fontFamily: 'Inter',
                                                fontSize: isLargeScreen
                                                    ? 16
                                                    : screenSize.width * 0.035,
                                                fontWeight: FontWeight.w500,
                                                height: 1.43,
                                                color: const Color(0xFF1F2122),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    TextButton(
                                        onPressed: () async {
                                          final selectedAddress =
                                              await Navigator.push<AddressData>(
                                            context,
                                            MaterialPageRoute(
                                              builder: (context) =>
                                                  SelectAddressPage(
                                                currentAddress:
                                                    currentAddressData,
                                              ),
                                            ),
                                          );

                                          if (selectedAddress != null) {
                                            setState(() {
                                              currentAddressData =
                                                  selectedAddress;
                                              currentAddress =
                                                  selectedAddress.addressText ??
                                                      'No address';
                                            });
                                          }
                                        },
                                        style: TextButton.styleFrom(
                                          padding: EdgeInsets.zero,
                                          minimumSize: Size(
                                              screenSize.width * 0.075,
                                              screenSize.height * 0.025),
                                          tapTargetSize:
                                              MaterialTapTargetSize.shrinkWrap,
                                        ),
                                        child: IntrinsicWidth(
                                          child: Column(
                                            mainAxisSize: MainAxisSize.min,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.center,
                                            children: [
                                              Text(
                                                'Change',
                                                style: TextStyle(
                                                  fontFamily: 'Inter',
                                                  fontWeight: FontWeight.w600,
                                                  fontSize: isLargeScreen
                                                      ? 14
                                                      : screenSize.width * 0.03,
                                                  height: 1.0,
                                                  color: Color(0xFF1F2122),
                                                  letterSpacing: 0.5,
                                                  decoration: TextDecoration
                                                      .none, // Disable default underline
                                                ),
                                              ),
                                              SizedBox(
                                                  height:
                                                      1), // Spacing between text and underline
                                              Container(
                                                height:
                                                    1, // Thickness of underline
                                                width: double
                                                    .infinity, // Matches text width inside IntrinsicWidth
                                                color: Color(0xFF1F2122),
                                              ),
                                            ],
                                          ),
                                        )),
                                  ],
                                ),
                                SizedBox(height: screenSize.height * 0.02),
                                Text(
                                  'Delivery',
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontSize: isLargeScreen
                                        ? 16
                                        : screenSize.width * 0.035,
                                    fontWeight: FontWeight.w500,
                                    height: 1.14,
                                    letterSpacing: 0.28,
                                    color: const Color(0xFF1F2122),
                                  ),
                                ),
                                SizedBox(height: screenSize.height * 0.018),
                                Row(
                                  children: [
                                    Icon(
                                      Icons.access_time_rounded,
                                      size: isLargeScreen
                                          ? 14
                                          : screenSize.width * 0.035,
                                      color: const Color(0xFF1F2122),
                                    ),
                                    SizedBox(width: screenSize.width * 0.02),
                                    _buildDeliveryTimeText(
                                        screenSize, isLargeScreen),
                                  ],
                                ),
                                SizedBox(height: screenSize.height * 0.02),
                                Divider(
                                  color: const Color(0xFFE1E3E6),
                                  thickness: 1,
                                ),
                                SizedBox(height: screenSize.height * 0.02),
                                _buildDropoffOptionDropdown(
                                    screenSize, isLargeScreen),
                                SizedBox(height: screenSize.height * 0.02),
                                Text(
                                  'Drop-Off Instructions',
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontSize: isLargeScreen
                                        ? 16
                                        : screenSize.width * 0.035,
                                    fontWeight: FontWeight.w500,
                                    height: 1.14,
                                    letterSpacing: 0.28,
                                    color: const Color(0xFF1F2122),
                                  ),
                                ),
                                SizedBox(height: screenSize.height * 0.01),
                                TextField(
                                  controller: _instructionsController,
                                  onChanged: (value) {
                                    _dropOffInstructions = value;
                                  },
                                  textAlign: TextAlign.left,
                                  decoration: InputDecoration(
                                    contentPadding:
                                        EdgeInsets.all(screenSize.width * 0.03),
                                    hintText: 'Add drop-off instructions...',
                                    hintStyle: TextStyle(
                                      fontFamily: 'Inter',
                                      fontWeight: FontWeight.w400,
                                      fontSize: isLargeScreen
                                          ? 18
                                          : screenSize.width * 0.04,
                                      height: 1.5,
                                      color: const Color(0xFF66696D),
                                    ),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8),
                                      borderSide: const BorderSide(
                                          color: Color(0xFFE1E3E6)),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8),
                                      borderSide: const BorderSide(
                                          color: Color(0xFFE1E3E6)),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8),
                                      borderSide: const BorderSide(
                                        color: Color(0xFF1F2122),
                                        width: 1.5,
                                      ),
                                    ),
                                  ),
                                  minLines: 5,
                                  maxLines: 8,
                                  cursorColor: const Color(0xFF1F2122),
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontWeight: FontWeight.w400,
                                    fontSize: isLargeScreen
                                        ? 18
                                        : screenSize.width * 0.04,
                                    height: 1.5,
                                    color: const Color(0xFF66696D),
                                  ),
                                ),
                                SizedBox(height: screenSize.height * 0.02),
                                Divider(
                                  color: const Color(0xFFE1E3E6),
                                  thickness: 1,
                                ),
                                SizedBox(height: screenSize.height * 0.02),
                                _buildDeliveryTimeOptions(
                                    screenSize, isLargeScreen),
                              ],
                            ),
                    ),
                    // SizedBox(height: screenSize.height * 0.03),
                    // Container(
                    //   decoration: BoxDecoration(
                    //     color: Colors.white,
                    //     borderRadius: BorderRadius.circular(10),
                    //     boxShadow: [
                    //       BoxShadow(
                    //         color: Colors.black.withOpacity(0.05),
                    //         blurRadius: 2,
                    //         offset: const Offset(0, 1),
                    //       ),
                    //     ],
                    //   ),
                    //   padding: EdgeInsets.all(screenSize.width * 0.04),
                    //   child: state is MealPlanProgressLoading
                    //       ? Column(
                    //           crossAxisAlignment: CrossAxisAlignment.start,
                    //           children: List.generate(
                    //             4,
                    //             (index) => Padding(
                    //               padding: EdgeInsets.symmetric(
                    //                 vertical: screenSize.height * 0.01,
                    //               ),
                    //               child: _buildLoadingShimmer(screenSize),
                    //             ),
                    //           ),
                    //         )
                    //       : Column(
                    //           crossAxisAlignment: CrossAxisAlignment.start,
                    //           children: [
                    //             Text(
                    //               'Payment',
                    //               style: TextStyle(
                    //                 fontFamily: 'Inter',
                    //                 fontSize: isLargeScreen
                    //                     ? 26
                    //                     : screenSize.width * 0.05,
                    //                 fontWeight: FontWeight.w600,
                    //                 height: 1.24,
                    //                 color: const Color(0xFF1F2122),
                    //               ),
                    //             ),
                    //             SizedBox(height: screenSize.height * 0.02),
                    //             Row(
                    //               mainAxisAlignment:
                    //                   MainAxisAlignment.spaceBetween,
                    //               children: [
                    //                 Row(
                    //                   children: [
                    //                     Container(
                    //                       width: isLargeScreen
                    //                           ? 36
                    //                           : screenSize.width * 0.08,
                    //                       height: isLargeScreen
                    //                           ? 24
                    //                           : screenSize.width * 0.05,
                    //                       decoration: BoxDecoration(
                    //                         borderRadius:
                    //                             BorderRadius.circular(4),
                    //                       ),
                    //                       child: Image.asset(
                    //                         'assets/icons/Visa.png',
                    //                         fit: BoxFit.contain,
                    //                       ),
                    //                     ),
                    //                     SizedBox(
                    //                         width: screenSize.width * 0.02),
                    //                     Column(
                    //                       crossAxisAlignment:
                    //                           CrossAxisAlignment.start,
                    //                       children: [
                    //                         Row(
                    //                           children: [
                    //                             Text(
                    //                               'Card ending with 0001',
                    //                               style: TextStyle(
                    //                                 fontFamily: 'Inter',
                    //                                 fontSize: isLargeScreen
                    //                                     ? 16
                    //                                     : screenSize.width *
                    //                                         0.035,
                    //                                 fontWeight: FontWeight.w400,
                    //                                 color:
                    //                                     const Color(0xFF1F2122),
                    //                               ),
                    //                             ),
                    //                             SizedBox(
                    //                                 width: screenSize.width *
                    //                                     0.02),
                    //                             Container(
                    //                               padding: EdgeInsets.symmetric(
                    //                                 horizontal:
                    //                                     screenSize.width * 0.02,
                    //                                 vertical:
                    //                                     screenSize.height *
                    //                                         0.005,
                    //                               ),
                    //                               decoration: BoxDecoration(
                    //                                 color:
                    //                                     const Color(0xFFCEF8E0),
                    //                                 borderRadius:
                    //                                     BorderRadius.circular(
                    //                                         10),
                    //                               ),
                    //                               child: Text(
                    //                                 'Default',
                    //                                 style: TextStyle(
                    //                                   fontFamily: 'Inter',
                    //                                   fontSize: isLargeScreen
                    //                                       ? 14
                    //                                       : screenSize.width *
                    //                                           0.03,
                    //                                   fontWeight:
                    //                                       FontWeight.w500,
                    //                                   height: 1.0,
                    //                                   letterSpacing: 0.24,
                    //                                   color: const Color(
                    //                                       0xFF007A4D),
                    //                                 ),
                    //                               ),
                    //                             ),
                    //                           ],
                    //                         ),
                    //                         Text(
                    //                           'Expires 03/2028',
                    //                           style: TextStyle(
                    //                             fontFamily: 'Inter',
                    //                             fontSize: isLargeScreen
                    //                                 ? 16
                    //                                 : screenSize.width * 0.035,
                    //                             fontWeight: FontWeight.w400,
                    //                             height: 1.43,
                    //                             color: const Color(0xFF1F2122),
                    //                           ),
                    //                         ),
                    //                       ],
                    //                     ),
                    //                   ],
                    //                 ),
                    //                 IconButton(
                    //                   icon: Icon(
                    //                     Icons.more_vert,
                    //                     color: const Color(0xFF1F2122),
                    //                     size: isLargeScreen
                    //                         ? 24
                    //                         : screenSize.width * 0.05,
                    //                   ),
                    //                   onPressed: () {},
                    //                   constraints: const BoxConstraints(),
                    //                   padding: EdgeInsets.zero,
                    //                 ),
                    //               ],
                    //             ),
                    //             Divider(
                    //               height: screenSize.height * 0.025,
                    //               color: const Color(0xFFE1E3E6),
                    //             ),
                    //             Row(
                    //               mainAxisAlignment:
                    //                   MainAxisAlignment.spaceBetween,
                    //               children: [
                    //                 Row(
                    //                   children: [
                    //                     Container(
                    //                       width: isLargeScreen
                    //                           ? 36
                    //                           : screenSize.width * 0.08,
                    //                       height: isLargeScreen
                    //                           ? 24
                    //                           : screenSize.width * 0.05,
                    //                       decoration: BoxDecoration(
                    //                         borderRadius:
                    //                             BorderRadius.circular(4),
                    //                       ),
                    //                       child: Image.asset(
                    //                         'assets/icons/db.png',
                    //                         fit: BoxFit.contain,
                    //                       ),
                    //                     ),
                    //                     SizedBox(
                    //                         width: screenSize.width * 0.02),
                    //                     Text(
                    //                       '\$ 200.00',
                    //                       style: TextStyle(
                    //                         fontFamily: 'Inter',
                    //                         fontSize: isLargeScreen
                    //                             ? 16
                    //                             : screenSize.width * 0.035,
                    //                         fontWeight: FontWeight.w400,
                    //                         height: 1.43,
                    //                         color: const Color(0xFF1F2122),
                    //                       ),
                    //                     ),
                    //                   ],
                    //                 ),
                    //                 IconButton(
                    //                   icon: Icon(
                    //                     Icons.more_vert,
                    //                     color: const Color(0xFF1F2122),
                    //                     size: isLargeScreen
                    //                         ? 24
                    //                         : screenSize.width * 0.05,
                    //                   ),
                    //                   onPressed: () {},
                    //                   constraints: const BoxConstraints(),
                    //                   padding: EdgeInsets.zero,
                    //                 ),
                    //               ],
                    //             ),
                    //             Divider(
                    //               height: screenSize.height * 0.025,
                    //               color: const Color(0xFFE1E3E6),
                    //             ),
                    //             Row(
                    //               mainAxisAlignment:
                    //                   MainAxisAlignment.spaceBetween,
                    //               children: [
                    //                 Row(
                    //                   children: [
                    //                     Container(
                    //                       width: isLargeScreen
                    //                           ? 36
                    //                           : screenSize.width * 0.08,
                    //                       height: isLargeScreen
                    //                           ? 24
                    //                           : screenSize.width * 0.05,
                    //                       decoration: BoxDecoration(
                    //                         borderRadius:
                    //                             BorderRadius.circular(4),
                    //                       ),
                    //                       child: Image.asset(
                    //                         'assets/icons/Visa.png',
                    //                         fit: BoxFit.contain,
                    //                       ),
                    //                     ),
                    //                     SizedBox(
                    //                         width: screenSize.width * 0.02),
                    //                     Column(
                    //                       crossAxisAlignment:
                    //                           CrossAxisAlignment.start,
                    //                       children: [
                    //                         Text(
                    //                           'Card ending with 0001',
                    //                           style: TextStyle(
                    //                             fontFamily: 'Inter',
                    //                             fontSize: isLargeScreen
                    //                                 ? 16
                    //                                 : screenSize.width * 0.035,
                    //                             fontWeight: FontWeight.w400,
                    //                             height: 1.43,
                    //                             color: const Color(0xFF1F2122),
                    //                           ),
                    //                         ),
                    //                         Text(
                    //                           'Expires 03/2028',
                    //                           style: TextStyle(
                    //                             fontFamily: 'Inter',
                    //                             fontSize: isLargeScreen
                    //                                 ? 16
                    //                                 : screenSize.width * 0.035,
                    //                             fontWeight: FontWeight.w400,
                    //                             height: 1.43,
                    //                             color: const Color(0xFF1F2122),
                    //                           ),
                    //                         ),
                    //                       ],
                    //                     ),
                    //                   ],
                    //                 ),
                    //                 IconButton(
                    //                   icon: Icon(
                    //                     Icons.more_vert,
                    //                     color: const Color(0xFF1F2122),
                    //                     size: isLargeScreen
                    //                         ? 24
                    //                         : screenSize.width * 0.05,
                    //                   ),
                    //                   onPressed: () {},
                    //                   constraints: const BoxConstraints(),
                    //                   padding: EdgeInsets.zero,
                    //                 ),
                    //               ],
                    //             ),
                    //             Divider(
                    //               height: screenSize.height * 0.025,
                    //               color: const Color(0xFFE1E3E6),
                    //             ),
                    //             SizedBox(height: screenSize.height * 0.015),
                    //             TextButton.icon(
                    //               onPressed: () {},
                    //               icon: Icon(
                    //                 Icons.add,
                    //                 size: isLargeScreen
                    //                     ? 24
                    //                     : screenSize.width * 0.05,
                    //                 color: const Color(0xFF1F2122),
                    //               ),
                    //               label: IntrinsicWidth(
                    //                 child: Column(
                    //                   mainAxisSize: MainAxisSize.min,
                    //                   crossAxisAlignment:
                    //                       CrossAxisAlignment.center,
                    //                   children: [
                    //                     Text(
                    //                       'Add Payment Method',
                    //                       style: TextStyle(
                    //                         fontFamily: 'Inter',
                    //                         color: Color(0xFF414346),
                    //                         fontSize: isLargeScreen
                    //                             ? 18
                    //                             : screenSize.width * 0.03,
                    //                         fontWeight: FontWeight.w600,
                    //                         height: 1.0,
                    //                         letterSpacing: 0.32,
                    //                         decoration: TextDecoration
                    //                             .none, // Remove default underline
                    //                       ),
                    //                     ),
                    //                     SizedBox(
                    //                       height: 1,
                    //                     ), // Optional spacing between text and line
                    //                     Container(
                    //                       height: 1,
                    //                       width: double
                    //                           .infinity, // Stretches only to text width
                    //                       color: Color(0xFF414346),
                    //                     ),
                    //                   ],
                    //                 ),
                    //               ),
                    //               style: TextButton.styleFrom(
                    //                 padding: EdgeInsets.zero,
                    //                 minimumSize: Size(screenSize.width * 0.075,
                    //                     screenSize.height * 0.025),
                    //                 tapTargetSize:
                    //                     MaterialTapTargetSize.shrinkWrap,
                    //                 alignment: Alignment.centerLeft,
                    //               ),
                    //             ),
                    //           ],
                    //         ),
                    // ),
                    SizedBox(height: screenSize.height * 0.01),
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            blurRadius: 2,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                      padding: EdgeInsets.all(screenSize.width * 0.04),
                      child: state is MealPlanProgressLoading
                          ? Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: List.generate(
                                6,
                                (index) => Padding(
                                  padding: EdgeInsets.symmetric(
                                    vertical: screenSize.height * 0.01,
                                  ),
                                  child: _buildLoadingShimmer(screenSize),
                                ),
                              ),
                            )
                          : Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Order Summary',
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontSize: isLargeScreen
                                        ? 22
                                        : screenSize.width * 0.04,
                                    fontWeight: FontWeight.w500,
                                    color: const Color(0xFF1F2122),
                                  ),
                                ),
                                DottedDivider(),
                                SizedBox(height: screenSize.height * 0.01),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Ordered From',
                                      style: TextStyle(
                                        fontSize: isLargeScreen
                                            ? 16
                                            : screenSize.width * 0.035,
                                        fontWeight: FontWeight.w500,
                                        color: const Color(0xFF1F2122),
                                      ),
                                    ),
                                    SizedBox(height: screenSize.height * 0.01),
                                    Row(
                                      children: [
                                        CircleAvatar(
                                          radius: isLargeScreen
                                              ? 20
                                              : screenSize.width * 0.04,
                                          backgroundImage: NetworkImage(
                                            ServerHelper.imageUrl +
                                                (widget
                                                        .selectedChefsWithDetails[
                                                            'chef']
                                                        ?.chefPhoto ??
                                                    ''),
                                          ),
                                        ),
                                        SizedBox(
                                            width: screenSize.width * 0.02),
                                        Text(
                                          widget
                                                  .selectedChefsWithDetails[
                                                      'chef']
                                                  ?.chefName ??
                                              '',
                                          style: TextStyle(
                                            fontSize: isLargeScreen
                                                ? 16
                                                : screenSize.width * 0.035,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                                SizedBox(height: screenSize.height * 0.01),
                                DottedDivider(),
                                SizedBox(height: screenSize.height * 0.01),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          'Order Details (${cartItems?.length ?? 0} Items)',
                                          style: TextStyle(
                                            fontSize: isLargeScreen
                                                ? 16
                                                : screenSize.width * 0.035,
                                            fontWeight: FontWeight.w500,
                                            color: const Color(0xFF1F2122),
                                          ),
                                        ),
                                        TextButton(
                                          // onPressed: () {
                                          //   Navigator.push(
                                          //     context,
                                          //     MaterialPageRoute(
                                          //       builder: (context) => AddItem(
                                          //         id: widget.chef_id,
                                          //         title: widget
                                          //                 .selectedChefsWithDetails[
                                          //                     'chef']
                                          //                 ?.chefName ??
                                          //             '',
                                          //         fromPage: 'Cart',
                                          //       ),
                                          //     ),
                                          //   ).then((_) {
                                          //     // Refresh cart data when returning from AddItem
                                          //     context.read<AccountBloc>().add(
                                          //         ViewCartEvent(
                                          //             widget.chef_id));
                                          //   });
                                          // },
                                          onPressed: () {
                                            Navigator.push(
                                              context,
                                              MaterialPageRoute(
                                                builder: (context) => ViewChef2(
                                                  id: widget.chef_id,
                                                  title: widget.chef_id
                                                          .toString() ??
                                                      '',fromCheckout:true
                                                  // fromPage: 'Cart',
                                                ),
                                              ),
                                            ).then((_) {

                                            });
                                          },
                                          style: TextButton.styleFrom(
                                            minimumSize: Size.zero,
                                            padding: EdgeInsets.zero,
                                            tapTargetSize: MaterialTapTargetSize
                                                .shrinkWrap,
                                          ),
                                          child: Row(
                                            children: [
                                              Icon(
                                                Icons.add,
                                                size: isLargeScreen
                                                    ? 16
                                                    : screenSize.width * 0.035,
                                                color: const Color(0xFF414346),
                                              ),
                                              SizedBox(
                                                  width:
                                                      screenSize.width * 0.01),
                                              IntrinsicWidth(
                                                child: Column(
                                                  mainAxisSize:
                                                      MainAxisSize.min,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.center,
                                                  children: [
                                                    Text(
                                                      'Add dish',
                                                      style: TextStyle(
                                                        fontSize: isLargeScreen
                                                            ? 16
                                                            : screenSize.width *
                                                                0.03,
                                                        fontWeight:
                                                            FontWeight.w400,
                                                        color: const Color(
                                                            0xFF414346),
                                                        decoration: TextDecoration
                                                            .none, // remove default underline
                                                      ),
                                                    ),
                                                    SizedBox(
                                                        height:
                                                            1), // spacing between text and line
                                                    Container(
                                                      height: 1,
                                                      width: double
                                                          .infinity, // expands to text width due to IntrinsicWidth
                                                      color: const Color(
                                                          0xFF414346),
                                                    ),
                                                  ],
                                                ),
                                              )
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                    ...(widget.selectedChefsWithDetails['items']
                                                as List?)
                                            ?.map(
                                          (item) => Padding(
                                            padding: EdgeInsets.symmetric(
                                              vertical:
                                                  screenSize.height * 0.01,
                                            ),
                                            child: Row(
                                              children: [
                                                Container(
                                                  padding: EdgeInsets.symmetric(
                                                    horizontal:
                                                        screenSize.width *
                                                            0.025,
                                                    vertical:
                                                        screenSize.height *
                                                            0.005,
                                                  ),
                                                  decoration: BoxDecoration(
                                                    color:
                                                        const Color(0xFFE1E3E6),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            26),
                                                  ),
                                                  child: Text(
                                                    '${item.quantity}x',
                                                    style: TextStyle(
                                                      fontSize: isLargeScreen
                                                          ? 16
                                                          : screenSize.width *
                                                              0.035,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      color: const Color(
                                                          0xFF1F2122),
                                                    ),
                                                  ),
                                                ),
                                                SizedBox(
                                                    width: screenSize.width *
                                                        0.03),
                                                Expanded(
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Text(
                                                        item.dishName ?? '',
                                                        style: TextStyle(
                                                          fontSize: isLargeScreen
                                                              ? 16
                                                              : screenSize
                                                                      .width *
                                                                  0.035,
                                                          fontWeight:
                                                              FontWeight.w600,
                                                          color: const Color(
                                                              0xFF1F2122),
                                                        ),
                                                      ),
                                                      TextButton(
                                                        onPressed: () {
                                                          Navigator.pop(
                                                              context);
                                                        },
                                                        style: TextButton
                                                            .styleFrom(
                                                          minimumSize:
                                                              Size.zero,
                                                          padding:
                                                              EdgeInsets.zero,
                                                          tapTargetSize:
                                                              MaterialTapTargetSize
                                                                  .shrinkWrap,
                                                        ),
                                                        child: Text(
                                                          'Edit',
                                                          style: TextStyle(
                                                            fontSize: isLargeScreen
                                                                ? 14
                                                                : screenSize
                                                                        .width *
                                                                    0.03,
                                                            fontWeight:
                                                                FontWeight.w400,
                                                            color: const Color(
                                                                0xFF414346),
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                                Text(
                                                  '\$${(item.price ?? 0) * (item.quantity ?? 1)}',
                                                  style: TextStyle(
                                                    fontSize: isLargeScreen
                                                        ? 16
                                                        : screenSize.width *
                                                            0.035,
                                                    fontWeight: FontWeight.w500,
                                                    color:
                                                        const Color(0xFF1F2122),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ) ??
                                        [],
                                  ],
                                ),
                                DottedDivider(),
                                SizedBox(height: screenSize.height * 0.01),
                                appliedCoupon == null
                                    ? Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Row(
                                            children: [
                                              Icon(
                                                Icons.percent,
                                                size: isLargeScreen
                                                    ? 18
                                                    : screenSize.width * 0.04,
                                                color: const Color(0xFF1F2122),
                                              ),
                                              SizedBox(
                                                  width:
                                                      screenSize.width * 0.01),
                                              Text(
                                                'Add promo code',
                                                style: TextStyle(
                                                  fontFamily: 'Inter',
                                                  fontSize: isLargeScreen
                                                      ? 16
                                                      : screenSize.width *
                                                          0.035,
                                                  fontWeight: FontWeight.w400,
                                                  color:
                                                      const Color(0xFF1F2122),
                                                ),
                                              ),
                                            ],
                                          ),
                                          TextButton(
                                            onPressed: () async {
                                              final selectedCouponCode =
                                                  await Navigator.push(
                                                context,
                                                MaterialPageRoute(
                                                  builder: (context) =>
                                                      ApplyCouponPage(
                                                          chefid:
                                                              widget.chef_id),
                                                ),
                                              );

                                              if (selectedCouponCode != null) {
                                                log(selectedCouponCode);
                                                setState(() {
                                                  appliedCoupon = {
                                                    'code': selectedCouponCode,
                                                  };
                                                });
                                              }

                                              Map<String, dynamic> requestData =
                                                  {
                                                "chef_id":
                                                    widget.chef_id.toString(),
                                                "use_wallet_credits":
                                                    _useWalletCredits,
                                                if (appliedCoupon != null)
                                                  "coupon_code":
                                                      appliedCoupon!['code'],
                                              };

                                              // Include delivery_time_id if a delivery time is selected
                                              if (_selectedDeliveryTime !=
                                                  null) {
                                                requestData[
                                                        "delivery_time_id"] =
                                                    _selectedDeliveryTime!.id
                                                        .toString();
                                              }

                                              context.read<OrderBloc>().add(
                                                  GetOrgerSummaryEvent(
                                                      requestData));
                                            },

                                            // onPressed: () {
                                            //   Navigator.push(
                                            //     context,
                                            //     MaterialPageRoute(
                                            //       builder: (context) =>
                                            //           ApplyCouponPage(
                                            //               chefid:
                                            //                   widget.chef_id),
                                            //     ),
                                            //   ).then((_) {
                                            //     context.read<OrderBloc>().add(
                                            //           GetOrgerSummaryEvent({
                                            //             "chef_id": widget
                                            //                 .chef_id
                                            //                 .toString(),
                                            //             "coupon_code":
                                            //                 appliedCoupon
                                            //           }),
                                            //         );
                                            //   });
                                            // },

                                            style: TextButton.styleFrom(
                                              minimumSize: Size.zero,
                                              padding: EdgeInsets.zero,
                                              tapTargetSize:
                                                  MaterialTapTargetSize
                                                      .shrinkWrap,
                                            ),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Icon(
                                                  Icons.add,
                                                  size: isLargeScreen
                                                      ? 18
                                                      : screenSize.width * 0.04,
                                                  color:
                                                      const Color(0xFF1F2122),
                                                ),
                                                SizedBox(
                                                    width: screenSize.width *
                                                        0.01),
                                                IntrinsicWidth(
                                                  child: Column(
                                                    mainAxisSize:
                                                        MainAxisSize.min,
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .center,
                                                    children: [
                                                      Text(
                                                        'Add',
                                                        style: TextStyle(
                                                          fontFamily: 'Inter',
                                                          fontWeight:
                                                              FontWeight.w600,
                                                          fontSize: isLargeScreen
                                                              ? 14
                                                              : screenSize
                                                                      .width *
                                                                  0.03,
                                                          height: 1.0,
                                                          letterSpacing: 0.24,
                                                          color: const Color(
                                                              0xFF414346),
                                                        ),
                                                      ),
                                                      SizedBox(
                                                          height: screenSize
                                                                  .height *
                                                              0.002),
                                                      Container(
                                                        height: 1,
                                                        color: const Color(
                                                            0xFF1F2122),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      )
                                    : Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          // First Row: Coupon code + Remove Coupon
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                appliedCoupon?["code"] ??
                                                    "coupon",
                                                style: TextStyle(
                                                  fontFamily: 'Inter-medium',
                                                  fontWeight: FontWeight.w400,
                                                  fontSize: 14,
                                                  height: 20 / 14,
                                                  letterSpacing: 0,
                                                  color: Color(0xFF1F2122),
                                                ),
                                              ),
                                              TextButton(
                                                onPressed: () {
                                                  setState(() {
                                                    appliedCoupon = null;
                                                  });

                                                  Map<String, dynamic>
                                                      requestData = {
                                                    "chef_id": widget.chef_id
                                                        .toString(),
                                                    "use_wallet_credits":
                                                        _useWalletCredits,
                                                    if (appliedCoupon != null)
                                                      "coupon_code":
                                                          appliedCoupon![
                                                              'code'],
                                                  };

                                                  // Include delivery_time_id if a delivery time is selected
                                                  if (_selectedDeliveryTime !=
                                                      null) {
                                                    requestData[
                                                            "delivery_time_id"] =
                                                        _selectedDeliveryTime!
                                                            .id
                                                            .toString();
                                                  }

                                                  context.read<OrderBloc>().add(
                                                      GetOrgerSummaryEvent(
                                                          requestData));
                                                },
                                                style: TextButton.styleFrom(
                                                  minimumSize: Size.zero,
                                                  padding: EdgeInsets.zero,
                                                  tapTargetSize:
                                                      MaterialTapTargetSize
                                                          .shrinkWrap,
                                                ),
                                                child: Row(
                                                  children: [
                                                    IntrinsicWidth(
                                                      child: Column(
                                                        mainAxisSize:
                                                            MainAxisSize.min,
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .center,
                                                        children: [
                                                          Text(
                                                            'Remove Coupon',
                                                            style: TextStyle(
                                                              fontFamily:
                                                                  'Inter',
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w600,
                                                              fontSize: isLargeScreen
                                                                  ? 14
                                                                  : screenSize
                                                                          .width *
                                                                      0.03,
                                                              height: 1.0,
                                                              letterSpacing:
                                                                  0.24,
                                                              color: const Color(
                                                                  0xFF414346),
                                                            ),
                                                          ),
                                                          SizedBox(
                                                              height: screenSize
                                                                      .height *
                                                                  0.002),
                                                          Container(
                                                            height: 1,
                                                            width: double
                                                                .infinity, // will now match text width due to IntrinsicWidth
                                                            color: const Color(
                                                                0xFF1F2122),
                                                          ),
                                                        ],
                                                      ),
                                                    )
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                          SizedBox(height: 4),
                                          // Second Row: Coupon Applied text
                                          Text(
                                            'Coupon Applied',
                                            style: TextStyle(
                                              fontFamily: 'Inter',
                                              fontWeight: FontWeight.w400,
                                              fontSize: 12,
                                              height: 16 / 12,
                                              letterSpacing: 0,
                                              color: Color(0xFF007A4D),
                                            ),
                                          ),
                                        ],
                                      ),
                                SizedBox(height: screenSize.height * 0.025),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Row(
                                      children: [
                                        Container(
                                          width: isLargeScreen
                                              ? 28
                                              : screenSize.width * 0.06,
                                          height: isLargeScreen
                                              ? 20
                                              : screenSize.width * 0.045,
                                          decoration: BoxDecoration(
                                            color: const Color(0xFF1F2122),
                                            borderRadius:
                                                BorderRadius.circular(4),
                                          ),
                                          alignment: Alignment.center,
                                          child: Text(
                                            'DB',
                                            style: TextStyle(
                                              fontFamily: 'Inter',
                                              fontSize: isLargeScreen
                                                  ? 12
                                                  : screenSize.width * 0.025,
                                              fontWeight: FontWeight.bold,
                                              color: Colors.white,
                                            ),
                                          ),
                                        ),
                                        SizedBox(
                                            width: screenSize.width * 0.025),
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'Eatro Wallet Credits',
                                              style: TextStyle(
                                                fontFamily: 'Inter',
                                                fontSize: isLargeScreen
                                                    ? 16
                                                    : screenSize.width * 0.035,
                                                fontWeight: FontWeight.w400,
                                                color: const Color(0xFF1F2122),
                                              ),
                                            ),
                                            Text(
                                              '\$ ${orderSummaryData?.walletBalance?.toStringAsFixed(2) ?? "0.00"}',
                                              style: TextStyle(
                                                fontFamily: 'Inter',
                                                fontSize: isLargeScreen
                                                    ? 14
                                                    : screenSize.width * 0.03,
                                                fontWeight: FontWeight.w400,
                                                color: const Color(0xFF66696D),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                    CustomToggle(
                                      value: _useWalletCredits,
                                      onChanged: (bool newValue) {
                                        // Check if wallet balance is 0 or null and user is trying to enable
                                        if (newValue &&
                                            (orderSummaryData?.walletBalance ==
                                                    null ||
                                                orderSummaryData!
                                                        .walletBalance! <=
                                                    0)) {
                                          // Show toast message
                                          ScaffoldMessenger.of(context)
                                              .showSnackBar(
                                            const SnackBar(
                                              content: Text(
                                                  "No available credits in your Dabba wallet"),
                                              duration: Duration(seconds: 2),
                                              backgroundColor: Color.fromARGB(
                                                  255, 230, 74, 53),
                                              behavior:
                                                  SnackBarBehavior.floating,
                                              margin: EdgeInsets.symmetric(
                                                  horizontal: 16, vertical: 16),
                                              shape: RoundedRectangleBorder(
                                                borderRadius: BorderRadius.all(
                                                    Radius.circular(8)),
                                              ),
                                            ),
                                          );
                                          return; // Don't proceed with the toggle
                                        }

                                        setState(() {
                                          _useWalletCredits = newValue;
                                        });

                                        // Call GetOrgerSummaryEvent with use_wallet_credits to refresh order summary
                                        Map<String, dynamic> requestData = {
                                          "chef_id": widget.chef_id.toString(),
                                          "use_wallet_credits": newValue,
                                          if (appliedCoupon != null)
                                            "coupon_code":
                                                appliedCoupon!['code'],
                                        };

                                        // Include delivery_time_id if a delivery time is selected
                                        if (_selectedDeliveryTime != null) {
                                          requestData["delivery_time_id"] =
                                              _selectedDeliveryTime!.id
                                                  .toString();
                                        }

                                        context.read<OrderBloc>().add(
                                            GetOrgerSummaryEvent(requestData));
                                      },
                                    ),
                                  ],
                                ),
                                SizedBox(height: screenSize.height * 0.01),
                                DottedDivider(),
                                SizedBox(height: screenSize.height * 0.01),
                                _buildOrderTotalSection(
                                    screenSize, isLargeScreen),
                                SizedBox(height: screenSize.height * 0.025),
                                Text(
                                  "Note: Lorem ipsum dolor sit amet, consectetur elit adipiscing, sed do eiusm tem dolor.",
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontSize: isLargeScreen
                                        ? 16
                                        : screenSize.width * 0.035,
                                    fontWeight: FontWeight.w400,
                                    height: 1.43,
                                    color: const Color(0xFF66696D),
                                  ),
                                ),
                              ],
                            ),
                    ),
                    SizedBox(height: screenSize.height * 0.025),
                    _buildPlaceOrderButton(screenSize, isLargeScreen),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
