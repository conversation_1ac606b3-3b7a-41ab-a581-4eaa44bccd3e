import 'package:db_eats/bloc/main_bloc.dart';
import 'package:db_eats/ui/auth/emailverificatiotest.dart';
import 'package:db_eats/ui/auth/phoneverificationpage.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class Verifysignup extends StatefulWidget {
  final String email;
  final String phone;
  final String countryCode;

  const Verifysignup(
      {Key? key, this.email = "", this.phone = "", this.countryCode = "+1"})
      : super(key: key);

  @override
  State<Verifysignup> createState() => _VerifysignupState();
}

class _VerifysignupState extends State<Verifysignup> {
  int _selectedVerificationMethod = -1;

  String maskEmail(String email) {
    if (email.isEmpty) return "j****@gmail.com";
    var parts = email.split('@');
    if (parts.length != 2) return email;
    return "${parts[0][0]}****@${parts[1]}";
  }

  String maskPhone(String phone) {
    if (phone.isEmpty) return "+123****56";
    return "${widget.countryCode}${phone.substring(0, 3)}****${phone.substring(phone.length - 2)}";
  }

  void _handleVerificationSelection(int index) {
    setState(() {
      _selectedVerificationMethod = index;
    });

    if (index == 0) {
      context.read<MainBloc>().add(
            VerifyEmailEvent(email: widget.email),
          );
    } else if (index == 1) {
      context.read<MainBloc>().add(
            VerifyPhoneEvent(phone: widget.phone),
          );
    }
  }

  double getResponsiveSize(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width < 360) return 12;
    if (width < 600) return 14;
    if (width < 900) return 16;
    return 18;
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isLandscape = size.width > size.height;
    final baseTextSize = getResponsiveSize(context);

    String maskedEmail = maskEmail(widget.email);
    String maskedPhone = maskPhone(widget.phone);

    return BlocListener<MainBloc, MainState>(
      listener: (context, state) {
        if (state is VerifyEmailSuccess) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => Emailverificatiotest(email: widget.email),
            ),
          );
        } else if (state is VerifyEmailFailed) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.message)),
          );
        } else if (state is VerifyPhoneSuccess) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => PhoneVerificationPage(
                phone: widget.phone,
                countryCode: widget.countryCode,
              ),
            ),
          );
        } else if (state is VerifyPhoneFailed) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.message)),
          );
        }
      },
      child: BlocBuilder<MainBloc, MainState>(
        builder: (context, state) {
          return Stack(
            children: [
              Scaffold(
                backgroundColor: const Color(0xFFF6F3EC),
                body: SafeArea(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(
                          left: size.width * 0.04,
                          top: size.height * 0.02,
                        ),
                        child: Container(
                          width: size.width * 0.1,
                          height: size.width * 0.1,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.05),
                                blurRadius: 2,
                                offset: const Offset(0, 1),
                              ),
                            ],
                          ),
                          child: IconButton(
                            icon: Icon(
                              Icons.close,
                              size: baseTextSize * 1.2,
                              color: Colors.black,
                            ),
                            onPressed: () {
                              Navigator.pop(context);
                            },
                            padding: EdgeInsets.all(size.width * 0.02),
                            constraints: const BoxConstraints(),
                          ),
                        ),
                      ),
                      SizedBox(height: size.height * 0.06),
                      Center(
                        child: Text(
                          'Verify your account',
                          style: TextStyle(
                            fontSize: isLandscape
                                ? size.height * 0.04
                                :
                                // baseTextSize * 1.5
                                baseTextSize * 1.7  // gives 23.8
,
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF1F2122),
                          ),
                        ),
                      ),
                      SizedBox(height: size.height * 0.02),
                      Center(
                        child: Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: size.width * 0.05),
                          child: Text(
                            'Choose how you want to verify your account:',
                            style: TextStyle(
                              fontSize: baseTextSize * 1.15  // = 16.1 (very close to 16)
,
                              fontFamily: 'Inter',
                              fontWeight: FontWeight.w400,
                              color: Color(0xFF414346),
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                      SizedBox(height: size.height * 0.04),
                      Padding(
                        padding:
                            EdgeInsets.symmetric(horizontal: size.width * 0.04),
                        child: _buildResponsiveRadioOption(
                          context: context,
                          title: 'Verify with email',
                          value: maskedEmail,
                          index: 0,
                          baseTextSize: baseTextSize,
                        ),
                      ),
                      SizedBox(height: size.height * 0.02),
                      Padding(
                        padding:
                            EdgeInsets.symmetric(horizontal: size.width * 0.04),
                        child: _buildResponsiveRadioOption(
                          context: context,
                          title: 'Verify with phone number',
                          value: maskedPhone,
                          index: 1,
                          baseTextSize: baseTextSize,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              if (state is VerfyingPhone || state is VerifyingEmail)
                Container(
                  color: Colors.black.withOpacity(0.3),
                  child: const Center(
                    child: CupertinoActivityIndicator(
                      color: Colors.white,
                      radius: 10.0,
                    ),
                  ),
                ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildResponsiveRadioOption({
    required BuildContext context,
    required String title,
    required String value,
    required int index,
    required double baseTextSize,
  }) {
    final size = MediaQuery.of(context).size;

    return Container(
      height: size.height * 0.07,
      decoration: BoxDecoration(
        color: const Color(0xFFF6F3EC),
        borderRadius: BorderRadius.circular(size.width * 0.02),
        border: Border.all(
          color: const Color(0xFFB9B6AD),
          width: 1,
        ),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(size.width * 0.02),
        onTap: () => _handleVerificationSelection(index),
        child: Row(
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: size.width * 0.03),
              child: Radio<int>(
                value: index,
                groupValue: _selectedVerificationMethod,
                onChanged: (int? value) => _handleVerificationSelection(value!),
                activeColor: Colors.black,
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
            Expanded(
              child: Text.rich(
                TextSpan(
                  children: [
                    TextSpan(
                      text: '$title ',
                      style: TextStyle(
                        fontSize: baseTextSize,
                        fontWeight: FontWeight.w500,
                        fontFamily: 'Inter',
                        color: Color(0xFF1F2122),
                      ),
                    ),
                    TextSpan(
                      text: value,
                      style: TextStyle(
                        fontSize: baseTextSize,
                        fontWeight: FontWeight.w400,
                        fontFamily: 'Inter',
                        color: Color(0xFF1F2122),
                      ),
                    ),
                  ],
                ),
                softWrap: true,
                overflow: TextOverflow.visible,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
