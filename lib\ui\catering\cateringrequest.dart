import 'package:db_eats/bloc/catering_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/data/models/catering/cateringacceptedlistmodel.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/ui/cart/yourcart.dart';
import 'package:db_eats/ui/catering/chef_infopage.dart';
import 'package:db_eats/ui/catering/editcateringrequest.dart';
import 'package:db_eats/ui/mainnavigationpage.dart';
import 'package:db_eats/widgets/floatingactionbutton.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fluttertoast/fluttertoast.dart';

class CateringRequestsPage extends StatefulWidget {
  final int initialTab;

  const CateringRequestsPage({
    super.key,
    this.initialTab = 0,
  });

  @override
  State<CateringRequestsPage> createState() => _CateringRequestsPageState();
}

class _CateringRequestsPageState extends State<CateringRequestsPage>
    with SingleTickerProviderStateMixin {
  late PageController _pageController;
  int _currentPageIndex = 0;
  final List<String> _tabs = ['Pending', 'Accepted', 'Past Orders'];

  Map<String, bool> expandedMap = {};
  Map<String, bool> cuisineExpandedMap = {}; // Track cuisine expansion state

  late AnimationController _animationController;
  late Animation<double> _tabIndicatorAnimation;

  String _currentStatus = 'pending';

  @override
  void initState() {
    super.initState();
    _currentPageIndex = widget.initialTab;
    _pageController = PageController(initialPage: widget.initialTab);
    _currentStatus = _getStatusFromTabIndex(widget.initialTab);

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _tabIndicatorAnimation =
        Tween<double>(begin: 0, end: 1).animate(_animationController);
    _pageController.addListener(_onPageChange);

    // Load initial data
    _loadCurrentTabData();
  }

  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  String _getStatusFromTabIndex(int index) {
    switch (_tabs[index].toLowerCase()) {
      case 'pending':
        return 'pending';
      case 'accepted':
        return 'accepted';
      case 'past orders':
        return 'past';
      default:
        return 'pending';
    }
  }

  void _loadCurrentTabData() {
    final tabName = _tabs[_currentPageIndex].toLowerCase();
    switch (tabName) {
      case 'pending':
        _currentStatus = 'pending';
        break;
      case 'accepted':
        _currentStatus = 'accepted';
        break;
      case 'past orders':
        _currentStatus = 'past';
        break;
      default:
        return;
    }
    context.read<CateringBloc>().add(ListCateringrequestsEvent(_currentStatus));
  }

  void _onPageChange() {
    final pageIndex = _pageController.page?.round() ?? 0;
    if (pageIndex != _currentPageIndex) {
      setState(() {
        _currentPageIndex = pageIndex;
        _currentStatus = _getStatusFromTabIndex(pageIndex);
      });

      context
          .read<CateringBloc>()
          .add(ListCateringrequestsEvent(_currentStatus));
    }
  }

  @override
  void dispose() {
    _pageController.removeListener(_onPageChange);
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFf6f3ec),
      appBar: AppBar(
        backgroundColor: const Color(0xFFf6f3ec),
        elevation: 0,
        centerTitle: false,
        scrolledUnderElevation: 0,
        automaticallyImplyLeading: false,
        title: Text(
          'Catering Requests',
          style: TextStyle(
            fontFamily: 'Inter',
            fontWeight: FontWeight.w600,
            fontSize: twenty,
            color: Color(0xFF1F2122),
          ),
        ),
      ),
      body: Column(
        children: [
          Padding(
            padding: EdgeInsets.symmetric(
                horizontal: sixteen, vertical: sixteen / 2),
            child: _buildAnimatedTabBar(),
          ),
          SizedBox(height: sixteen),
          Expanded(
            child: PageView(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentPageIndex = index;
                });
              },
              children: _tabs.map((tabName) {
                return _buildTabContent(tabName.toLowerCase());
              }).toList(),
            ),
          ),
        ],
      ),
      floatingActionButton: CartFloatingActionButton(
        itemCount: Initializer.cartCount ?? 0,
        onPressed: _openCart,
      ),
    );
  }

  Widget _buildAnimatedTabBar() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: List.generate(_tabs.length, (index) {
        return _buildTabItem(_tabs[index], index);
      }),
    );
  }

  void onEditRequest(
      {required String itemid, required CateringAcceptedListData item}) {
    dynamic data = transformCateringRequest(item);
    print(data);
    print("Edit request clicked");
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EditCateringRequestPage(
          cateringData: data,
        ),
      ),
    );
  }

  void onCancelRequest({required CateringAcceptedListData item}) {
    print(" Cancel request clicked");
    // Navigator.push(
    //   context,
    //   MaterialPageRoute(
    //     builder: (context) => edit(
    //      catering_id: item.id,
    //     ),
    //   ),
    // );
    dynamic data = {
      "catering_id": item.id,
    };
    context.read<CateringBloc>().add(CancelCateringRequestEvent(data));
  }

  /// Converts one catering-request record from the server into
  /// the structure your “create/modify request” endpoint expects.
  Map<String, dynamic> transformCateringRequest(CateringAcceptedListData item) {
    List<int> extractIds(List<dynamic>? list) {
      return list?.map((e) => (e as dynamic).id as int).toList() ?? [];
    }

    return {
      'catering_id': item.id,
      'people_count': item.peopleCount,
      'address': item.address,
      'state': item.state,
      'city': item.city,
      'zip_code': item.zipCode,
      'allergy_prference_text': item.allergyPreferenceText,
      'packaging_type_id': item.packagingTypeId,
      'time_slot_id': item.timeSlot?.id,
      'date': item.date,
      'cuisine_ids': extractIds(item.cateringCuisines),
      'sub_cuisine_ids': extractIds(item.cateringSubCuisines),
      'local_cuisine_ids': extractIds(item.cateringLocalCuisines),
      'dietary_preference_id': item.dietaryPreferenceId,
      'spice_level_id': item.spiceLevelId,
      'catering_type_id': item.cateringType!.id,
    };
  }

  Widget _buildTabItem(String title, int index) {
    final isSelected = _currentPageIndex == index;

    return GestureDetector(
      onTap: () {
        _pageController.animateToPage(
          index,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );

        setState(() {
          _currentStatus = _getStatusFromTabIndex(index);
        });

        context
            .read<CateringBloc>()
            .add(ListCateringrequestsEvent(_currentStatus));
      },
      child: Column(
        children: [
          AnimatedDefaultTextStyle(
            duration: const Duration(milliseconds: 200),
            style: TextStyle(
              fontFamily: 'Inter',
              fontWeight: FontWeight.w600,
              fontSize: forteen,
              color: isSelected
                  ? const Color(0xFF1F2122)
                  : const Color(0xFF66696D),
            ),
            child: Text(title),
          ),
          SizedBox(height: sixteen / 4),
          // Animated indicator
          TweenAnimationBuilder<double>(
            duration: const Duration(milliseconds: 250),
            curve: Curves.easeInOut,
            tween: Tween<double>(
              begin: 0.0,
              end: isSelected ? 1.0 : 0.0,
            ),
            builder: (context, value, child) {
              return Container(
                height: sixteen / 8,
                width: ten * 11,
                decoration: BoxDecoration(
                  color: Color.lerp(
                    Colors.transparent,
                    const Color(0xFFFFBE16),
                    value,
                  ),
                  borderRadius: BorderRadius.circular(1),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildTabContent(String tabName) {
    return BlocBuilder<CateringBloc, CateringState>(
      builder: (context, state) {
        // Handle all three tabs
        if (tabName.toLowerCase() == 'accepted' ||
            tabName.toLowerCase() == 'pending' ||
            tabName.toLowerCase() == 'past orders') {
          if (state is ListCateringrequestsLoading) {
            return Center(
              child: CupertinoActivityIndicator(
                color: Colors.black,
                radius: twelve, // You can adjust the size
              ),
            );
          } else if (state is ListCateringrequestsSuccess) {
            if (Initializer.cateringAcceptedListModel.data == null) {
              return Center(
                child: Text(
                  'No data available',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontSize: forteen,
                    color: Color(0xFF414346),
                  ),
                ),
              );
            }

            final requests = Initializer.cateringAcceptedListModel.data!;
            if (requests.isEmpty) {
              return Center(
                child: Text(
                  'No ${tabName.toLowerCase()} requests found',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontSize: forteen,
                    color: Color(0xFF414346),
                  ),
                ),
              );
            }

            return AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              child: ListView.builder(
                key: ValueKey<String>(tabName),
                padding: EdgeInsets.symmetric(horizontal: sixteen),
                itemCount: requests.length,
                itemBuilder: (context, index) {
                  final item = requests[index];
                  final itemId = '${item.id}_${item.date}';
                  if (!expandedMap.containsKey(itemId)) {
                    expandedMap[itemId] = true;
                  }

                  final card = _buildAcceptedRequestCard(item, itemId);

                  // Add bottom spacing after the last card
                  if (index == requests.length - 1) {
                    return Column(
                      children: [
                        card,
                        SizedBox(height: screenHeight * 0.08),
                      ],
                    );
                  }

                  return card;
                },
              ),
            );
          } else if (state is ListCateringrequestsFailed) {
            return Center(
              child: Text(
                state.message,
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontSize: forteen,
                  color: Color(0xFF414346),
                ),
              ),
            );
          }
        }

        return Center(
          child: Text(
            'Invalid tab',
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: sixteen,
              color: Color(0xFF414346),
            ),
          ),
        );
      },
    );
  }

  void _openCart() {
    print('Opening cart');
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CartPage(),
      ),
    );
  }

  Widget _buildAcceptedRequestCard(
      CateringAcceptedListData item, String itemId) {
    final isExpanded = expandedMap[itemId] ?? true;

    // Format date string
    String formattedDate = '';
    if (item.date != null) {
      try {
        final date = DateTime.parse(item.date!);
        formattedDate =
            '${date.day} ${_getMonthName(date.month)}, ${date.year}';
      } catch (e) {
        print('Error parsing date: $e');
        formattedDate = item.date ?? '';
      }
    }

    // Format time string
    String formattedTime = '';
    if (item.timeSlot?.startTime != null) {
      try {
        final time = TimeOfDay.fromDateTime(
            DateTime.parse('2024-01-01 ${item.timeSlot!.startTime}'));
        formattedTime =
            '${time.hourOfPeriod}:${time.minute.toString().padLeft(2, '0')} ${time.period == DayPeriod.am ? 'AM' : 'PM'}';
      } catch (e) {
        print('Error parsing time: $e');
        formattedTime = item.timeSlot?.startTime ?? '';
      }
    }

    return AnimatedOpacity(
      opacity: 1.0,
      duration: const Duration(milliseconds: 300),
      child: Container(
        margin: EdgeInsets.only(bottom: sixteen),
        padding: EdgeInsets.all(sixteen),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: sixteen / 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: _buildCuisineTitle(item, itemId),
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(
                          horizontal: ten, vertical: sixteen / 4),
                      decoration: BoxDecoration(
                        color: _getStatusColor(item.status ?? ''),
                        borderRadius: BorderRadius.circular(twelve),
                      ),
                      child: Text(
                        _getStatusText(item.status ?? ''),
                        style: TextStyle(
                          fontSize: twelve,
                          fontWeight: FontWeight.w500,
                          fontFamily: 'Inter',
                        ),
                      ),
                    ),
                    if (_currentStatus == 'pending')
                      PopupMenuButton(
                        icon: Image.asset(
                          'assets/icons/edit_1.png',
                          width: twentyFour,
                          height: twentyFour,
                        ),
                        onSelected: (value) {
                          if (value == 'edit') {
                            onEditRequest(
                              item: item,
                              itemid: itemId,
                            );
                          }
                        },
                        offset: const Offset(-10, 15),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        color: Colors.white,
                        itemBuilder: (context) => [
                          PopupMenuItem(
                            height: ten * 4,
                            value: 'edit',
                            child: Container(
                              width: ten * 16,
                              padding: EdgeInsets.symmetric(horizontal: twelve),
                              child: Text(
                                'Edit request',
                                style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontSize: forteen,
                                  fontWeight: FontWeight.w400,
                                  color: Color(0xFF1F2122),
                                ),
                              ),
                            ),
                          ),
                          PopupMenuItem(
                            height: ten * 4,
                            value: 'cancel',
                            child: Container(
                              width: ten * 16,
                              padding: EdgeInsets.symmetric(horizontal: twelve),
                              child: Text(
                                'Cancel request',
                                style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontSize: forteen,
                                  fontWeight: FontWeight.w400,
                                  color: Color(0xFF1F2122),
                                ),
                              ),
                            ),
                            onTap: () {
                              Future.delayed(
                                const Duration(milliseconds: 100),
                                () {
                                  showDialog(
                                    context: context,
                                    builder: (BuildContext context) => Dialog(
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Container(
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        padding: EdgeInsets.all(twentyFour),
                                        child: Column(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Text(
                                              'Are you sure you want to cancel this catering request?',
                                              textAlign: TextAlign.center,
                                              style: TextStyle(
                                                fontFamily: 'Inter',
                                                fontSize: forteen,
                                                fontWeight: FontWeight.w400,
                                                color: Color(0xFF414346),
                                              ),
                                            ),
                                            SizedBox(height: twentyFour),
                                            Row(
                                              children: [
                                                Expanded(
                                                  child: TextButton(
                                                    onPressed: () =>
                                                        Navigator.of(context)
                                                            .pop(),
                                                    style: TextButton.styleFrom(
                                                      backgroundColor:
                                                          Colors.white,
                                                      shape:
                                                          RoundedRectangleBorder(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(8),
                                                        side: const BorderSide(
                                                          color:
                                                              Color(0xFFE1E3E6),
                                                        ),
                                                      ),
                                                      minimumSize: Size(
                                                          double.infinity,
                                                          twentyFour * 2),
                                                    ),
                                                    child: Text(
                                                      'No',
                                                      style: TextStyle(
                                                        fontFamily: 'Inter',
                                                        fontSize: forteen,
                                                        fontWeight:
                                                            FontWeight.w600,
                                                        color:
                                                            Color(0xFF1F2122),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                                SizedBox(width: twelve),
                                                Expanded(
                                                  child: BlocConsumer<
                                                      CateringBloc,
                                                      CateringState>(
                                                    listener: (context, state) {
                                                      if (state
                                                          is CancelCateringRequestSuccess) {
                                                        Fluttertoast.showToast(
                                                          msg:
                                                              "Request cancelled successfully.",
                                                          toastLength: Toast
                                                              .LENGTH_SHORT,
                                                          gravity: ToastGravity
                                                              .BOTTOM,
                                                          backgroundColor:
                                                              Colors.green,
                                                          textColor:
                                                              Colors.white,
                                                        );

                                                        Navigator.push(
                                                          context,
                                                          MaterialPageRoute(
                                                            builder: (_) =>
                                                                WithNavBar(
                                                              currentIndex: 2,
                                                              child:
                                                                  CateringRequestsPage(),
                                                            ),
                                                          ),
                                                        );
                                                      } else if (state
                                                          is CancelCateringRequestFailed) {
                                                        Fluttertoast.showToast(
                                                          msg:
                                                              "Failed to cancel the request. Please try again.",
                                                          toastLength: Toast
                                                              .LENGTH_SHORT,
                                                          gravity: ToastGravity
                                                              .BOTTOM,
                                                          backgroundColor:
                                                              Colors.red,
                                                          textColor:
                                                              Colors.white,
                                                        );
                                                      }
                                                    },
                                                    builder: (context, state) {
                                                      final isLoading = state
                                                          is CancelCateringRequestLoading;

                                                      return TextButton(
                                                        onPressed: isLoading
                                                            ? null
                                                            : () =>
                                                                onCancelRequest(
                                                                    item: item),
                                                        style: TextButton
                                                            .styleFrom(
                                                          backgroundColor:
                                                              const Color(
                                                                  0xFFFFBE16),
                                                          shape:
                                                              RoundedRectangleBorder(
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        sixteen /
                                                                            2),
                                                          ),
                                                          minimumSize: Size(
                                                              double.infinity,
                                                              twentyFour * 2),
                                                        ),
                                                        child: isLoading
                                                            ? SizedBox(
                                                                width:
                                                                    twentyFour,
                                                                height:
                                                                    twentyFour,
                                                                child:
                                                                    CircularProgressIndicator(
                                                                  strokeWidth:
                                                                      2.5,
                                                                  valueColor: AlwaysStoppedAnimation<
                                                                          Color>(
                                                                      Color.fromARGB(
                                                                          255,
                                                                          255,
                                                                          255,
                                                                          255)),
                                                                ),
                                                              )
                                                            : Text(
                                                                'Yes',
                                                                style:
                                                                    TextStyle(
                                                                  fontFamily:
                                                                      'Inter',
                                                                  fontSize:
                                                                      forteen,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w600,
                                                                  color: Color(
                                                                      0xFF1F2122),
                                                                ),
                                                              ),
                                                      );
                                                    },
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              );
                            },
                          ),
                        ],
                      ),
                  ],
                ),
              ],
            ),
            SizedBox(height: sixteen / 2),
            Row(
              children: [
                Text(
                  formattedDate,
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w400,
                    fontSize: forteen,
                    color: Color(0xFF414346),
                  ),
                ),
                if (formattedTime.isNotEmpty) ...[
                  SizedBox(width: sixteen / 4),
                  Text('•',
                      style: TextStyle(
                        color: Color(0xFF414346),
                        fontSize: forteen,
                      )),
                  SizedBox(width: sixteen / 4),
                  Text(
                    formattedTime,
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w400,
                      fontSize: forteen,
                      color: Color(0xFF414346),
                    ),
                  ),
                ],
              ],
            ),
            SizedBox(height: sixteen / 2),
            Text(
              item.address ?? '',
              style: TextStyle(
                fontFamily: 'Inter',
                fontWeight: FontWeight.w400,
                fontSize: forteen,
                color: Color(0xFF414346),
              ),
            ),
            SizedBox(height: sixteen),
            if (item.chef != null) ...[
              if (isExpanded)
                _buildChefInfo(item.chef!, _getFullCuisineNames(item),
                    item.peopleCount ?? 0,
                    item: item),
              InkWell(
                onTap: () {
                  setState(() {
                    expandedMap[itemId] = !isExpanded;
                  });
                },
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: sixteen / 2.0),
                  child: IntrinsicWidth(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          isExpanded ? 'Hide list (1)' : 'Show list (1)',
                          style: TextStyle(
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w600,
                            fontSize: forteen,
                            // decoration: TextDecoration.underline,
                          ),
                        ),
                        Container(
                          height: 1,
                          margin: const EdgeInsets.only(top: 0),
                          color: const Color(0xff1F2122),
                          // width will match the text above
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildChefInfo(Chef chef, String cuisineNames, int peopleCount,
      {CateringAcceptedListData? item}) {
    return Container(
      margin: EdgeInsets.only(bottom: twelve),
      padding: EdgeInsets.all(twelve),
      decoration: BoxDecoration(
        border: Border.all(color: const Color(0xFFE1E3E6)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: sixteen,
                backgroundImage: NetworkImage(
                  ServerHelper.imageUrl + (chef.photo ?? ''),
                ),
                onBackgroundImageError: (exception, stackTrace) {
                  print('Error loading chef image: $exception');
                },
                backgroundColor: const Color(0xFFE1E3E6),
              ),
              SizedBox(width: twelve),
              Text(
                chef.name ?? '_',
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w600,
                  fontSize: forteen,
                ),
              ),
            ],
          ),
          SizedBox(height: twelve),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  chef.searchTags?.join(', ') ?? '',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontSize: twelve,
                    color: Color(0xFF414346),
                  ),
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(
                    horizontal: sixteen / 2, vertical: sixteen / 4),
                decoration: BoxDecoration(
                  color: const Color(0xFFE1E3E6),
                  borderRadius: BorderRadius.circular(twelve),
                ),
                child: Row(
                  children: [
                    Image.asset(
                      'assets/icons/thump.png',
                      width: twelve,
                      height: twelve,
                    ),
                    SizedBox(width: sixteen / 4),
                    Text(
                      '${chef.ratingPercentage}% (${chef.noOfRatings})',
                      style: TextStyle(
                        fontSize: ten,
                        fontWeight: FontWeight.w500,
                        fontFamily: 'Inter',
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          // Only show View Menu if status is ACCEPTED
          if (item?.status?.toUpperCase() == 'ACCEPTED') ...[
            SizedBox(height: sixteen),
            GestureDetector(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => CateringChefInfoPage(
                      cateringRequestId: item?.id ?? 0,
                      id: chef.id ?? 0,
                      title: chef.name ?? '',
                      cuisineTitle: cuisineNames,
                      peopleCount: peopleCount,
                      cuisineIds: item?.cateringCuisines
                          ?.map((c) => c.id ?? 0)
                          .toList(),
                      subCuisineIds: item?.cateringSubCuisines
                          ?.map((c) => c.id ?? 0)
                          .toList(),
                      localCuisineIds: item?.cateringLocalCuisines
                          ?.map((c) => c.id ?? 0)
                          .toList(),
                      packagingTypeId: item?.packagingTypeId,
                      dietaryPreferenceId: item?.dietaryPreferenceId,
                      spiceLevelId: item?.spiceLevelId,
                    ),
                  ),
                ).then((_) {
                  context.read<CateringBloc>().add(
                        ListCateringrequestsEvent(_currentStatus),
                      );
                });
              },
              child: Align(
                alignment: Alignment.centerLeft,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    IntrinsicWidth(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'View Menu',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontWeight: FontWeight.w600,
                              fontSize: forteen,
                              color: Color(0xFF1F2122),
                            ),
                          ),
                          Container(
                            height: 1,
                            margin: const EdgeInsets.only(top: 0),
                            color: const Color(0xff1F2122),
                            // underline matches text width
                          ),
                        ],
                      ),
                    ),
                    SizedBox(width: sixteen / 4),
                    Icon(Icons.arrow_forward,
                        size: sixteen, color: Color(0xFF1F2122)),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildChefCard(Map<String, dynamic> chef, String title,
      {CateringAcceptedListData? item}) {
    return Container(
      margin: EdgeInsets.only(bottom: twelve),
      padding: EdgeInsets.all(twelve),
      decoration: BoxDecoration(
        border: Border.all(
          color: const Color(0xFFE1E3E6),
          width: 1,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: twenty,
                backgroundImage: AssetImage(chef['image']),
              ),
              SizedBox(width: twelve),
              Text(
                chef['name'],
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w600,
                  fontSize: forteen,
                ),
              ),
            ],
          ),
          SizedBox(height: twelve),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  chef['tags'],
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w400,
                    fontSize: twelve,
                    color: Color(0xFF414346),
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(
                    horizontal: sixteen / 2, vertical: sixteen / 8),
                decoration: BoxDecoration(
                  color: const Color.fromRGBO(225, 227, 230, 1),
                  borderRadius: BorderRadius.circular(twelve),
                ),
                child: Row(
                  children: [
                    Image.asset(
                      'assets/icons/thump.png',
                      width: sixteen,
                      height: sixteen,
                      color: Colors.black54,
                    ),
                    SizedBox(width: sixteen / 4),
                    Text(
                      "${chef['rating']}%",
                      style: TextStyle(
                        fontSize: forteen,
                        fontWeight: FontWeight.w500,
                        fontFamily: 'Inter',
                      ),
                    ),
                    const SizedBox(width: 2),
                    Text(
                      "(${chef['reviews']})",
                      style: TextStyle(
                        fontSize: forteen,
                        fontWeight: FontWeight.w500,
                        fontFamily: 'Inter',
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
          SizedBox(height: twenty),
          Row(
            children: [
              GestureDetector(
                onTap: () {
                  if (_currentStatus == 'pending') {
                    showDialog(
                      context: context,
                      builder: (BuildContext context) {
                        return AlertDialog(
                          content: Text(
                            'The chef hasn\'t accepted your request yet',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontSize: sixteen,
                              color: Color(0xFF414346),
                            ),
                          ),
                          actions: [
                            TextButton(
                              onPressed: () {
                                Navigator.of(context).pop();
                              },
                              child: const Text(
                                'OK',
                                style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontWeight: FontWeight.w600,
                                  color: Colors.black,
                                ),
                              ),
                            ),
                          ],
                        );
                      },
                    );
                  } else {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => CateringChefInfoPage(
                          cateringRequestId: item?.id ?? 0,
                          id: chef['id'] ?? 0,
                          title: chef['name'] ?? '',
                          cuisineTitle: title,
                          peopleCount: chef['peopleCount'] ?? 0,
                        ),
                      ),
                    );
                  }
                },
                child: Text(
                  'View Menu',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w600,
                    fontSize: sixteen,
                    height: 1,
                    color: Color(0xFF1F2122),
                    decoration: TextDecoration.underline,
                    decorationStyle: TextDecorationStyle.solid,
                  ),
                ),
              ),
              SizedBox(width: sixteen / 2),
              Icon(
                Icons.arrow_forward,
                size: sixteen,
                color: Color(0xFF1F2122),
              ),
            ],
          ),
          SizedBox(height: twelve / 2),
        ],
      ),
    );
  }

  String _getMonthName(int month) {
    switch (month) {
      case 1:
        return 'January';
      case 2:
        return 'February';
      case 3:
        return 'March';
      case 4:
        return 'April';
      case 5:
        return 'May';
      case 6:
        return 'June';
      case 7:
        return 'July';
      case 8:
        return 'August';
      case 9:
        return 'September';
      case 10:
        return 'October';
      case 11:
        return 'November';
      case 12:
        return 'December';
      default:
        return '';
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toUpperCase()) {
      case 'PENDING':
        return const Color(0xFFFFF7E6); // Light yellow
      case 'PLACED':
        return const Color.fromARGB(255, 176, 216, 176); // Light green
      case 'ACCEPTED':
        return const Color(0xFFE6FFE6); // Light green
      case 'IN_PROGRESS':
        return const Color(0xFFE6F7FF); // Light blue
      case 'COMPLETED':
        return const Color(0xFFE1E3E6); // Gray
      case 'CANCELLED':
        return const Color(0xFFFFE6E6); // Light red
      default:
        return const Color(0xFFE1E3E6); // Default gray
    }
  }

  String _getStatusText(String status) {
    switch (status.toUpperCase()) {
      case 'PENDING':
        return 'Pending';
      case 'PLACED':
        return 'Placed';
      case 'ACCEPTED':
        return 'Accepted';
      case 'IN_PROGRESS':
        return 'In Progress';
      case 'COMPLETED':
        return 'Completed';
      case 'CANCELLED':
        return 'Cancelled';
      default:
        return status; // Return the original status if not matched
    }
  }

  String _getFullCuisineNames(CateringAcceptedListData item) {
    final cuisineList = [
      ...(item.cateringCuisines?.map((c) => c.name) ?? []),
      ...(item.cateringSubCuisines?.map((c) => c.name) ?? []),
      ...(item.cateringLocalCuisines?.map((c) => c.name) ?? []),
    ].where((name) => name != null && name.isNotEmpty).cast<String>().toList();

    return cuisineList.join(', ');
  }

  Widget _buildCuisineTitle(CateringAcceptedListData item, String itemId) {
    final cuisineList = [
      ...(item.cateringCuisines?.map((c) => c.name) ?? []),
      ...(item.cateringSubCuisines?.map((c) => c.name) ?? []),
      ...(item.cateringLocalCuisines?.map((c) => c.name) ?? []),
    ].where((name) => name != null && name.isNotEmpty).cast<String>().toList();

    final isCuisineExpanded = cuisineExpandedMap[itemId] ?? false;
    const maxVisibleCuisines = 3;

    if (cuisineList.length <= maxVisibleCuisines) {
      // Show all cuisines if 3 or fewer
      return Text(
        '${cuisineList.join(', ')} Catering for ${item.peopleCount} People',
        style: TextStyle(
          fontFamily: 'Inter',
          fontWeight: FontWeight.w600,
          fontSize: sixteen,
        ),
      );
    } else {
      // Show first 3 + expandable logic
      if (isCuisineExpanded) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${cuisineList.join(', ')} Catering for ${item.peopleCount} People',
              style: TextStyle(
                fontFamily: 'Inter',
                fontWeight: FontWeight.w600,
                fontSize: sixteen,
              ),
            ),
            SizedBox(height: sixteen / 4),
            GestureDetector(
              onTap: () {
                setState(() {
                  cuisineExpandedMap[itemId] = false;
                });
              },
              child: Text(
                'Show less',
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w500,
                  fontSize: twelve,
                  color: const Color(0xFF414346),
                  decoration: TextDecoration.underline,
                ),
              ),
            ),
          ],
        );
      } else {
        final visibleCuisines = cuisineList.take(maxVisibleCuisines).toList();
        final remainingCount = cuisineList.length - maxVisibleCuisines;

        return RichText(
          text: TextSpan(
            style: TextStyle(
              fontFamily: 'Inter',
              fontWeight: FontWeight.w600,
              fontSize: sixteen,
              color: Colors.black,
            ),
            children: [
              TextSpan(text: '${visibleCuisines.join(', ')} '),
              WidgetSpan(
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      cuisineExpandedMap[itemId] = true;
                    });
                  },
                  child: Text(
                    '+$remainingCount more',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w500,
                      fontSize: forteen,
                      color: const Color(0xFF414346),
                    ),
                  ),
                ),
              ),
              TextSpan(text: ' Catering for ${item.peopleCount} People'),
            ],
          ),
        );
      }
    }
  }
}
