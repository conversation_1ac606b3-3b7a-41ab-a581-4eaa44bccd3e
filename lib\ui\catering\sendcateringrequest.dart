import 'dart:convert';
import 'dart:developer';
import 'dart:async';

import 'package:db_eats/bloc/account_bloc.dart';
import 'package:db_eats/bloc/home_bloc.dart';
import 'package:db_eats/bloc/mealplan_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/data/models/guesthome/listaddressmodel.dart';
import 'package:db_eats/data/models/meal_plan/cateringtypemodel.dart';
import 'package:db_eats/data/models/meal_plan/cuisinelistingmodel.dart';
import 'package:db_eats/ui/catering/customdropdown.dart';
import 'package:db_eats/data/models/meal_plan/listdiatarymodel.dart';
import 'package:db_eats/data/models/meal_plan/spicelevellistmodel.dart';
import 'package:db_eats/data/models/meal_plan/timinglistmodel.dart';
import 'package:db_eats/data/models/meal_plan/typeofpackagingmodel.dart';
import 'package:db_eats/ui/catering/selectchef.dart';
import 'package:db_eats/widgets/cheflocationmodal.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:intl/intl.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shimmer/shimmer.dart';
import 'package:http/http.dart' as http;

class SendCateringRequestPage extends StatefulWidget {
  final int? chefid;

  const SendCateringRequestPage({super.key, this.chefid});

  @override
  State<SendCateringRequestPage> createState() =>
      _SendCateringRequestPageState();
}

class _SendCateringRequestPageState extends State<SendCateringRequestPage> {
  String dropOffOption = 'Meet at my door';
  int peopleCount = 0;
  DateTime? selectedDate;

  final GlobalKey<HierarchicalCuisineDropdownState> _cuisineDropdownKey =
      GlobalKey();

  String? selectedCity;
  int? selectedPackaging;

  List<AddressData>? _savedAddresses;
  AddressData? _currentAddressData;

  String? cuisineError;
  String? peopleCountError;
  String? dateError;
  String? timeError;
  String? streetError;
  String? aptError;
  String? zipError;
  String? stateError;
  String? cityError;
  String? packagingError;
  String? cateringError;
  String? dietaryError;
  String? spiceLevelError;
// No error for allergy (non-mandatory)

  final streetController = TextEditingController();
  final aptController = TextEditingController();
  final zipController = TextEditingController();
  final allergyController = TextEditingController();
  final cityController = TextEditingController();
  final stateController = TextEditingController();
  final peopleCountController = TextEditingController();
  int _currentIndex = 2;

  late PageController _pageController;
  void _onTabTapped(int index) {
    _pageController.jumpToPage(index);
    setState(() => _currentIndex = index);
  }

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: _currentIndex);
    // Leave peopleCountController empty initially
    // Dispatch events to fetch data
    context.read<MealplanBloc>().add(ListCuisineEvent());
    context.read<MealplanBloc>().add(ListTimingEvent());
    context.read<MealplanBloc>().add(ListPackagingTimeEvent());
    context.read<MealplanBloc>().add(ListDietaryEvent());
    context.read<MealplanBloc>().add(ListSpiceLevelEvent());
    context.read<MealplanBloc>().add(CateringTypeEvent());
    _loadAddresses();
  }

  Future<void> _loadAddresses() async {
    // setState(() => _loadingAddresses = true);
    try {
      context.read<AccountBloc>().add(ListAddressesEvent());
    } catch (e) {
      log('Error loading addresses: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to load addresses: $e')),
      );
    }
    // finally {
    //   setState(() => _loadingAddresses = false);
    // }
  }

  Future<void> _loadCurrentLocationCoordinates() async {
    try {
      final lat = Initializer().getLatitude;
      final lng = Initializer().getLongitude;

      if (lat != null && lng != null) {
        setState(() {
          _currentLatitude = lat;
          _currentLongitude = lng;
        });
        log('Set coordinates from Initializer: lat=$lat, lng=$lng');
      } else {
        log('No coordinates available from Initializer');
      }
    } catch (e) {
      log('Error loading coordinates from Initializer: $e');
    }
  }

  // Initialize lists for dropdowns
  List<Cuisines> cuisines = []; // filled from API
  List<String> cuisineNames = []; // extracted from cuisines
  String? selectedCuisine;
  int? selectedCuisineId;
  Map<String, List<int>> selectedCuisineData = {
    'cuisine_ids': [],
    'sub_cuisine_ids': [],
    'local_cuisine_ids': [],
  };

  List<Timings> times = [];
  List<String> timeLabels = [];
  List<String> packagingTypes = [];
  List<String> cateringtypes = [];
  List<PackagingTypes> packagingTypeList = [];
  List<CateringTypes> cateringTypeList = [];
  List<Map<String, dynamic>> dietaryPreferences = [];

  double? _currentLatitude;
  double? _currentLongitude;

  List<Dietaries> dietList = [];
  List<SpiceLevels> spiceLevels = [];
  String? selectedTime;
  int? selectedTimeId;
  String? selectedPackagingType;
  int? selectedPackagingTypeId;
  String? selectedCateringType;
  int? selectedCateringTypeId;
  List<Map<String, dynamic>> spiceLevelsList = [];
  String? _currentAddress;
  bool _showAddressDropdown = false;

  // Google Places search variables
  List<Prediction> _searchResults = [];
  bool _showPredictions = false;
  bool _isSearching = false;
  Timer? _debounce;
  final String kGoogleApiKey = "AIzaSyCpAdQaZ3fPe5H0wfkI0NqMXcT8J7AW9uY";

  int? _selectedSpiceLevelId;
  String? _selectedSpiceLevelName; // optional if you need the name too

  final List<Map<String, String>> _preferences = [
    {
      'name': 'Organic',
      'icon': 'assets/icons/organic.png',
    },
    {
      'name': 'Halal',
      'icon': 'assets/icons/halal.png',
    },
    {
      'name': 'Vegan',
      'icon': 'assets/icons/vegan.png',
    },
    {
      'name': 'Vegetarian',
      'icon': 'assets/icons/veg.png',
    },
  ];
  final List<int> _selectedPreferences = [];
  final List<String> _selectedSpiceLevel = [];

  @override
  void dispose() {
    streetController.dispose();
    aptController.dispose();
    zipController.dispose();
    allergyController.dispose();
    cityController.dispose();
    stateController.dispose();
    peopleCountController.dispose();
    _pageController.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  Widget _buildDropdownShimmer() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        height: eighteen * 2,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(thirtyTwo),
        ),
      ),
    );
  }

  Widget _buildListItemShimmer() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        margin: EdgeInsets.only(bottom: forteen),
        padding: EdgeInsets.all(sixteen),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Container(width: twenty, height: twenty, color: Colors.white),
            SizedBox(width: twenty - eighteen / eighteen),
            Container(width: twenty * 5, height: forteen, color: Colors.white),
          ],
        ),
      ),
    );
  }

  Widget _buildDropdown(
    String label,
    String? value,
    List<String> items,
    void Function(String?) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontFamily: 'Inter',
            fontWeight: FontWeight.w500,
            fontSize: forteen,
          ),
        ),
        SizedBox(height: twelve),
        Container(
          height: ten * 4,
          padding: EdgeInsets.symmetric(horizontal: eighteen),
          decoration: BoxDecoration(
            color: const Color(0xFFFFFFFF),
            border: Border.all(color: const Color(0xFFFFFFFF)),
            borderRadius: BorderRadius.circular(thirtyTwo),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: twelve / 3,
                offset: Offset(0, two),
              ),
            ],
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: value,
              isExpanded: true,
              hint: Text(
                label == 'State'
                    ? 'Select state'
                    : label == 'City'
                        ? 'Select city'
                        : label == 'ZIP Code'
                            ? 'Enter zip code'
                            : 'Select...',
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w400,
                  fontSize: sixteen,
                  color: Colors.black54,
                ),
              ),
              icon: const Icon(Icons.keyboard_arrow_down,
                  color: Color(0xFF1F2122)),
              dropdownColor: Colors.white,
              style: TextStyle(
                fontFamily: 'Inter',
                fontWeight: FontWeight.w400,
                fontSize: sixteen,
                color: Colors.black,
              ),
              items: items.map((e) {
                return DropdownMenuItem<String>(
                  value: e,
                  child: SizedBox(
                    width: double.infinity,
                    child: Text(
                      e,
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w400,
                        fontSize: forteen,
                        color: Colors.black,
                      ),
                    ),
                  ),
                );
              }).toList(),
              onChanged: onChanged,
            ),
          ),
        ),
        //     const SizedBox(height: 20),
      ],
    );
  }

  Widget _buildCounter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Indicate Number of People',
          style: TextStyle(
            fontFamily: 'Inter',
            fontWeight: FontWeight.w500,
            fontSize: forteen,
          ),
        ),
        SizedBox(height: twelve),
        Container(
          height: ten*4, // Use responsive height instead of 40
       
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: const Color(0xFFE1E3E6)),
            borderRadius: BorderRadius.circular(thirtyTwo),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: sixteen / 4,
                offset: Offset(0, two),
              )
            ],
          ),
          child: TextField(
            
            controller: peopleCountController,
            keyboardType: TextInputType.number,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
            ],
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: sixteen,
              fontWeight: FontWeight.w400,
              color: Colors.black,
            ),
            decoration: InputDecoration(
              isDense: true,
              contentPadding: EdgeInsets.symmetric(vertical: twelve/2,horizontal: eighteen),
              hintText: 'Enter number of people',
              border: InputBorder.none,
              hintStyle: TextStyle(
                fontFamily: 'Inter',
                fontSize: sixteen,
                color: Color(0xFFAAADB1),
              ),
            ),
            onChanged: (value) {
              final count = int.tryParse(value);
              if (count != null && count > 0) {
                setState(() {
                  peopleCount = count;
                });
              } else if (value.isEmpty) {
                setState(() {
                  peopleCount = 0;
                });
              }
            },
          ),
        ),
        SizedBox(height: twenty),
      ],
    );
  }

  Widget _buildDatePicker() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select Date',
          style: TextStyle(
            fontFamily: 'Inter',
            fontWeight: FontWeight.w500,
            fontSize: forteen,
          ),
        ),
        SizedBox(height: twelve),
        GestureDetector(
          onTap: () async {
            final date = await showDatePicker(
              context: context,
              initialDate:
                  selectedDate ?? DateTime.now().add(Duration(days: 1)),
              firstDate: DateTime.now().add(Duration(days: 1)),
              lastDate: DateTime(2100),
              builder: (context, child) {
                return Theme(
                  data: ThemeData.light().copyWith(
                    primaryColor: Color.fromARGB(255, 10, 10, 10),
                    colorScheme: const ColorScheme.light(
                      primary: Color.fromARGB(255, 8, 8, 8),
                    ),
                    dialogTheme: DialogThemeData(backgroundColor: Colors.white),
                  ),
                  child: child!,
                );
              },
            );
            if (date != null) {
              setState(() {
                selectedDate = date;
              });
            }
          },
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: sixteen, vertical: ten),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(color: const Color(0xFFE1E3E6)),
              borderRadius: BorderRadius.circular(thirtyTwo),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: sixteen / 4,
                  offset: Offset(0, two),
                )
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  selectedDate == null
                      ? 'Pick Date'
                      : DateFormat('E, MMM d').format(selectedDate!),
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontSize: sixteen,
                    color: Color(0xFF66696D),
                  ),
                ),
                SizedBox(width: sixteen / 2),
                Image.asset(
                  'assets/icons/calender.png',
                  width: twenty,
                  height: twenty,
                ),
              ],
            ),
          ),
        ),

        /// const SizedBox(height: 20),
      ],
    );
  }

  Widget _buildTextField(String hintText,
      {TextEditingController? controller, int maxLines = 1}) {
    return Container(
      margin: const EdgeInsets.only(bottom: 0),
      padding: EdgeInsets.symmetric(horizontal: eighteen),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: const Color(0xFFE1E3E6)),
        borderRadius: BorderRadius.circular(maxLines > 2 ? ten : thirtyTwo),
      ),
      child: SizedBox(
        height: maxLines == 1 ? 4*ten : null,
        child: TextField(
          controller: controller,
          maxLines: maxLines,
          style: TextStyle(fontSize: sixteen, height: 1.3),
          decoration: InputDecoration(
            isDense: true,
            contentPadding: EdgeInsets.symmetric(vertical: ten),
            hintText: hintText,
            border: InputBorder.none,
            hintStyle: TextStyle(
              fontFamily: 'Inter',
              fontSize: sixteen,
              height: 1.3,
              color: Color(0xFFAAADB1),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAddressField(String hintText,
      {TextEditingController? controller}) {
    return Column(
      children: [
        Container(
          margin: EdgeInsets.only(bottom: 0),
          padding: EdgeInsets.symmetric(horizontal: eighteen),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: const Color(0xFFE1E3E6)),
            borderRadius: BorderRadius.circular(thirtyTwo),
          ),
          child: SizedBox(
            height: forty,
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: controller,
                    style: TextStyle(fontSize: sixteen, height: 1.3),
                    onChanged: (value) {
                      // Auto-show dropdown when user starts typing
                      if (!_showAddressDropdown && value.isNotEmpty) {
                        setState(() {
                          _showAddressDropdown = true;
                        });
                      }
                      searchPlaces(value);
                    },
                    onTap: () {
                      // Show dropdown when user taps on the field
                      if (!_showAddressDropdown) {
                        setState(() {
                          _showAddressDropdown = true;
                        });
                      }
                    },
                    decoration: InputDecoration(
                      isDense: true,
                      contentPadding: EdgeInsets.symmetric(vertical: ten),
                      hintText: hintText,
                      border: InputBorder.none,
                      hintStyle: TextStyle(
                        fontFamily: 'Inter',
                        fontSize: sixteen,
                        height: 1.3,
                        color: Color(0xFFAAADB1),
                      ),
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _showAddressDropdown = !_showAddressDropdown;
                    });
                  },
                  child: Icon(
                    _showAddressDropdown
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_down,
                    color: const Color(0xFF1F2122),
                    size: twenty,
                  ),
                ),
              ],
            ),
          ),
        ),
        if (_showAddressDropdown)
          Container(
            margin: EdgeInsets.only(top: twelve / 2),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(color: const Color(0xFFE1E3E6)),
              borderRadius: BorderRadius.circular(10),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                // Show search predictions if user is typing
                if (_showPredictions && _searchResults.isNotEmpty) ...[
                  Padding(
                    padding: EdgeInsets.all(twelve),
                    child: Text(
                      'Search Results',
                      style: TextStyle(
                        fontSize: twelve,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF66696D),
                        fontFamily: 'Inter',
                      ),
                    ),
                  ),
                  ..._searchResults
                      .take(3)
                      .map((prediction) => _buildPredictionItem(prediction)),
                  if (_savedAddresses != null && _savedAddresses!.isNotEmpty)
                    Divider(height: 1, color: const Color(0xFFE1E3E6)),
                ],
                // Show saved addresses
                if (_savedAddresses != null && _savedAddresses!.isNotEmpty) ...[
                  if (!_showPredictions || _searchResults.isEmpty)
                    Padding(
                      padding: EdgeInsets.all(twelve),
                      child: Text(
                        'Saved Addresses',
                        style: TextStyle(
                          fontSize: twelve,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF66696D),
                          fontFamily: 'Inter',
                        ),
                      ),
                    ),
                  // Show current address first if it exists
                  if (_savedAddresses!.any((addr) => addr.isCurrent == true))
                    ..._savedAddresses!
                        .where((addr) => addr.isCurrent == true)
                        .map((address) => _buildAddressListItem(address,
                            isCurrentAddress: true)),
                  // Show up to 3 other addresses
                  ..._savedAddresses!
                      .where((addr) => addr.isCurrent != true)
                      .take(3)
                      .map((address) => _buildAddressListItem(address)),
                ],
                // Show message if no addresses and no search results
                if ((_savedAddresses == null || _savedAddresses!.isEmpty) &&
                    (!_showPredictions || _searchResults.isEmpty))
                  Padding(
                    padding: EdgeInsets.all(sixteen),
                    child: Text(
                      'No saved addresses. Start typing to search for a new address.',
                      style: TextStyle(
                        fontSize: forteen,
                        color: const Color(0xFF66696D),
                        fontFamily: 'Inter',
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildPredictionItem(Prediction prediction) {
    return InkWell(
      onTap: () => selectPlace(prediction),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: sixteen, vertical: twelve),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: const Color(0xFFE1E3E6),
              width: 0.5,
            ),
          ),
        ),
        child: Row(
          children: [
            Icon(
              Icons.search,
              size: eighteen,
              color: const Color(0xFF66696D),
            ),
            SizedBox(width: twelve),
            Expanded(
              child: Text(
                prediction.description ?? 'No description',
                style: TextStyle(
                  fontSize: forteen,
                  fontWeight: FontWeight.w400,
                  fontFamily: 'Inter',
                  color: const Color(0xFF1F2122),
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            if (_isSearching)
              SizedBox(
                width: sixteen,
                height: sixteen,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    const Color(0xFF66696D),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildAddressListItem(AddressData address,
      {bool isCurrentAddress = false}) {
    return InkWell(
      onTap: () => _selectAddress(address),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: sixteen, vertical: twelve),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: const Color(0xFFE1E3E6),
              width: 0.5,
            ),
          ),
        ),
        child: Row(
          children: [
            Icon(
              Icons.location_on_outlined,
              size: eighteen,
              color: isCurrentAddress ? Colors.green : const Color(0xFF66696D),
            ),
            SizedBox(width: twelve),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    address.addressText ?? 'No address text',
                    style: TextStyle(
                      fontSize: forteen,
                      fontWeight: FontWeight.w400,
                      fontFamily: 'Inter',
                      color: const Color(0xFF1F2122),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (isCurrentAddress)
                    Text(
                      'Current address',
                      style: TextStyle(
                        fontSize: twelve,
                        color: Colors.green,
                        fontFamily: 'Inter',
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSpiceIcons(int count) {
    return Row(
      children: List.generate(
          count,
          (index) => Padding(
                padding: EdgeInsets.only(left: sixteen / 4),
                child: Image.asset(
                  'assets/icons/spice.png',
                  width: eighteen,
                  height: eighteen,
                ),
              )),
    );
  }

  late double two;
  late double four;
  late double six;
  late double eight;
  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double thirtyTwo;
  late double forty;
  late double sixty;
  late double eighty;
  late double oneHundred;
  late double oneHundredFifty;

  late double screenHeight;
  late double screenWidth;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    two = screenWidth * 0.00509;
    four = screenWidth * 0.01018;
    six = screenWidth * 0.01527;
    eight = screenWidth * 0.02036;
    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
    thirtyTwo = screenWidth * 0.08145;
    forty = screenWidth * 0.10181;
    sixty = screenWidth * 0.15272;
    eighty = screenWidth * 0.20363;
    oneHundred = screenWidth * 0.25454;
    oneHundredFifty = screenWidth * 0.38181;
  }

  Future<void> _showLocationModalIfNeeded() async {
    if (_savedAddresses?.isEmpty ?? true) {
      _currentAddress = await Initializer.getAddress();
      if ((_currentAddress?.isEmpty ?? true) && mounted) {
        showDialog(
          context: context,
          builder: (_) => const ChefLocationModal(),
        );
      }
    }
  }

  void _selectAddress(AddressData address) {
    setState(() {
      streetController.text = address.addressText ?? '';
      _currentAddressData = address;
      _showAddressDropdown = false;

      // Update coordinates if available
      if (address.location?.coordinates != null) {
        _currentLatitude = address.location!.coordinates![1];
        _currentLongitude = address.location!.coordinates![0];
      }
    });
  }

  String? _extractAddressComponent(List addressComponents, String componentType,
      {bool useShortName = false}) {
    for (var component in addressComponents) {
      final types = component['types'] as List;
      if (types.contains(componentType)) {
        return useShortName ? component['short_name'] : component['long_name'];
      }
    }
    return null;
  }

  Future<void> searchPlaces(String query) async {
    if (query.isEmpty) {
      setState(() {
        _showPredictions = false;
        _searchResults = [];
        // Keep dropdown open to show saved addresses
      });
      return;
    }

    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 300), () async {
      setState(() => _isSearching = true);
      try {
        final url = Uri.parse(
          'https://maps.googleapis.com/maps/api/place/autocomplete/json?input=$query&key=$kGoogleApiKey',
        );
        final response = await http.get(url);
        if (response.statusCode == 200) {
          final json = jsonDecode(response.body);
          if (json['status'] == 'OK') {
            final predictions = json['predictions'] as List;
            setState(() {
              _searchResults = predictions
                  .map((p) => Prediction(
                        description: p['description'],
                        placeId: p['place_id'],
                      ))
                  .toList();
              _showPredictions = true;
            });
          } else {
            throw Exception('Places API error: ${json['status']}');
          }
        } else {
          throw Exception('Failed to fetch places: ${response.statusCode}');
        }
      } catch (e) {
        log('Error searching places: $e');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Failed to search places: $e')),
          );
        }
      } finally {
        if (mounted) {
          setState(() => _isSearching = false);
        }
      }
    });
  }

  Future<void> selectPlace(Prediction prediction) async {
    setState(() => _isSearching = true);
    try {
      final url = Uri.parse(
        'https://maps.googleapis.com/maps/api/place/details/json?place_id=${prediction.placeId}&fields=geometry,address_components,formatted_address&key=$kGoogleApiKey',
      );
      final response = await http.get(url);
      if (response.statusCode == 200) {
        final json = jsonDecode(response.body);
        if (json['status'] == 'OK') {
          final result = json['result'];
          final location = result['geometry']['location'];
          // Ensure we get doubles for coordinates
          final lat = location['lat'] as double;
          final lng = location['lng'] as double;
          final addressComponents = result['address_components'] as List;

          // Update coordinates first
          setState(() {
            _currentLatitude = lat;
            _currentLongitude = lng;

            // Clear current address data since we're selecting a new address
            _currentAddressData = null;

            // Update the street address
            streetController.text = prediction.description ?? '';

            // Close dropdowns
            _showAddressDropdown = false;
            _showPredictions = false;
            _searchResults = [];
          });

          log('New place selected - lat: $_currentLatitude, lng: $_currentLongitude');

          // Extract and update other address components
          String? zipCode =
              _extractAddressComponent(addressComponents, 'postal_code');
          String? state = _extractAddressComponent(
              addressComponents, 'administrative_area_level_1');
          String? city =
              _extractAddressComponent(addressComponents, 'locality') ??
                  _extractAddressComponent(
                      addressComponents, 'sublocality_level_1');

          if (zipCode != null) zipController.text = zipCode;
          if (state != null) stateController.text = state;
          if (city != null) cityController.text = city;
        } else {
          throw Exception('Place details API error: ${json['status']}');
        }
      } else {
        throw Exception(
            'Failed to fetch place details: ${response.statusCode}');
      }
    } catch (e) {
      log('Error selecting place: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to select place: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSearching = false);
      }
    }
  }

  void _submit() {
    // Log all fields
    // debugPrint("Cuisine: $selectedCuisineId");
    debugPrint("Cuisine: $selectedCuisineData");
    //debugPrint("Drop-Off Option: $dropOffOption");
    debugPrint("People Count: $peopleCount");
    debugPrint("Selected Date: ${selectedDate?.toIso8601String()}");
    debugPrint("Delivery Time: $selectedTimeId");
    debugPrint("Street: ${streetController.text}");
    debugPrint("Apartment: ${aptController.text}");
    debugPrint("Zip: ${zipController.text}");
    debugPrint("State: ${stateController.text}");
    debugPrint("City: ${cityController.text}");
    debugPrint("Packaging: $selectedPackagingTypeId");
    debugPrint("Dietary Preferences: $_selectedPreferences");
    debugPrint("Spice Level: $_selectedSpiceLevelId");
    debugPrint("Allergies: ${allergyController.text}");
    debugPrint("lat: $_currentLatitude");
    debugPrint("long: $_currentLongitude");
    debugPrint("catering type: $selectedCateringType");

    // Additional coordinate validation
    if (_currentLatitude == null || _currentLongitude == null) {
      log('WARNING: Coordinates are null! lat=$_currentLatitude, lng=$_currentLongitude');
      log('Current address data: ${_currentAddressData?.addressText}');
      log('Street controller text: ${streetController.text}');
    } else {
      log('Using coordinates: lat=$_currentLatitude, lng=$_currentLongitude for chef filtering');
    }

    // Validate all fields
    bool isValid = true;
    String errorMessage = '';

    bool hasCuisineSelection = selectedCuisineData['cuisine_ids']!.isNotEmpty ||
        selectedCuisineData['sub_cuisine_ids']!.isNotEmpty ||
        selectedCuisineData['local_cuisine_ids']!.isNotEmpty;

    if (!hasCuisineSelection) {
      isValid = false;
      errorMessage += 'Cuisine selection is missing.\n';
    }
    // if (dropOffOption == null || dropOffOption!.isEmpty) {
    //   isValid = false;
    //   errorMessage += 'Drop-Off Option is missing.\n';
    // }
    // Validate people count from text input
    final peopleCountText = peopleCountController.text.trim();
    final parsedPeopleCount = int.tryParse(peopleCountText);
    if (parsedPeopleCount == null || parsedPeopleCount <= 0) {
      isValid = false;
      errorMessage += 'People Count is invalid.\n';
    } else {
      // Update peopleCount variable with parsed value
      peopleCount = parsedPeopleCount;
    }
    if (selectedDate == null) {
      isValid = false;
      errorMessage += 'Selected Date is missing.\n';
    }
    if (selectedTimeId == null) {
      isValid = false;
      errorMessage += 'Delivery Time is missing.\n';
    }
    if (streetController.text.isEmpty) {
      isValid = false;
      errorMessage += 'Street is missing.\n';
    }

    if (zipController.text.isEmpty) {
      isValid = false;
      errorMessage += 'Zip is missing.\n';
    }
    if (stateController.text.isEmpty) {
      isValid = false;
      errorMessage += 'State is missing.\n';
    }
    if (cityController.text.isEmpty) {
      isValid = false;
      errorMessage += 'City is missing.\n';
    }
    if (selectedPackagingTypeId == null) {
      isValid = false;
      errorMessage += 'Packaging is missing.\n';
    }
    if (selectedCateringTypeId == null) {
      isValid = false;
      errorMessage += 'Catering is missing.\n';
    }
    if ((_selectedPreferences.isEmpty)) {
      isValid = false;
      errorMessage += 'Dietary Preferences are missing.\n';
    }
    if (_selectedSpiceLevelId == null) {
      isValid = false;
      errorMessage += 'Spice Level is missing.\n';
    }

    setState(() {
      cuisineError = selectedCuisineData['cuisine_ids']!.isEmpty &&
              selectedCuisineData['sub_cuisine_ids']!.isEmpty &&
              selectedCuisineData['local_cuisine_ids']!.isEmpty
          ? 'Please select at least one cuisine'
          : null;

      // Validate people count input
      final peopleCountText = peopleCountController.text.trim();
      final parsedPeopleCount = int.tryParse(peopleCountText);
      peopleCountError = (parsedPeopleCount == null || parsedPeopleCount <= 0)
          ? 'Enter a valid number of people'
          : null;

      dateError = selectedDate == null ? 'Select a date' : null;
      timeError = selectedTimeId == null ? 'Select a delivery time' : null;
      streetError = streetController.text.isEmpty ? 'Enter street' : null;

      zipError = zipController.text.isEmpty ? 'Enter ZIP code' : null;
      stateError = stateController.text.isEmpty ? 'Enter state' : null;
      cityError = cityController.text.isEmpty ? 'Enter city' : null;
      packagingError =
          selectedPackagingTypeId == null ? 'Select packaging' : null;
      cateringError = selectedCateringTypeId == null ? 'Select Catering' : null;
      dietaryError =
          _selectedPreferences.isEmpty ? 'Select dietary preference' : null;
      spiceLevelError =
          _selectedSpiceLevelId == null ? 'Select spice level' : null;
    });

    // Log validation result
    if (isValid) {
      String formattedDate = DateFormat('yyyy-MM-dd').format(selectedDate!);
      int firstPreferenceAlt = _selectedPreferences[0];
      debugPrint(
          "All fields are valid. Dispatching FindChefEvent and navigating to CateringRequestsPage.");
      BlocProvider.of<MealplanBloc>(context).add(
        FindChefEvent({
          "packaging_type_id": selectedPackagingTypeId,
          "time_slot_id": selectedTimeId,
          "date": formattedDate,
          "cuisine_ids": selectedCuisineData['cuisine_ids'],
          "sub_cuisine_ids": selectedCuisineData['sub_cuisine_ids'],
          "local_cuisine_ids": selectedCuisineData['local_cuisine_ids'],
          "dietary_preference_id": firstPreferenceAlt,
          "spice_level_id": _selectedSpiceLevelId,
          "latitude": _currentLatitude,
          "longitude": _currentLongitude,
          "catering_type_id": selectedCateringTypeId,
        }),
      );
      debugPrint("All fields are valid. Navigating to CateringRequestsPage.");
      // Navigator.push(
      //   context,
      //   MaterialPageRoute(
      //     builder: (context) => CateringRequestsPage(),
      //   ),
      // );
    } else {
      Fluttertoast.showToast(
        msg: "Please fill in all required fields.",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );
      debugPrint("Validation failed:\n$errorMessage");
    }
  }

  // @override
  // Widget build(BuildContext context) {
  //   return Scaffold(
  //       backgroundColor: const Color(0xFFf6f3ec),
  //       appBar: AppBar(
  //         backgroundColor: const Color(0xFFf6f3ec),
  //         elevation: 0,
  //         scrolledUnderElevation: 0,
  //         automaticallyImplyLeading: false,
  //         title: const Text('Send a catering request',
  //             style: TextStyle(
  //                 fontFamily: 'Inter',
  //                 fontWeight: FontWeight.w600,
  //                 fontSize: 28,
  //                 color: Color(0xFF1F2122))),
  //       },
  //       body:

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<MealplanBloc>.value(
          value: context.read<MealplanBloc>(),
        ),
        BlocProvider<AccountBloc>.value(
          value: context.read<AccountBloc>(),
        ),
      ],
      child: Scaffold(
        backgroundColor: const Color(0xFFf6f3ec),
        appBar: AppBar(
          backgroundColor: const Color(0xFFf6f3ec),
          elevation: 0,
          centerTitle: false,
          scrolledUnderElevation: 0,
          automaticallyImplyLeading: false,
          title: Text('Send a catering request',
              style: TextStyle(
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w600,
                  fontSize: eighteen,
                  color: const Color(0xFF1F2122))),
        ),
        body: BlocListener<AccountBloc, AccountState>(
          listener: (context, state) {
            if (state is ListAddressesSuccess) {
              setState(() {
                _savedAddresses = state.data;
                _currentAddressData = _savedAddresses?.firstWhere(
                  (address) => address.isCurrent == true,
                  orElse: () => _savedAddresses?.isNotEmpty == true
                      ? _savedAddresses!.first
                      : AddressData(),
                );

                // Only set address and coordinates if no address is currently selected
                if (streetController.text.isEmpty &&
                    _currentAddressData?.addressText != null) {
                  streetController.text = _currentAddressData!.addressText!;

                  // Set coordinates from current address if available
                  if (_currentAddressData?.location?.coordinates != null) {
                    _currentLatitude =
                        _currentAddressData!.location!.coordinates![1];
                    _currentLongitude =
                        _currentAddressData!.location!.coordinates![0];
                    log('Set initial coordinates from saved address: lat=$_currentLatitude, lng=$_currentLongitude');
                  }
                }
              });
              WidgetsBinding.instance.addPostFrameCallback((_) {
                _showLocationModalIfNeeded();
              });
              if (_currentAddressData?.location?.coordinates != null) {
                final lat = _currentAddressData!.location!.coordinates![1];
                final lng = _currentAddressData!.location!.coordinates![0];

                setState(() {
                  _currentLatitude = lat;
                  _currentLongitude = lng;
                });

                Initializer().setCoordinates(lat, lng);
                context.read<HomeBloc>().add(
                      GetHomeDataEvent(
                        data: {
                          'latitude': lat,
                          'longitude': lng,
                        },
                      ),
                    );
              }
            } else if (state is AddAddressSuccess) {
              // Refresh address list when a new address is added
              _loadAddresses();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Address added successfully')),
              );
            }
          },
          child: BlocListener<MealplanBloc, MealPlanState>(
            listener: (context, state) {
              // Handle Cuisine States
              if (state is ListCuisineSuccess) {
                setState(() {
                  cuisines = state.data.data?.cuisines ?? [];
                  cuisineNames = cuisines.map((c) => c.name ?? '').toList();
                  log("Cuisines : ${cuisines.toString()}");
                  log("Cuisines updated: ${cuisineNames.toString()}");
                });
              }
              if (state is FindChefEventSuccess) {
                String formattedDate =
                    DateFormat('yyyy-MM-dd').format(selectedDate!);
                int firstPreferenceAlt = _selectedPreferences[0];
                Map<String, dynamic> userData = {
                  "packaging_type_id": selectedPackagingTypeId,
                  "time_slot_id": selectedTimeId,
                  "date": formattedDate,
                  "people_count": peopleCount,
                  "cuisine_ids": selectedCuisineData['cuisine_ids'],
                  "sub_cuisine_ids": selectedCuisineData['sub_cuisine_ids'],
                  "local_cuisine_ids": selectedCuisineData['local_cuisine_ids'],
                  "dietary_preference_id": firstPreferenceAlt,
                  "spice_level_id": _selectedSpiceLevelId,
                  "latitude": _currentLatitude,
                  "longitude": _currentLongitude,
                  "catering_type_id": selectedCateringTypeId,
                  // "delivery_time_id": selectedTimeId,
                  "address": streetController.text + aptController.text,
                  "state": stateController.text,
                  "city": cityController.text,
                  "zip_code": zipController.text,
                  "allergy_prference_text": allergyController.text,
                };

                Navigator.of(context)
                    .push(MaterialPageRoute(builder: (context) {
                  return SelectChef(
                    userData: userData,
                    chefData: state
                        .data, // Assuming state.data contains the chef data
                  );
                }));
              }

              // chefData: {
              //   "total": 2,
              //   "page": 1,
              //   "limit": 20,
              //   "chefs": [
              //     {
              //       "chef_id": 2,
              //       "profile_photo": "1747394618985.jpg",
              //       "cover_photo": "1747394658968.jpg",
              //       "location": {
              //         "crs": {
              //           "type": "name",
              //           "properties": {"name": "EPSG:4326"}
              //         },
              //         "type": "Point",
              //         "coordinates": [76.8201627, 9.5335193]
              //       },
              //       "search_tags": ["sdfghjkl"],
              //       "distance": 701.14709392,
              //       "chef": {
              //         "first_name": "Edwin",
              //         "last_name": "Cy",
              //         "operation_days": [
              //           {
              //             "day_id": 6,
              //             "day": {"name": "Sat"}
              //           },
              //           {
              //             "day_id": 7,
              //             "day": {"name": "Sun"}
              //           }
              //         ],
              //         "operation_times": [
              //           {"timing_id": 4},
              //           {"timing_id": 3},
              //           {"timing_id": 2}
              //         ],
              //         "catering_dishes": [
              //           {
              //             "id": 2,
              //             "order_type": 2,
              //             "title": "Chicken Soup ",
              //             "serving_size": 6,
              //             "price": "99.99",
              //             "photo": "1747393650944.jpg",
              //             "listing_order": null
              //           },
              //           {
              //             "id": 3,
              //             "order_type": 2,
              //             "title": "Chicken Soup ",
              //             "serving_size": 6,
              //             "price": "99.99",
              //             "photo": "1747393651487.jpg",
              //             "listing_order": null
              //           }
              //         ]
              //       }
              //     },
              //     {
              //       "chef_id": 3,
              //       "profile_photo": "1749462393975.jpeg",
              //       "cover_photo": "1749462393990.jpeg",
              //       "location": {
              //         "crs": {
              //           "type": "name",
              //           "properties": {"name": "EPSG:4326"}
              //         },
              //         "type": "Point",
              //         "coordinates": [
              //           76.82227101609595,
              //           9.527852246897147
              //         ]
              //       },
              //       "search_tags": ["Kerala", "Tamil Nadu"],
              //       "distance": 1346.97605286,
              //       "chef": {
              //         "first_name": "Jesso",
              //         "last_name": "Joseph ",
              //         "operation_days": [
              //           {
              //             "day_id": 1,
              //             "day": {"name": "Mon"}
              //           },
              //           {
              //             "day_id": 2,
              //             "day": {"name": "Tue"}
              //           },
              //           {
              //             "day_id": 3,
              //             "day": {"name": "Wed"}
              //           },
              //           {
              //             "day_id": 4,
              //             "day": {"name": "Thu"}
              //           },
              //           {
              //             "day_id": 5,
              //             "day": {"name": "Fri"}
              //           },
              //           {
              //             "day_id": 6,
              //             "day": {"name": "Sat"}
              //           },
              //           {
              //             "day_id": 7,
              //             "day": {"name": "Sun"}
              //           }
              //         ],
              //         "operation_times": [
              //           {"timing_id": 3},
              //           {"timing_id": 4},
              //           {"timing_id": 2},
              //           {"timing_id": 1}
              //         ],
              //         "catering_dishes": [
              //           {
              //             "id": 24,
              //             "order_type": 1,
              //             "title": "Wedding",
              //             "serving_size": 4,
              //             "price": "789.00",
              //             "photo": "1749440223045.jpg",
              //             "listing_order": null
              //           },
              //           {
              //             "id": 25,
              //             "order_type": 2,
              //             "title": "rice",
              //             "serving_size": 3,
              //             "price": "989.00",
              //             "photo": "1749440256509.jpg",
              //             "listing_order": null
              //           },
              //           {
              //             "id": 27,
              //             "order_type": 1,
              //             "title": "jnjn",
              //             "serving_size": 3,
              //             "price": "345.00",
              //             "photo": "1749440271705.png",
              //             "listing_order": null
              //           },
              //           {
              //             "id": 28,
              //             "order_type": 1,
              //             "title": "ddx",
              //             "serving_size": 3,
              //             "price": "4554.00",
              //             "photo": "1749440284532.jpg",
              //             "listing_order": null
              //           }
              //         ]
              //       }
              //     }
              //   ]
              // },
              // Handle Timing States
              else if (state is ListTimingSuccess) {
                setState(() {
                  times = state.data.data?.timings ?? [];
                  timeLabels = times.map((timing) {
                    final startTime = DateFormat('h:mm a').format(
                        DateFormat('HH:mm:ss')
                            .parse(timing.startTime ?? '00:00:00'));
                    final endTime = DateFormat('h:mm a').format(
                        DateFormat('HH:mm:ss')
                            .parse(timing.endTime ?? '00:00:00'));
                    return '$startTime - $endTime';
                  }).toList();
                  log("Times updated: ${timeLabels.toString()}");
                });
              }

              // Handle Packaging States
              else if (state is ListPackagingTimeSucess) {
                setState(() {
                  packagingTypeList = state.data.data?.packagingTypes ?? [];
                  packagingTypes =
                      packagingTypeList.map((p) => p.name ?? '').toList();
                  log("Packaging updated: ${packagingTypes.toString()}");
                });
              } else if (state is CateringTypeSuccess) {
                setState(() {
                  cateringTypeList = state.data.data?.cateringTypes ?? [];
                  cateringtypes =
                      cateringTypeList.map((p) => p.name ?? '').toList();
                  log("catering updated: ${cateringtypes.toString()}");
                });
              }

              // Handle Dietary Preferences States
              else if (state is ListDietarySuccess) {
                setState(() {
                  final dietList = state.data.data?.dietaries ?? [];
                  dietaryPreferences = dietList
                      .map((diet) {
                        final Map<String, String> pref =
                            _preferences.firstWhere(
                          (p) => p['name'] == diet.name,
                          orElse: () => {
                            'name': diet.name ?? '',
                            'icon': 'assets/icons/default.png',
                          },
                        );

                        return {
                          'id': diet.id,
                          'name': diet.name ?? '',
                          'icon': pref['icon'] ?? 'assets/icons/default.png',
                        };
                      })
                      .toList()
                      .cast<Map<String, dynamic>>();
                  log("Dietary preferences updated: ${dietaryPreferences.toString()}");
                });
              }

              // Handle Spice Level States
              else if (state is ListSpiceLevelSuccess) {
                setState(() {
                  spiceLevels = state.data.data?.spiceLevels ?? [];
                  spiceLevelsList = spiceLevels
                      .asMap()
                      .entries
                      .map((entry) {
                        final index = entry.key;
                        final spice = entry.value;
                        return {
                          'id': spice.id ?? 0,
                          'level': spice.name ?? '',
                          'count': index,
                        };
                      })
                      .toList()
                      .cast<Map<String, dynamic>>();
                  log("Spice levels updated: ${spiceLevelsList.toString()}");
                });
              }
            },
            child: BlocBuilder<MealplanBloc, MealPlanState>(
              builder: (context, state) {
                // Only show loading states here, no data manipulation
                return GestureDetector(
                  onTap: () {
                    // Close address dropdown and search predictions when tapping outside
                    if (_showAddressDropdown) {
                      setState(() {
                        _showAddressDropdown = false;
                        _showPredictions = false;
                        _searchResults = [];
                      });
                    }

                    // Close cuisine dropdown when tapping outside
                    if (_cuisineDropdownKey.currentState != null) {
                      _cuisineDropdownKey.currentState!.closeDropdown();
                    }

                    // Unfocus any focused text fields
                    FocusScope.of(context).unfocus();
                  },
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "Lorem ipsum dolor sit amet, consectetur adipisci.",
                            style: TextStyle(
                                fontFamily: 'Inter',
                                fontWeight: FontWeight.w400,
                                fontSize: forteen,
                                color: Color(0xFF414346)),
                          ),
                          SizedBox(height: eighteen),

                          // Packaging Dropdown with shimmer
                          cateringtypes.isEmpty
                              ? _buildDropdownShimmer()
                              : _buildDropdown(
                                  "Select Catering Type or Event",
                                  selectedCateringType,
                                  cateringtypes,
                                  (v) {
                                    setState(() {
                                      selectedCateringType = v;
                                      final matched =
                                          cateringTypeList.firstWhere(
                                        (p) => p.name == v,
                                        orElse: () => CateringTypes(),
                                      );
                                      selectedCateringTypeId = matched.id;
                                    });
                                  },
                                ),
                          if (cateringError != null)
                            Padding(
                              padding: EdgeInsets.only(
                                  left: eighteen, top: sixteen / 8),
                              child: Text(
                                cateringError!,
                                style: TextStyle(
                                    color: Colors.red, fontSize: twelve),
                              ),
                            ),
                          SizedBox(
                            height: twenty,
                          ),

                          // Cuisine Dropdown with shimmer
                          cuisineNames.isEmpty
                              ? _buildDropdownShimmer()
                              : HierarchicalCuisineDropdown(
                                  key:
                                      _cuisineDropdownKey, // Make sure this key is assigned
                                  cuisines: cuisines,
                                  label: "Select A Cuisine",
                                  onSelectionChanged: (selection) {
                                    setState(() {
                                      selectedCuisineData = selection;
                                    });
                                    debugPrint("Selected cuisines: $selection");
                                  },
                                  initialSelection: selectedCuisineData,
                                ),
                          if (cuisineError != null)
                            Padding(
                              padding: EdgeInsets.only(
                                  left: eighteen, top: sixteen / 8),
                              child: Text(
                                cuisineError!,
                                style: TextStyle(
                                    color: Colors.red, fontSize: twelve),
                              ),
                            ),
                          SizedBox(
                            height: twenty,
                          ),
                          SizedBox(height: twelve),
                          _buildCounter(),
                          if (peopleCountError != null)
                            Padding(
                              padding: EdgeInsets.only(
                                  left: eighteen, top: sixteen / 8),
                              child: Text(
                                peopleCountError!,
                                style: TextStyle(
                                    color: Colors.red, fontSize: twelve),
                              ),
                            ),
                          SizedBox(height: twelve),
                          _buildDatePicker(),
                          if (dateError != null)
                            Padding(
                              padding: EdgeInsets.only(
                                  left: eighteen, top: sixteen / 8),
                              child: Text(
                                dateError!,
                                style: TextStyle(
                                    color: Colors.red, fontSize: twelve),
                              ),
                            ),
                          SizedBox(
                            height: twenty,
                          ),
                          SizedBox(height: twelve),

                          // Timing Dropdown with shimmer
                          timeLabels.isEmpty
                              ? _buildDropdownShimmer()
                              : _buildDropdown(
                                  "Delivery Time",
                                  selectedTime,
                                  timeLabels,
                                  (v) {
                                    setState(() {
                                      selectedTime = v;
                                      final start =
                                          v?.split(' - ').first.trim();
                                      final parsedStart =
                                          DateFormat('HH:mm:ss').format(
                                        DateFormat('h:mm a').parse(start!),
                                      );
                                      final matched = times.firstWhere(
                                        (t) => t.startTime == parsedStart,
                                        orElse: () => Timings(),
                                      );
                                      selectedTimeId = matched.id;
                                    });
                                  },
                                ),
                          if (timeError != null)
                            Padding(
                              padding: EdgeInsets.only(
                                  left: eighteen, top: sixteen / 8),
                              child: Text(
                                timeError!,
                                style: TextStyle(
                                    color: Colors.red, fontSize: twelve),
                              ),
                            ),
                          SizedBox(
                            height: twenty,
                          ),
                          SizedBox(height: ten),

                          // Address fields (no shimmer needed)
                          Text('Address',
                              style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontWeight: FontWeight.w500,
                                  fontSize: forteen)),
                          SizedBox(height: sixteen / 2),
                          _buildAddressField("Street address",
                              controller: streetController),
                          if (streetError != null)
                            Padding(
                              padding: EdgeInsets.only(
                                  left: eighteen, top: sixteen / 8),
                              child: Text(
                                streetError!,
                                style: TextStyle(
                                    color: Colors.red, fontSize: twelve),
                              ),
                            ),
                          SizedBox(
                            height: twelve,
                          ),
                          _buildTextField("Apartment, suite, unit, etc.",
                              controller: aptController),
                          if (aptError != null)
                            Padding(
                              padding: EdgeInsets.only(
                                  left: eighteen, top: sixteen / 8),
                              child: Text(
                                aptError!,
                                style: TextStyle(
                                    color: Colors.red, fontSize: twelve),
                              ),
                            ),
                          SizedBox(
                            height: twelve,
                          ),
                          Text("State",
                              style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontWeight: FontWeight.w500,
                                  fontSize: forteen)),
                          SizedBox(height: sixteen / 2),
                          _buildTextField("Enter state",
                              controller: stateController),
                          if (stateError != null)
                            Padding(
                              padding: EdgeInsets.only(
                                  left: eighteen, top: sixteen / 8),
                              child: Text(
                                stateError!,
                                style: TextStyle(
                                    color: Colors.red, fontSize: twelve),
                              ),
                            ),
                          SizedBox(
                            height: twenty,
                          ),
                          _buildTextField("City", controller: cityController),
                          if (cityError != null)
                            Padding(
                              padding: EdgeInsets.only(
                                  left: eighteen, top: sixteen / 8),
                              child: Text(
                                cityError!,
                                style: TextStyle(
                                    color: Colors.red, fontSize: twelve),
                              ),
                            ),
                          SizedBox(
                            height: twenty,
                          ),
                          Text("Zip Code",
                              style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontWeight: FontWeight.w500,
                                  fontSize: forteen)),
                          SizedBox(height: sixteen / 2),
                          _buildTextField("Enter zip code",
                              controller: zipController),
                          if (zipError != null)
                            Padding(
                              padding: EdgeInsets.only(
                                  left: eighteen, top: sixteen / 8),
                              child: Text(
                                zipError!,
                                style: TextStyle(
                                    color: Colors.red, fontSize: twelve),
                              ),
                            ),
                          SizedBox(
                            height: twelve,
                          ),
                          SizedBox(height: sixteen),

                          // Packaging Dropdown with shimmer
                          packagingTypes.isEmpty
                              ? _buildDropdownShimmer()
                              : _buildDropdown(
                                  "Select Packaging Type",
                                  selectedPackagingType,
                                  packagingTypes,
                                  (v) {
                                    setState(() {
                                      selectedPackagingType = v;
                                      final matched =
                                          packagingTypeList.firstWhere(
                                        (p) => p.name == v,
                                        orElse: () => PackagingTypes(),
                                      );
                                      selectedPackagingTypeId = matched.id;
                                    });
                                  },
                                ),
                          if (packagingError != null)
                            Padding(
                              padding: EdgeInsets.only(
                                  left: eighteen, top: sixteen / 8),
                              child: Text(
                                packagingError!,
                                style: TextStyle(
                                    color: Colors.red, fontSize: twelve),
                              ),
                            ),
                          SizedBox(
                            height: twenty,
                          ),

                          SizedBox(height: twenty),

                          // Dietary Preferences with shimmer
                          Text("Choose dietary preferences",
                              style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontWeight: FontWeight.w500,
                                  fontSize: forteen,
                                  color: Color(0xFF1F2122))),
                          SizedBox(height: sixteen / 2),
                          dietaryPreferences.isEmpty
                              ? Column(
                                  children: List.generate(
                                      4, (index) => _buildListItemShimmer()))
                              : ListView.builder(
                                  shrinkWrap: true,
                                  physics: const NeverScrollableScrollPhysics(),
                                  itemCount: dietaryPreferences.length,
                                  itemBuilder: (context, index) {
                                    final pref = dietaryPreferences[index];
                                    final name = pref['name'] as String;
                                    final icon = pref['icon'] as String;
                                    final id = pref['id']
                                        as int; // Assuming id is String
                                    final isSelected =
                                        _selectedPreferences.contains(id);

                                    return Padding(
                                      padding: EdgeInsets.only(bottom: forteen),
                                      child: InkWell(
                                        onTap: () {
                                          setState(() {
                                            if (isSelected) {
                                              _selectedPreferences.remove(id);
                                            } else {
                                              _selectedPreferences
                                                ..clear()
                                                ..add(id); // Single selection
                                            }
                                          });
                                        },
                                        child: Container(
                                          padding: EdgeInsets.all(sixteen),
                                          decoration: BoxDecoration(
                                            color: isSelected
                                                ? const Color(0xFFE1DDD5)
                                                : const Color(0xFFF6F3EC),
                                            border: Border.all(
                                                color: isSelected
                                                    ? const Color(0xFF1F2122)
                                                    : const Color(0xFFB9B6AD)),
                                            borderRadius:
                                                BorderRadius.circular(8),
                                          ),
                                          child: Row(
                                            children: [
                                              Image.asset(icon,
                                                  width: twenty,
                                                  height: twenty),
                                              SizedBox(width: twenty),
                                              Text(name,
                                                  style: TextStyle(
                                                      fontSize: forteen,
                                                      fontWeight:
                                                          FontWeight.w600,
                                                      fontFamily: 'Inter')),
                                            ],
                                          ),
                                        ),
                                      ),
                                    );
                                  },
                                ),
                          if (dietaryError != null)
                            Padding(
                              padding: EdgeInsets.only(
                                  left: eighteen, top: sixteen / 8),
                              child: Text(
                                dietaryError!,
                                style: TextStyle(
                                    color: Colors.red, fontSize: twelve),
                              ),
                            ),
                          SizedBox(
                            height: twenty,
                          ),
                          SizedBox(height: twentyFour),

                          // Spice Levels with shimmer
                          Text('Choose spice level',
                              style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontWeight: FontWeight.w500,
                                  fontSize: forteen,
                                  color: Colors.grey[900])),
                          SizedBox(height: sixteen / 2),
                          spiceLevelsList.isEmpty
                              ? Column(
                                  children: List.generate(
                                      3, (index) => _buildListItemShimmer()))
                              : Column(
                                  children: spiceLevelsList.map((level) {
                                    final isSelected =
                                        _selectedSpiceLevelId == level['id'];
                                    return Container(
                                      margin: const EdgeInsets.only(bottom: 14),
                                      decoration: BoxDecoration(
                                        color: isSelected
                                            ? const Color(0xFFE1DDD5)
                                            : const Color(0xFFF6F3EC),
                                        border: Border.all(
                                            color: isSelected
                                                ? Colors.black
                                                : const Color(0xFFB9B6AD)),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: InkWell(
                                        onTap: () => setState(() {
                                          _selectedSpiceLevelId = level['id'];
                                          _selectedSpiceLevelName =
                                              level['level'];
                                        }),
                                        child: Padding(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: sixteen,
                                              vertical: sixteen),
                                          child: Row(
                                            children: [
                                              Expanded(
                                                  child: Text(level['level'],
                                                      style: TextStyle(
                                                          fontSize: forteen,
                                                          fontWeight:
                                                              FontWeight.w600,
                                                          fontFamily:
                                                              'Inter'))),
                                              if (level['count'] == 0)
                                                Image.asset(
                                                    'assets/icons/close_2.png',
                                                    width: twenty,
                                                    height: twenty)
                                              else
                                                _buildSpiceIcons(
                                                    level['count']),
                                            ],
                                          ),
                                        ),
                                      ),
                                    );
                                  }).toList(),
                                ),
                          if (spiceLevelError != null)
                            Padding(
                              padding: EdgeInsets.only(left: eighteen, top: 0),
                              child: Text(
                                spiceLevelError!,
                                style: TextStyle(
                                    color: Colors.red, fontSize: twelve),
                              ),
                            ),
                          SizedBox(
                            height: forteen,
                          ),
                          SizedBox(
                            height: twelve,
                          ),
                          SizedBox(height: twenty),
                          Text(
                              "Please Indicate Any Food Allergies or Preferences",
                              style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontWeight: FontWeight.w500,
                                  fontSize: forteen)),
                          SizedBox(height: ten),
                          _buildTextField("Type in your answer",
                              controller: allergyController, maxLines: 7),
                          SizedBox(height: twentyFour),

                          SizedBox(
                            height: ten * 4 + forteen,
                            width: double.infinity,
                            child: ElevatedButton(
                              onPressed: state is FindChefEventLoading
                                  ? null
                                  : _submit,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color(0xFF1F2122),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(32),
                                ),
                              ),
                              child: state is FindChefEventLoading
                                  ? SizedBox(
                                      width: twentyFour,
                                      height: twentyFour,
                                      child: CircularProgressIndicator(
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                                Colors.white),
                                        strokeWidth: 3,
                                      ),
                                    )
                                  : Text(
                                      'Send Request',
                                      style: TextStyle(
                                        fontFamily: 'Inter',
                                        fontSize: sixteen,
                                        fontWeight: FontWeight.w400,
                                        letterSpacing: 0.32,
                                        color: Colors.white,
                                      ),
                                    ),
                            ),
                          ),

                          SizedBox(height: ten * 4),
                        ]),
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}

class Prediction {
  final String? description;
  final String? placeId;

  Prediction({this.description, this.placeId});
}
