class ViewCartModel {
  bool? status;
  Data? data;
  int? statusCode;

  ViewCartModel({this.status, this.data, this.statusCode});

  ViewCartModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
    statusCode = json['status_code'];
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      if (data != null) 'data': data!.toJson(),
      'status_code': statusCode,
    };
  }
}

class Data {
  Chef? chef;
  List<Items>? items;
  int? totalPrice;
  dynamic customerTimePreference;
  CurrentAddress? currentAddress;

  Data({
    this.chef,
    this.items,
    this.totalPrice,
    this.customerTimePreference,
    this.currentAddress,
  });

  Data.fromJson(Map<String, dynamic> json) {
    chef = json['chef'] != null ? Chef.fromJson(json['chef']) : null;
    if (json['items'] != null) {
      items = List<Items>.from(json['items'].map((v) => Items.fromJson(v)));
    }
    totalPrice = json['total_price'];
    customerTimePreference = json['customer_time_preference'];
    currentAddress = json['current_address'] != null
        ? CurrentAddress.fromJson(json['current_address'])
        : null;
  }

  Map<String, dynamic> toJson() {
    return {
      if (chef != null) 'chef': chef!.toJson(),
      if (items != null) 'items': items!.map((v) => v.toJson()).toList(),
      'total_price': totalPrice,
      'customer_time_preference': customerTimePreference,
      'current_address': currentAddress?.toJson(),
    };
  }
}

class Chef {
  int? chefId;
  String? chefName;
  String? chefPhoto;
  List<String>? chefOperationDays;
  ChefOperationTime? chefOperationTime;

  Chef({
    this.chefId,
    this.chefName,
    this.chefPhoto,
    this.chefOperationDays,
    this.chefOperationTime,
  });

  Chef.fromJson(Map<String, dynamic> json) {
    chefId = json['chef_id'];
    chefName = json['chef_name'];
    chefPhoto = json['chef_photo'];
    chefOperationDays = json['chef_operation_days'] != null
        ? List<String>.from(json['chef_operation_days'])
        : null;
    chefOperationTime = json['chef_operation_time'] != null
        ? ChefOperationTime.fromJson(json['chef_operation_time'])
        : null;
  }

  Map<String, dynamic> toJson() {
    return {
      'chef_id': chefId,
      'chef_name': chefName,
      'chef_photo': chefPhoto,
      'chef_operation_days': chefOperationDays,
      if (chefOperationTime != null)
        'chef_operation_time': chefOperationTime!.toJson(),
    };
  }
}

class ChefOperationTime {
  int? id;
  String? startTime;
  String? endTime;

  ChefOperationTime({this.id, this.startTime, this.endTime});

  ChefOperationTime.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    startTime = json['start_time'];
    endTime = json['end_time'];
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'start_time': startTime,
      'end_time': endTime,
    };
  }
}

class Items {
  int? cartItemId;
  int? dishId;
  String? dishName;
  String? dishPhoto;
  int? quantity;
  int? servingSizeId;
  int? price;
  int? totalPrice;
  dynamic notes;

  Items({
    this.cartItemId,
    this.dishId,
    this.dishName,
    this.dishPhoto,
    this.quantity,
    this.servingSizeId,
    this.price,
    this.totalPrice,
    this.notes,
  });

  Items.fromJson(Map<String, dynamic> json) {
    cartItemId = json['cart_item_id'];
    dishId = json['dish_id'];
    dishName = json['dish_name'];
    dishPhoto = json['dish_photo'];
    quantity = json['quantity'];
    servingSizeId = json['serving_size_id'];
    price = json['price'];
    totalPrice = json['total_price'];
    notes = json['notes'];
  }

  Map<String, dynamic> toJson() {
    return {
      'cart_item_id': cartItemId,
      'dish_id': dishId,
      'dish_name': dishName,
      'dish_photo': dishPhoto,
      'quantity': quantity,
      'serving_size_id': servingSizeId,
      'price': price,
      'total_price': totalPrice,
      'notes': notes,
    };
  }
}

class CurrentAddress {
  int? id;
  String? addressText;
  Location? location;
  bool? isCurrent;

  CurrentAddress({
    this.id,
    this.addressText,
    this.location,
    this.isCurrent,
  });

  CurrentAddress.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    addressText = json['address_text'];
    location =
        json['location'] != null ? Location.fromJson(json['location']) : null;
    isCurrent = json['is_current'];
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'address_text': addressText,
      'location': location?.toJson(),
      'is_current': isCurrent,
    };
  }
}

class Location {
  Crs? crs;
  String? type;
  List<double>? coordinates;

  Location({this.crs, this.type, this.coordinates});

  Location.fromJson(Map<String, dynamic> json) {
    crs = json['crs'] != null ? Crs.fromJson(json['crs']) : null;
    type = json['type'];
    coordinates = json['coordinates'] != null
        ? List<double>.from(json['coordinates'].map((x) => x.toDouble()))
        : null;
  }

  Map<String, dynamic> toJson() {
    return {
      'crs': crs?.toJson(),
      'type': type,
      'coordinates': coordinates,
    };
  }
}

class Crs {
  String? type;
  Map<String, dynamic>? properties;

  Crs({this.type, this.properties});

  Crs.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    properties = json['properties'];
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'properties': properties,
    };
  }
}
