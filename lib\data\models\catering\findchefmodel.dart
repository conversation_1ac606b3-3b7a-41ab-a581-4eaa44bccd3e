class FindChefModel {
  bool? status;
  String? message;
  int? statusCode;
  Data? data;

  FindChefModel({this.status, this.message, this.statusCode, this.data});

  FindChefModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    statusCode = json['status_code'];
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    data['status_code'] = this.statusCode;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  int? total;
  int? page;
  int? limit;
  List<Chefs>? chefs;

  Data({this.total, this.page, this.limit, this.chefs});

  Data.fromJson(Map<String, dynamic> json) {
    total = json['total'];
    page = json['page'];
    limit = json['limit'];
    if (json['chefs'] != null) {
      chefs = <Chefs>[];
      json['chefs'].forEach((v) {
        chefs!.add(new Chefs.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['total'] = this.total;
    data['page'] = this.page;
    data['limit'] = this.limit;
    if (this.chefs != null) {
      data['chefs'] = this.chefs!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Chefs {
  int? chefId;
  String? profilePhoto;
  String? coverPhoto;
  Location? location;
  List<String>? searchTags;
  double? distance;
  Chef? chef;

  Chefs(
      {this.chefId,
      this.profilePhoto,
      this.coverPhoto,
      this.location,
      this.searchTags,
      this.distance,
      this.chef});

  Chefs.fromJson(Map<String, dynamic> json) {
    chefId = json['chef_id'];
    profilePhoto = json['profile_photo'];
    coverPhoto = json['cover_photo'];
    location = json['location'] != null
        ? new Location.fromJson(json['location'])
        : null;
    searchTags = json['search_tags'].cast<String>();
    distance = json['distance'];
    chef = json['chef'] != null ? new Chef.fromJson(json['chef']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['chef_id'] = this.chefId;
    data['profile_photo'] = this.profilePhoto;
    data['cover_photo'] = this.coverPhoto;
    if (this.location != null) {
      data['location'] = this.location!.toJson();
    }
    data['search_tags'] = this.searchTags;
    data['distance'] = this.distance;
    if (this.chef != null) {
      data['chef'] = this.chef!.toJson();
    }
    return data;
  }
}

class Location {
  Crs? crs;
  String? type;
  List<double>? coordinates;

  Location({this.crs, this.type, this.coordinates});

  Location.fromJson(Map<String, dynamic> json) {
    crs = json['crs'] != null ? new Crs.fromJson(json['crs']) : null;
    type = json['type'];
    coordinates = json['coordinates'].cast<double>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.crs != null) {
      data['crs'] = this.crs!.toJson();
    }
    data['type'] = this.type;
    data['coordinates'] = this.coordinates;
    return data;
  }
}

class Crs {
  String? type;
  Properties? properties;

  Crs({this.type, this.properties});

  Crs.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    properties = json['properties'] != null
        ? new Properties.fromJson(json['properties'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['type'] = this.type;
    if (this.properties != null) {
      data['properties'] = this.properties!.toJson();
    }
    return data;
  }
}

class Properties {
  String? name;

  Properties({this.name});

  Properties.fromJson(Map<String, dynamic> json) {
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['name'] = this.name;
    return data;
  }
}

class Chef {
  String? firstName;
  String? lastName;
  List<OperationDays>? operationDays;
  List<OperationTimes>? operationTimes;
  List<CateringDishes>? cateringDishes;

  Chef(
      {this.firstName,
      this.lastName,
      this.operationDays,
      this.operationTimes,
      this.cateringDishes});

  Chef.fromJson(Map<String, dynamic> json) {
    firstName = json['first_name'];
    lastName = json['last_name'];
    if (json['operation_days'] != null) {
      operationDays = <OperationDays>[];
      json['operation_days'].forEach((v) {
        operationDays!.add(new OperationDays.fromJson(v));
      });
    }
    if (json['operation_times'] != null) {
      operationTimes = <OperationTimes>[];
      json['operation_times'].forEach((v) {
        operationTimes!.add(new OperationTimes.fromJson(v));
      });
    }
    if (json['catering_dishes'] != null) {
      cateringDishes = <CateringDishes>[];
      json['catering_dishes'].forEach((v) {
        cateringDishes!.add(new CateringDishes.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['first_name'] = this.firstName;
    data['last_name'] = this.lastName;
    if (this.operationDays != null) {
      data['operation_days'] =
          this.operationDays!.map((v) => v.toJson()).toList();
    }
    if (this.operationTimes != null) {
      data['operation_times'] =
          this.operationTimes!.map((v) => v.toJson()).toList();
    }
    if (this.cateringDishes != null) {
      data['catering_dishes'] =
          this.cateringDishes!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class OperationDays {
  int? dayId;
  Properties? day;

  OperationDays({this.dayId, this.day});

  OperationDays.fromJson(Map<String, dynamic> json) {
    dayId = json['day_id'];
    day = json['day'] != null ? new Properties.fromJson(json['day']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['day_id'] = this.dayId;
    if (this.day != null) {
      data['day'] = this.day!.toJson();
    }
    return data;
  }
}

class OperationTimes {
  int? timingId;

  OperationTimes({this.timingId});

  OperationTimes.fromJson(Map<String, dynamic> json) {
    timingId = json['timing_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['timing_id'] = this.timingId;
    return data;
  }
}

class CateringDishes {
  int? id;
  int? orderType;
  String? title;
  int? servingSize;
  String? price;
  String? photo;
  Null? listingOrder;

  CateringDishes(
      {this.id,
      this.orderType,
      this.title,
      this.servingSize,
      this.price,
      this.photo,
      this.listingOrder});

  CateringDishes.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    orderType = json['order_type'];
    title = json['title'];
    servingSize = json['serving_size'];
    price = json['price'];
    photo = json['photo'];
    listingOrder = json['listing_order'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['order_type'] = this.orderType;
    data['title'] = this.title;
    data['serving_size'] = this.servingSize;
    data['price'] = this.price;
    data['photo'] = this.photo;
    data['listing_order'] = this.listingOrder;
    return data;
  }
}
