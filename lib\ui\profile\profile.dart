import 'dart:developer';

import 'package:db_eats/server/serverhelper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:db_eats/bloc/account_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'dart:io';


class AccountInfoPage extends StatefulWidget {
  const AccountInfoPage({Key? key}) : super(key: key);

  @override
  State<AccountInfoPage> createState() => _AccountInfoPageState();
}

class _AccountInfoPageState extends State<AccountInfoPage> {


       late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  Future<void> _updateProfilePicture(BuildContext context) async {
    final ImagePicker picker = ImagePicker();
    try {
      final XFile? image = await picker.pickImage(source: ImageSource.gallery);
      if (image != null) {
        if (context.mounted) {
          context
              .read<AccountBloc>()
              .add(UpdateProfilePictureEvent(File(image.path)));
        }
      }
    } catch (e) {
      log('Error picking image: $e');
    }
  }

  double getResponsiveSize(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width < 360) return 12;
    if (width < 600) return 14;
    if (width < 900) return 16;
    return 18;
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isLandscape = size.width > size.height;
    final baseTextSize = getResponsiveSize(context);

    final horizontalPadding = size.width * 0.05;
    final verticalSpacing = size.height * 0.02;
    final containerPadding = EdgeInsets.symmetric(
      vertical: size.height * 0.02,
      horizontal: size.width * 0.04,
    );

    final titleStyle = TextStyle(
      fontFamily: 'Inter',
      fontWeight: FontWeight.w600,
      fontSize:
          isLandscape ? size.height * 0.04 : baseTextSize * 1.29 // ≈ 18.06
      ,
    );

    final labelStyle = TextStyle(
      fontFamily: 'Inter',
      fontWeight: FontWeight.w600,
      fontSize: baseTextSize,
    );

    final valueStyle = TextStyle(
      fontFamily: 'Inter',
      fontWeight: FontWeight.w400,
      fontSize: baseTextSize * 0.9,
      color: const Color(0xFF414346),
    );

    final editStyle = TextStyle(
      fontFamily: 'Inter',
      fontWeight: FontWeight.w600,
      fontSize: baseTextSize * 0.8,
      height: 1,
      color: const Color(0xFF1F2122),
      decoration: TextDecoration.underline,
    );

    const backgroundColor = Color(0xFFF6F3EC);
    const dividerColor = Color(0xFFE1E3E6);

    context.read<AccountBloc>().add(ViewProfileEvent());

    return Scaffold(
      backgroundColor: backgroundColor,
      appBar: AppBar(
        backgroundColor: backgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, size: baseTextSize * 1.2),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: BlocListener<AccountBloc, AccountState>(
        listener: (context, state) {
          if (state is UpdateProfilePictureSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(state.message)),
            );
          }
          if (state is UpdateProfilePictureFailed) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        child: BlocBuilder<AccountBloc, AccountState>(
          builder: (context, state) {
            if (state is ViewProfileLoading &&
                !(state is UpdateProfilePictureLoading)) {
              return Center(
                child: LoadingAnimationWidget.staggeredDotsWave(
                  color: const Color(0xFF1F2122),
                  size: baseTextSize * 2.5,
                ),
              );
            }

            if (state is ViewProfileSuccess ||
                state is UpdateProfilePictureLoading) {
              final profileData = state is ViewProfileSuccess
                  ? state.profileData
                  : (state as UpdateProfilePictureLoading).previousProfileData;
              final maxContentWidth = size.width > 900 ? 600.0 : size.width;

              return Container(
                constraints: BoxConstraints(maxWidth: maxContentWidth),
                child: SingleChildScrollView(
                  padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Account Info', style: titleStyle),
                      SizedBox(height: verticalSpacing),
                      Container(
                        padding: containerPadding,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius:
                              BorderRadius.circular(size.width * 0.03),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.04),
                              blurRadius: size.width * 0.02,
                              offset: Offset(0, size.height * 0.005),
                            ),
                          ],
                        ),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                Stack(
                                  children: [
                                    CircleAvatar(
                                      radius: isLandscape
                                          ? size.height * 0.08
                                          : size.width * 0.12,
                                      backgroundColor: const Color(0xFFF6F3EC),
                                      backgroundImage:
                                          profileData.profilePicture != null &&
                                                  profileData.profilePicture!
                                                      .isNotEmpty
                                              ? NetworkImage(ServerHelper
                                                      .imageUrl +
                                                  profileData.profilePicture!)
                                              : null,
                                      child: profileData.profilePicture ==
                                                  null ||
                                              profileData
                                                  .profilePicture!.isEmpty
                                          ? Icon(
                                              Icons.person_rounded,
                                              size: getResponsiveSize(context) *
                                                  2.5,
                                              color: const Color(0xFF1F2122),
                                            )
                                          : null,
                                    ),
                                    if (state is UpdateProfilePictureLoading)
                                      Positioned.fill(
                                        child: Container(
                                          decoration: const BoxDecoration(
                                            shape: BoxShape.circle,
                                            color: Colors.black26,
                                          ),
                                          child: const Center(
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                              valueColor:
                                                  AlwaysStoppedAnimation<Color>(
                                                      Colors.white),
                                            ),
                                          ),
                                        ),
                                      )
                                    else
                                      Positioned(
                                        bottom: 0,
                                        right: 0,
                                        child: GestureDetector(
                                          onTap: () =>
                                              _updateProfilePicture(context),
                                          child: Image.asset(
                                            'assets/icons/edit.png',
                                            width: baseTextSize * 1.5,
                                            height: baseTextSize * 1.5,
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                              ],
                            ),
                            SizedBox(height: size.height * 0.04),
                            _buildInfoSection(
                              context: context,
                              label: 'Name',
                              value:
                                  '${profileData.firstName ?? ''} ${profileData.lastName ?? ''}',
                              labelStyle: labelStyle,
                              valueStyle: valueStyle,
                              editStyle: editStyle,
                              baseTextSize: baseTextSize,
                            ),
                            _buildInfoSection(
                              context: context,
                              label: 'Phone Number',
                              value: profileData.phone ?? '',
                              labelStyle: labelStyle,
                              valueStyle: valueStyle,
                              editStyle: editStyle,
                              baseTextSize: baseTextSize,
                            ),
                            _buildInfoSection(
                              context: context,
                              label: 'Email',
                              value: profileData.email ?? '',
                              labelStyle: labelStyle,
                              valueStyle: valueStyle,
                              editStyle: editStyle,
                              baseTextSize: baseTextSize,
                              isLast: true,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }

            if (state is ViewProfileFailed) {
              return Center(child: Text(state.message));
            }

            return const SizedBox();
          },
        ),
      ),
    );
  }

  Widget _buildInfoSection({
    required BuildContext context,
    required String label,
    required String value,
    required TextStyle labelStyle,
    required TextStyle valueStyle,
    required TextStyle editStyle,
    required double baseTextSize,
    bool isLast = false,
  }) {
    final size = MediaQuery.of(context).size;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildLabelRow(context, label, 'assets/icons/edit_2.png', labelStyle,
            editStyle, baseTextSize),
        SizedBox(height: size.height * 0.01),
        Text(value, style: valueStyle),
        if (!isLast) ...[
          SizedBox(height: size.height * 0.02),
          Divider(height: 1, color: Color(0xFFE1E3E6)),
          SizedBox(height: size.height * 0.02),
        ],
      ],
    );
  }

  Widget _buildLabelRow(BuildContext context, String label, String iconPath,
      TextStyle labelStyle, TextStyle editStyle, double iconSize) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(label, style: labelStyle),
        // Row(
        //   children: [
        //     Text('Edit', style: editStyle),
        //     SizedBox(width: MediaQuery.of(context).size.width * 0.01),
        //     Image.asset(iconPath, width: iconSize, height: iconSize),
        //   ],
        // ),
      ],
    );
  }
}
