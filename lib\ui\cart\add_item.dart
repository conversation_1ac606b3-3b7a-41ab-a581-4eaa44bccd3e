import 'package:db_eats/bloc/account_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/data/models/chef/viewchefdetailsmodel.dart';
import 'package:db_eats/data/models/chef/viewdishesmodel.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/ui/cart/cart-subitem.dart';
import 'package:db_eats/ui/cart/yourcart.dart';
import 'package:db_eats/ui/catering/dishdetail.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:db_eats/bloc/home_bloc.dart';
import 'package:shimmer/shimmer.dart';

class AddItem extends StatefulWidget {
  final int id;
  final String title;
  final String? distance;
  final String? fromPage;

  const AddItem({
    super.key,
    required this.id,
    required this.title,
    this.distance,
    this.fromPage,
  });

  @override
  State<AddItem> createState() => _AddItemState();
}

class _AddItemState extends State<AddItem> with SingleTickerProviderStateMixin {
  ChefDetailsModel? chefDetails;
  DishesListModel? dishesData;
  late TabController _tabController;
  List<String> _categories = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _categories.length, vsync: this);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<HomeBloc>().add(ViewChefDetailsEvent(chefId: widget.id));
      context.read<HomeBloc>().add(GetDishesListEvent(
            data: {
              "chef_id": widget.id.toString(),
            },
          ));
    });
  }

  int _selectedIndex = 0;

  final List<Widget> _pages = [
    const Center(child: Text('Home Page')),
    const Center(child: Text('Orders Page')),
    const Center(child: Text('Catering Page')),
    const Center(child: Text('Messages Page')),
    const Center(child: Text('Account Page')),
  ];

  void _onNavItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  void _submit() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const DishDetailPage(dishId: "1"),
      ),
    );
  }

  void _navigateBack() {
    if (widget.fromPage == 'Cart') {
      Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => CartPage2(
              chef_id: widget.id,
            ),
          ));
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    return MultiBlocListener(
      listeners: [
        BlocListener<HomeBloc, HomeState>(
          listener: (context, state) {
            if (state is ChefDetailsSuccess) {
              setState(() {
                chefDetails = state.data;
              });
            }
            if (state is DishesListSuccess) {
              setState(() {
                dishesData = state.data;
                _categories = dishesData?.data?.categoryBasedList
                        ?.map((cat) => cat.category?.name ?? '')
                        .toList() ??
                    [];
                _tabController =
                    TabController(length: _categories.length, vsync: this);
              });
            }
          },
        ),
        BlocListener<AccountBloc, AccountState>(
          listener: (context, state) {
            if (state is AddFavouriteChefSuccess) {
              ScaffoldMessenger.of(context)
                ..hideCurrentSnackBar()
                ..showSnackBar(SnackBar(content: Text(state.message)));
            } else if (state is AddFavouriteChefFailed) {
              ScaffoldMessenger.of(context)
                ..hideCurrentSnackBar()
                ..showSnackBar(
                  SnackBar(
                      content: Text(state.message),
                      backgroundColor: Colors.red),
                );
            }
          },
        ),
      ],
      child: WillPopScope(
        onWillPop: () async {
          _navigateBack();
          return false;
        },
        child: Scaffold(
          backgroundColor: const Color(0xFFF6F3EC),
          appBar: AppBar(
            backgroundColor: const Color(0xFFF6F3EC),
            centerTitle: false,
            scrolledUnderElevation: 0,
            titleSpacing: screenWidth * 0.01,
            automaticallyImplyLeading: false,
            leading: IconButton(
              icon: Image.asset(
                'assets/icons/backbutton.png',
                width: screenWidth * 0.08,
                height: screenWidth * 0.08,
              ),
              onPressed: _navigateBack,
              tooltip: 'Back',
            ),
            // title: Text(
            //   chefDetails?.data?.chef?.chef?.firstName ?? widget.title,
            //   style: TextStyle(
            //     fontFamily: 'Inter',
            //     fontWeight: FontWeight.w600,
            //     fontSize: screenWidth * 0.06,
            //     color: const Color(0xFF1F2122),
            //   ),
            // ),
            elevation: 0,
          ),
          body: SafeArea(
            child: BlocBuilder<HomeBloc, HomeState>(
              builder: (context, state) {
                if (state is DishesListLoading || state is LoadingChefDetails) {
                  return _buildShimmerLoading(
                      context, screenWidth, screenHeight);
                }

                if (state is DishesListSuccess &&
                    (dishesData?.data == null ||
                        (dishesData?.data?.featuredList?.isEmpty ?? true) &&
                            (dishesData?.data?.categoryBasedList?.isEmpty ??
                                true))) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image.asset(
                          'assets/images/no_data.png',
                          width: screenWidth * 0.5,
                          height: screenWidth * 0.5,
                        ),
                        SizedBox(height: screenHeight * 0.02),
                        Text(
                          'No dishes available',
                          style: TextStyle(
                            fontSize: screenWidth * 0.045,
                            fontWeight: FontWeight.w500,
                            color: const Color(0xFF1F2122),
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: screenWidth * 0.035,
                          vertical: screenHeight * 0.01,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(
                              height: screenHeight * 0.18,
                              child: Stack(
                                clipBehavior: Clip.none,
                                children: [
                                  Container(
                                    width: double.infinity,
                                    height: screenHeight * 0.14,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(
                                          screenWidth * 0.04),
                                      image: DecorationImage(
                                        image: NetworkImage(
                                          ServerHelper.imageUrl +
                                              (chefDetails?.data?.chef
                                                      ?.coverPhoto ??
                                                  ''),
                                        ),
                                        fit: BoxFit.cover,
                                        onError: (exception, stackTrace) =>
                                            const AssetImage(
                                                'assets/images/placeholder.png'),
                                      ),
                                    ),
                                  ),
                                  Align(
                                    alignment: Alignment.bottomRight,
                                    child: FractionalTranslation(
                                      translation: const Offset(-0.21, -0.03),
                                      child: Container(
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          border: Border.all(
                                            color: Colors.white,
                                            width: screenWidth * 0.015,
                                          ),
                                        ),
                                        child: CircleAvatar(
                                          radius: screenWidth * 0.1,
                                          backgroundImage: NetworkImage(
                                            ServerHelper.imageUrl +
                                                (chefDetails?.data?.chef
                                                        ?.profilePhoto ??
                                                    ''),
                                          ),
                                          backgroundColor: Colors.white,
                                          onBackgroundImageError:
                                              (exception, stackTrace) => null,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            // SizedBox(height: screenHeight * 0.02),
                            Row(
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        '${chefDetails?.data?.chef?.chef?.firstName ?? ''} ${chefDetails?.data?.chef?.chef?.lastName ?? ''}',
                                        style: TextStyle(
                                          fontFamily: 'Inter',
                                          fontSize: screenWidth * 0.07,
                                          fontWeight: FontWeight.w600,
                                          height: 1.24,
                                          letterSpacing: -1,
                                          color: const Color(0xFF1F2122),
                                        ),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: screenHeight * 0.017),
                            Row(
                              children: [
                                _buildMetricContainer(
                                  context,
                                  screenWidth,
                                  icon: Icons.access_time,
                                  text: "35 mins",
                                ),
                                SizedBox(width: screenWidth * 0.02),
                                _buildMetricContainer(
                                  context,
                                  screenWidth,
                                  icon: Icons.star,
                                  text: "4.9",
                                ),
                                // SizedBox(width: screenWidth * 0.02),
                                // _buildMetricContainer(
                                //   context,
                                //   screenWidth,
                                //   icon: Icons.location_on_outlined,
                                //   text: widget.distance ?? '_',
                                // ),
                                SizedBox(width: screenWidth * 0.02),
                                _buildFavoriteButton(context, screenWidth),
                              ],
                            ),
                            SizedBox(height: screenHeight * 0.017),
                            Wrap(
                              spacing: screenWidth * 0.03,
                              runSpacing: screenHeight * 0.005,
                              children:
                                  (chefDetails?.data?.chef?.searchTags ?? [])
                                      .map((tag) {
                                return Text(
                                  tag,
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontSize: screenWidth * 0.035,
                                    fontWeight: FontWeight.w400,
                                    color: const Color(0xFF414346),
                                  ),
                                );
                              }).toList(),
                            ),
                            SizedBox(height: screenHeight * 0.012),
                            Text(
                              chefDetails?.data?.chef?.description ?? '',
                              style: TextStyle(
                                fontFamily: 'Inter',
                                fontSize: screenWidth * 0.035,
                                fontWeight: FontWeight.w400,
                                color: const Color(0xFF414346),
                              ),
                              textAlign: TextAlign.justify,
                            ),
                            SizedBox(height: screenHeight * 0.017),
                            if (chefDetails?.data?.chef?.chef?.operationDays !=
                                    null ||
                                chefDetails?.data?.chef?.chef?.operationTimes !=
                                    null)
                              Row(
                                children: [
                                  Image.asset(
                                    'assets/icons/calender_3.png',
                                    width: screenWidth * 0.05,
                                    height: screenWidth * 0.045,
                                    color: const Color(0xFF414346),
                                  ),
                                  SizedBox(width: screenWidth * 0.04),
                                  Text(
                                    _formatOperationTimes(chefDetails
                                        ?.data?.chef?.chef?.operationTimes),
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontWeight: FontWeight.w600,
                                      fontSize: screenWidth * 0.035,
                                    ),
                                  ),
                                  SizedBox(width: screenWidth * 0.04),
                                  Text(
                                    _formatOperationDays(chefDetails
                                        ?.data?.chef?.chef?.operationDays),
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontWeight: FontWeight.w400,
                                      fontSize: screenWidth * 0.035,
                                      letterSpacing: 1,
                                    ),
                                  ),
                                ],
                              ),
                            SizedBox(height: screenHeight * 0.017),
                          ],
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: screenWidth * 0.035),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(
                              height: screenHeight * 0.04,
                              child: ListView(
                                scrollDirection: Axis.horizontal,
                                children:
                                    _categories.asMap().entries.map((entry) {
                                  final index = entry.key;
                                  final category = entry.value;
                                  final isSelected =
                                      _tabController.index == index;
                                  return GestureDetector(
                                    onTap: () {
                                      setState(() {
                                        _tabController.animateTo(index);
                                      });
                                    },
                                    child: Container(
                                      margin: EdgeInsets.only(
                                          right: screenWidth * 0.02),
                                      padding: EdgeInsets.symmetric(
                                        horizontal: screenWidth * 0.03,
                                        vertical: screenHeight * 0.005,
                                      ),
                                      decoration: BoxDecoration(
                                        color: isSelected
                                            ? const Color(0xFFB9B6AD)
                                            : const Color(0xFFE1DDD5),
                                        borderRadius: BorderRadius.circular(
                                            screenWidth * 0.05),
                                      ),
                                      child: Center(
                                        child: Text(
                                          category,
                                          style: TextStyle(
                                            fontFamily: 'Inter',
                                            fontSize: screenWidth * 0.035,
                                            fontWeight: FontWeight.w600,
                                            color: const Color(0xFF1F2122),
                                          ),
                                        ),
                                      ),
                                    ),
                                  );
                                }).toList(),
                              ),
                            ),
                            SizedBox(height: screenHeight * 0.01),
                            Container(
                              height: screenHeight * 0.05,
                              decoration: BoxDecoration(
                                borderRadius:
                                    BorderRadius.circular(screenWidth * 0.06),
                                border:
                                    Border.all(color: const Color(0xFF1F2122)),
                              ),
                              child: InkWell(
                                borderRadius:
                                    BorderRadius.circular(screenWidth * 0.06),
                                onTap: () {},
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(Icons.tune,
                                        size: screenWidth * 0.04,
                                        color: const Color(0xFF1F2122)),
                                    SizedBox(width: screenWidth * 0.02),
                                    Text(
                                      "View Filters",
                                      style: TextStyle(
                                        fontSize: screenWidth * 0.03,
                                        fontWeight: FontWeight.w600,
                                        color: const Color(0xFF1F2122),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            SizedBox(height: screenHeight * 0.02),
                            Text(
                              "Featured Items",
                              style: TextStyle(
                                fontFamily: 'Inter',
                                fontSize: screenWidth * 0.06,
                                fontWeight: FontWeight.w600,
                                color: const Color(0xFF1F2122),
                              ),
                            ),
                            SizedBox(height: screenHeight * 0.015),
                            _buildFeaturedItems(
                                context, screenWidth, screenHeight),
                            SizedBox(height: screenHeight * 0.02),
                            _buildCategoryContent(
                                context, screenWidth, screenHeight),
                            SizedBox(height: screenHeight * 0.03),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMetricContainer(BuildContext context, double screenWidth,
      {required IconData icon, required String text}) {
    return Container(
      padding: EdgeInsets.symmetric(
          horizontal: screenWidth * 0.015, vertical: screenWidth * 0.01),
      constraints: BoxConstraints(
          minWidth: screenWidth * 0.2, minHeight: screenWidth * 0.1),
      decoration: BoxDecoration(
        color: const Color(0xFFF6F3EC),
        borderRadius: BorderRadius.circular(screenWidth * 0.025),
        border: Border.all(color: const Color(0xFFB9B6AD)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: screenWidth * 0.04, color: const Color(0xFF414346)),
          SizedBox(width: screenWidth * 0.01),
          Text(
            text,
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: screenWidth * 0.04,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF1F2122),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFavoriteButton(BuildContext context, double screenWidth) {
    return Container(
      width: screenWidth * 0.1,
      height: screenWidth * 0.1,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(screenWidth * 0.025),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: IconButton(
        icon: BlocBuilder<AccountBloc, AccountState>(
          builder: (context, state) {
            if (state is AddFavouriteChefLoading) {
              return SizedBox(
                width: screenWidth * 0.06,
                height: screenWidth * 0.06,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor:
                      const AlwaysStoppedAnimation<Color>(Color(0xFF1F2122)),
                ),
              );
            }
            return Image.asset(
              'assets/icons/favorites.png',
              width: screenWidth * 0.06,
              height: screenWidth * 0.06,
              color: const Color(0xFF1F2122),
            );
          },
        ),
        onPressed: () {
          context
              .read<AccountBloc>()
              .add(AddFavouriteChefEvent(chefDetails?.data?.chef?.chefId ?? 0));
        },
        padding: EdgeInsets.zero,
        tooltip: 'Add to favorites',
      ),
    );
  }

  String _formatOperationTimes(List<dynamic>? operationTimes) {
    if (operationTimes == null || operationTimes.isEmpty) return 'Open: N/A';
    var time = operationTimes.first;
    String startTime = time.timing?.startTime ?? 'N/A';
    String endTime = time.timing?.endTime ?? 'N/A';
    String formattedStart = _formatTimeString(startTime);
    String formattedEnd = _formatTimeString(endTime);
    return 'Open $formattedStart-$formattedEnd';
  }

  String _formatTimeString(String timeStr) {
    if (timeStr == 'N/A') return timeStr;
    try {
      List<String> parts = timeStr.split(':');
      if (parts.length < 2) return timeStr;
      int hour = int.parse(parts[0]);
      bool isPM = hour >= 12;
      if (hour > 12) hour -= 12;
      if (hour == 0) hour = 12;
      return '$hour${isPM ? 'PM' : 'AM'}';
    } catch (e) {
      return timeStr;
    }
  }

  String _formatOperationDays(List<dynamic>? operationDays) {
    if (operationDays == null || operationDays.isEmpty) return '';
    Map<String, String> dayAbbreviations = {
      'Monday': 'M',
      'Tuesday': 'T',
      'Wednesday': 'W',
      'Thursday': 'Th',
      'Friday': 'F',
      'Saturday': 'Sat',
      'Sunday': 'Sun',
    };
    List formattedDays = operationDays
        .map((day) =>
            dayAbbreviations[day.day?.name ?? ''] ?? day.day?.name ?? '')
        .where((day) => day.isNotEmpty)
        .toList();
    return formattedDays.join(', ');
  }

  Widget _buildFeaturedItems(
      BuildContext context, double screenWidth, double screenHeight) {
    if (dishesData?.data?.featuredList == null ||
        dishesData!.data!.featuredList!.isEmpty) {
      return const SizedBox();
    }
    return SizedBox(
      height: screenHeight * 0.42,
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.only(right: screenWidth * 0.04),
        children: dishesData!.data!.featuredList!
            .map((dish) => Container(
                  width: screenWidth * 0.8,
                  margin: EdgeInsets.only(right: screenWidth * 0.04),
                  child: _buildDishCard(context, screenWidth, screenHeight, {
                    'id': dish.id ?? 0,
                    'name': dish.name ?? '',
                    'photo': dish.photo ?? '',
                    'price': dish.servingSizePrices?.first?.price ?? '0.00',
                    'serving_size':
                        dish.servingSizePrices?.first?.servingSize?.title ?? '',
                    'serving_size_id':
                        dish.servingSizePrices?.first?.servingSizeId ?? 0,
                  }),
                ))
            .toList(),
      ),
    );
  }

  Widget _buildCategorySection(BuildContext context, double screenWidth,
      double screenHeight, String category) {
    final categoryList = dishesData?.data?.categoryBasedList
        ?.firstWhere((cat) => cat.category?.name == category,
            orElse: () => CategoryBasedList())
        .dishList;
    if (categoryList == null || categoryList.isEmpty) return const SizedBox();
    return SizedBox(
      height: screenHeight * 0.42,
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.only(right: screenWidth * 0.04),
        children: categoryList
            .map((dish) => Container(
                  width: screenWidth * 0.8,
                  margin: EdgeInsets.only(right: screenWidth * 0.04),
                  child: _buildDishCard(context, screenWidth, screenHeight, {
                    'id': dish.id ?? 0,
                    'name': dish.name ?? '',
                    'photo': dish.photo ?? '',
                    'price': dish.servingSizePrices?.first?.price ?? '0.00',
                    'serving_size':
                        dish.servingSizePrices?.first?.servingSize?.title ?? '',
                    'serving_size_id':
                        dish.servingSizePrices?.first?.servingSizeId ?? 0,
                  }),
                ))
            .toList(),
      ),
    );
  }

  Widget _buildCategoryContent(
      BuildContext context, double screenWidth, double screenHeight) {
    if (dishesData?.data?.categoryBasedList == null ||
        dishesData!.data!.categoryBasedList!.isEmpty) {
      return const SizedBox();
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: dishesData!.data!.categoryBasedList!.map((category) {
        final categoryName = category.category?.name ?? '';
        final dishes = category.dishList ?? [];
        if (dishes.isEmpty) return const SizedBox();
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(vertical: screenHeight * 0.01),
              child: Text(
                categoryName,
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontSize: screenWidth * 0.06,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF1F2122),
                ),
              ),
            ),
            SizedBox(height: screenHeight * 0.01),
            _buildCategorySection(
                context, screenWidth, screenHeight, categoryName),
            SizedBox(height: screenHeight * 0.02),
          ],
        );
      }).toList(),
    );
  }

  Widget _buildShimmerLoading(
      BuildContext context, double screenWidth, double screenHeight) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.all(screenWidth * 0.04),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: double.infinity,
                    height: screenHeight * 0.14,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(screenWidth * 0.04),
                    ),
                  ),
                  SizedBox(height: screenHeight * 0.02),
                  Row(
                    children: [
                      Container(
                        width: screenWidth * 0.2,
                        height: screenWidth * 0.2,
                        decoration: const BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                        ),
                      ),
                      SizedBox(width: screenWidth * 0.04),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              height: screenWidth * 0.06,
                              width: screenWidth * 0.5,
                              color: Colors.white,
                            ),
                            SizedBox(height: screenHeight * 0.01),
                            Container(
                              height: screenWidth * 0.04,
                              width: screenWidth * 0.3,
                              color: Colors.white,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: screenHeight * 0.02),
                  Row(
                    children: List.generate(
                      4,
                      (index) => Container(
                        margin: EdgeInsets.only(right: screenWidth * 0.02),
                        width: screenWidth * 0.2,
                        height: screenWidth * 0.1,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius:
                              BorderRadius.circular(screenWidth * 0.025),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: screenHeight * 0.02),
                  Container(
                    height: screenHeight * 0.1,
                    color: Colors.white,
                  ),
                  SizedBox(height: screenHeight * 0.02),
                  Container(
                    height: screenWidth * 0.06,
                    width: screenWidth * 0.5,
                    color: Colors.white,
                  ),
                  SizedBox(height: screenHeight * 0.02),
                  SizedBox(
                    height: screenHeight * 0.04,
                    child: ListView(
                      scrollDirection: Axis.horizontal,
                      children: List.generate(
                        4,
                        (index) => Container(
                          margin: EdgeInsets.only(right: screenWidth * 0.02),
                          width: screenWidth * 0.25,
                          height: screenWidth * 0.08,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius:
                                BorderRadius.circular(screenWidth * 0.05),
                          ),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: screenHeight * 0.02),
                  Container(
                    width: screenWidth * 0.4,
                    height: screenWidth * 0.06,
                    color: Colors.white,
                  ),
                  SizedBox(height: screenHeight * 0.02),
                  SizedBox(
                    height: screenHeight * 0.42,
                    child: ListView(
                      scrollDirection: Axis.horizontal,
                      padding: EdgeInsets.only(right: screenWidth * 0.04),
                      children: List.generate(
                        3,
                        (index) => _buildShimmerDishCard(
                            context, screenWidth, screenHeight),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShimmerDishCard(
      BuildContext context, double screenWidth, double screenHeight) {
    return Container(
      width: screenWidth * 0.8,
      height: screenHeight * 0.42,
      margin: EdgeInsets.only(right: screenWidth * 0.04),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(screenWidth * 0.04),
      ),
      child: Column(
        children: [
          Container(
            height: screenHeight * 0.25,
            color: Colors.white,
          ),
          Padding(
            padding: EdgeInsets.all(screenWidth * 0.04),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: screenWidth * 0.4,
                  height: screenWidth * 0.05,
                  color: Colors.white,
                ),
                SizedBox(height: screenHeight * 0.01),
                Container(
                  width: screenWidth * 0.3,
                  height: screenWidth * 0.04,
                  color: Colors.white,
                ),
                SizedBox(height: screenHeight * 0.02),
                Container(
                  width: screenWidth * 0.2,
                  height: screenWidth * 0.06,
                  color: Colors.white,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDishCard(BuildContext context, double screenWidth,
      double screenHeight, Map<String, dynamic> dish) {
    final priceDouble = double.tryParse(dish['price'] ?? '0.00') ?? 0.0;
    const rating = "90";

    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => DishDetailPage(dishId: dish['id'].toString()),
          ),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(screenWidth * 0.04),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.vertical(
                  top: Radius.circular(screenWidth * 0.04)),
              child: Image.network(
                ServerHelper.imageUrl + (dish['photo'] ?? ''),
                height: screenHeight * 0.25,
                width: double.infinity,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    height: screenHeight * 0.25,
                    width: double.infinity,
                    color: Colors.grey[200],
                    child: const Center(child: Text('Image not available')),
                  );
                },
              ),
            ),
            Padding(
              padding: EdgeInsets.all(screenWidth * 0.04),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    dish['name'] ?? 'Unknown Dish',
                    style: TextStyle(
                      fontSize: screenWidth * 0.045,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF1F2122),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: screenHeight * 0.015),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Wrap(
                              spacing: screenWidth * 0.02,
                              runSpacing: screenHeight * 0.01,
                              children: [
                                Container(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: screenWidth * 0.015,
                                    vertical: screenWidth * 0.005,
                                  ),
                                  decoration: BoxDecoration(
                                    color: const Color(0xFFE1E3E6),
                                    borderRadius: BorderRadius.circular(
                                        screenWidth * 0.03),
                                  ),
                                  child: Text(
                                    "${dish['serving_size'].split(' ').first} Servings",
                                    style: TextStyle(
                                      fontSize: screenWidth * 0.03,
                                      fontWeight: FontWeight.w500,
                                      fontFamily: 'Inter',
                                    ),
                                  ),
                                ),
                                Container(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: screenWidth * 0.015,
                                    vertical: screenWidth * 0.005,
                                  ),
                                  decoration: BoxDecoration(
                                    color: const Color(0xFFE1E3E6),
                                    borderRadius: BorderRadius.circular(
                                        screenWidth * 0.03),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Image.asset(
                                        'assets/icons/thump.png',
                                        width: screenWidth * 0.03,
                                        height: screenWidth * 0.0275,
                                        color: Colors.black54,
                                      ),
                                      SizedBox(width: screenWidth * 0.01),
                                      Text(
                                        "$rating%",
                                        style: TextStyle(
                                          fontSize: screenWidth * 0.03,
                                          fontWeight: FontWeight.w500,
                                          fontFamily: 'Inter',
                                        ),
                                      ),
                                      SizedBox(width: screenWidth * 0.005),
                                      Text(
                                        "($rating)",
                                        style: TextStyle(
                                          fontSize: screenWidth * 0.03,
                                          fontWeight: FontWeight.w500,
                                          fontFamily: 'Inter',
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: screenHeight * 0.02),
                            Text.rich(
                              TextSpan(
                                children: [
                                  TextSpan(
                                    text: '\$',
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: screenWidth * 0.04,
                                      fontWeight: FontWeight.w500,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  TextSpan(
                                    text: priceDouble.toStringAsFixed(2),
                                    style: TextStyle(
                                      fontFamily: 'Roboto',
                                      fontSize: screenWidth * 0.04,
                                      fontWeight: FontWeight.w500,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      BlocListener<AccountBloc, AccountState>(
                        listener: (context, state) {
                          if (state is AddToCartSuccess) {
                            ScaffoldMessenger.of(context)
                              ..hideCurrentSnackBar()
                              ..showSnackBar(
                                  SnackBar(content: Text(state.message)));
                          } else if (state is AddToCartFailed) {
                            ScaffoldMessenger.of(context)
                              ..hideCurrentSnackBar()
                              ..showSnackBar(
                                SnackBar(
                                  content: Text(state.message),
                                  backgroundColor: Colors.red,
                                ),
                              );
                          }
                        },
                        child: BlocBuilder<AccountBloc, AccountState>(
                          builder: (context, state) {
                            final isLoading = state is AddToCartLoading;
                            return GestureDetector(
                              onTap: isLoading
                                  ? null
                                  : () {
                                      if (dish['id'] == 0 ||
                                          dish['serving_size_id'] == 0) {
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          const SnackBar(
                                            content: Text(
                                                'Invalid dish or serving size'),
                                            backgroundColor: Colors.red,
                                          ),
                                        );
                                        return;
                                      }
                                      context.read<AccountBloc>().add(
                                            AddToCartEvent({
                                              'chef_id': chefDetails
                                                      ?.data?.chef?.chefId ??
                                                  0,
                                              'chef_dish_id': dish['id'],
                                              'quantity': 1,
                                              'serving_size_id':
                                                  dish['serving_size_id'],
                                            }),
                                          );
                                    },
                              child: isLoading
                                  ? SizedBox(
                                      width: screenWidth * 0.08,
                                      height: screenWidth * 0.08,
                                      child: const CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                                Colors.black),
                                      ),
                                    )
                                  : Image.asset(
                                      'assets/icons/add.png',
                                      width: screenWidth * 0.08,
                                      height: screenWidth * 0.08,
                                      semanticLabel: 'Add to cart',
                                    ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
