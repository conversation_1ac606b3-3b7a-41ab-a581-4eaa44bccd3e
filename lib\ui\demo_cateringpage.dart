// ignore_for_file: deprecated_member_use, unnecessary_const
import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:db_eats/bloc/account_bloc.dart';
import 'package:db_eats/bloc/home_bloc.dart';
import 'package:db_eats/bloc/mealplan_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/data/models/guesthome/homemodel.dart';
import 'package:db_eats/data/models/guesthome/listaddressmodel.dart';
import 'package:db_eats/data/models/meal_plan/timinglistmodel.dart'
    as meal_plan;
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/ui/cart/yourcart.dart';
import 'package:db_eats/ui/catering/cateringrequest.dart';
import 'package:db_eats/ui/catering/sendcateringrequest.dart';
import 'package:db_eats/ui/chef/popular_chefs_near.dart';
import 'package:db_eats/ui/chef/recommended_chefs.dart';
import 'package:db_eats/ui/chef/top_rated_chefs.dart';
import 'package:db_eats/ui/chef/view_chef2.dart';
import 'package:db_eats/ui/deals/dealslist.dart';
import 'package:db_eats/ui/mainnavigationpage.dart';
import 'package:db_eats/ui/meal_plan/choose_plan.dart';
import 'package:db_eats/ui/search/global_search.dart';
import 'package:db_eats/widgets/cheflocationmodal.dart';
import 'package:db_eats/widgets/floatingactionbutton.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:http/http.dart' as http;
import 'package:shimmer/shimmer.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'Food Delivery UI',
      theme: ThemeData(fontFamily: 'Roboto'),
      home: const DemoCateringPage(),
    );
  }
}

int _selectedIndex = 0;

// List of page widgets to show based on selected tab
final List<Widget> _pages = [
  const Center(child: Text('Home Page')),
  const Center(child: Text('Orders Page')),
  const Center(child: Text('Catering Page')),
  const Center(child: Text('Messages Page')),
  const Center(child: Text('Account Page')),
];

// Convert DemoCateringPage from StatelessWidget to StatefulWidget
class DemoCateringPage extends StatefulWidget {
  const DemoCateringPage({super.key});

  @override
  State<DemoCateringPage> createState() => _DemoCateringPageState();
}

class _DemoCateringPageState extends State<DemoCateringPage> {
  // Add missing state variables for address and search logic (matching Home2)
  bool _isAddressUpdating = false;
  bool _isSearchMode = false;
  String? _currentSearchQuery;
  bool _isSearchPopupVisible = false;
  OverlayEntry? _overlayEntry;
  String _selectedDeliveryTime = 'ASAP';
  String _currentlySelectedTimeRange = '7:00 AM - 7:30 AM';
  final String _selectedTimeOption = '7:00 AM - 7:30 AM';
  final bool _showScheduleOptions = false;
  bool _showLocationMenu = false;
  bool _showTimeOptions = false;
  bool _loadingAddresses = false;
  bool _loadingChefs = false;
  bool _addressLoaded = false;
  late final MealplanBloc _mealplanBloc;
  List<AddressData>? _savedAddresses;
  AddressData? _currentAddressData;
  double? _currentLatitude;
  double? _currentLongitude;
  double? _pendingLatitude;
  double? _pendingLongitude;
  List<meal_plan.Timings>? _availableTimings;
  String? _currentAddress;
  int? _selectedTimeId;
  final TextEditingController _addressController = TextEditingController();
  List<Prediction> _searchResults = [];
  bool _showPredictions = false;
  bool _isSearching = false;
  Timer? _debounce;
  final String kGoogleApiKey = "AIzaSyCpAdQaZ3fPe5H0wfkI0NqMXcT8J7AW9uY";

  // Screen size variables
  double ten = 0;
  double twelve = 0;
  double forteen = 0;
  double sixteen = 0;
  double eighteen = 0;
  double twenty = 0;
  double twentyFour = 0;
  double screenWidth = 0;
  double screenHeight = 0;

  @override
  void initState() {
    super.initState();
    _mealplanBloc = MealplanBloc();
    // Load addresses and timings only once
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadAddresses();
      _mealplanBloc.add(ListTimingEvent());
      // Check if address is already loaded from storage
      _checkInitialAddress();
    });
  }

  Future<void> _checkInitialAddress() async {
    final address = await Initializer.getAddress();
    if (address != null && address.isNotEmpty) {
      setState(() {
        _addressLoaded = true;
      });
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  void _onNavItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  Future<void> _loadAddresses() async {
    // Prevent multiple simultaneous address loading requests
    if (_loadingAddresses) return;

    setState(() => _loadingAddresses = true);
    try {
      context.read<AccountBloc>().add(ListAddressesEvent());
    } catch (e) {
      log('Error loading addresses: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load addresses: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _loadingAddresses = false);
      }
    }
  }

  Future<void> _showLocationModalIfNeeded() async {
    if (_savedAddresses?.isEmpty ?? true) {
      _currentAddress = await Initializer.getAddress();
      if ((_currentAddress?.isEmpty ?? true) && mounted) {
        showDialog(
          context: context,
          builder: (_) => const ChefLocationModal(),
        );
      }
    }
  }

  @override
  void dispose() {
    _closeSearchPopup();
    super.dispose();
  }

  String _formatDistance(double? distance) {
    if (distance == null) return 'Unknown';

    final kilometers = distance / 1000;

    return '${kilometers.toStringAsFixed(1)} km';
  }

  void _submit() {
    // Navigator.push(
    //     context,
    //     MaterialPageRoute(
    //       builder: (context) => CateringRequestsPage(),
    //     ));

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => WithNavBar(
          currentIndex: 2,
          child: CateringRequestsPage(),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    return MultiBlocProvider(
      providers: [
        BlocProvider<HomeBloc>.value(
          value: BlocProvider.of<HomeBloc>(context),
        ),
        BlocProvider<MealplanBloc>.value(
          value: _mealplanBloc,
        ),
      ],
      child: BlocListener<AccountBloc, AccountState>(
        listener: (context, state) {
          if (state is ListAddressesSuccess) {
            _savedAddresses = state.data;
            _currentAddressData = _savedAddresses?.firstWhere(
              (address) => address.isCurrent == true,
              orElse: () => _savedAddresses?.isNotEmpty == true
                  ? _savedAddresses!.first
                  : AddressData(),
            );

            WidgetsBinding.instance.addPostFrameCallback((_) {
              _showLocationModalIfNeeded();
            });

            if (_currentAddressData?.location?.coordinates != null) {
              final lat = _currentAddressData!.location!.coordinates![1];
              final lng = _currentAddressData!.location!.coordinates![0];

              _currentLatitude = lat;
              _currentLongitude = lng;
              _addressLoaded = true;

              Initializer().setCoordinates(lat, lng);

              setState(() {
                _loadingChefs = true;
              });

              context.read<HomeBloc>().add(
                    GetHomeDataEvent(
                      data: {
                        'latitude': lat,
                        'longitude': lng,
                      },
                    ),
                  );
            }
          } else if (state is EditAddressSuccess) {
            if (_pendingLatitude != null && _pendingLongitude != null) {
              _currentLatitude = _pendingLatitude;
              _currentLongitude = _pendingLongitude;
              _showLocationMenu = false;
              _pendingLatitude = null;
              _pendingLongitude = null;
              _addressLoaded = true;

              Initializer()
                  .setCoordinates(_currentLatitude!, _currentLongitude!);

              setState(() {
                _loadingChefs = true;
              });

              context.read<HomeBloc>().add(
                    GetHomeDataEvent(
                      data: {
                        'latitude': _currentLatitude!,
                        'longitude': _currentLongitude!,
                      },
                    ),
                  );
            }
            if (mounted) {
              context.read<AccountBloc>().add(ListAddressesEvent());
            }
          } else if (state is AddAddressSuccess) {
            if (!_loadingAddresses && mounted) {
              context.read<AccountBloc>().add(ListAddressesEvent());
            }
          }
        },
        child: BlocListener<MealplanBloc, MealPlanState>(
          listener: (context, state) {
            if (state is ListTimingSuccess) {
              _availableTimings = state.data.data?.timings;
            }
          },
          child: Scaffold(
            backgroundColor: const Color.fromRGBO(246, 243, 236, 1),
            body: SafeArea(
              child: Stack(
                children: [
                  Column(
                    children: [
                      _buildLocationHeader(context, screenWidth, screenHeight),
                      SizedBox(height: screenHeight * 0.01),
                      Expanded(
                        child: BlocConsumer<HomeBloc, HomeState>(
                          listener: (context, state) {
                            if (state is HomeDataSuccess) {
                              setState(() {
                                _loadingChefs = false;
                              });
                            } else if (state is HomeDataLoading) {
                              setState(() {
                                _loadingChefs = true;
                              });
                            }
                          },
                          builder: (context, state) {
                            List<ChefData>? chefs;
                            if (state is HomeDataSuccess) {
                              chefs = state.data.data?.popularChefsNear;
                            } else {
                              chefs = null;
                            }

                            if (_loadingChefs || state is HomeDataLoading) {
                              return SingleChildScrollView(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const SizedBox(height: 16),
                                    _buildSectionHeader(context, screenWidth,
                                        'Most Popular Near You'),
                                    SizedBox(height: screenHeight * 0.02),
                                    _buildChefShimmerLoading(
                                        context, screenWidth, screenHeight),
                                    Padding(
                                      padding: EdgeInsets.only(
                                          left: twenty,
                                          right: twenty,
                                          top: twenty,
                                          bottom: ten),
                                      child: SizedBox(
                                        height: ten * 5.5,
                                        width: double.infinity,
                                        child: ElevatedButton(
                                          onPressed: _submit,
                                          style: ElevatedButton.styleFrom(
                                              backgroundColor:
                                                  const Color(0xFF1F2122),
                                              shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          ten * 3.2))),
                                          child: Text('View Catering Requests',
                                              style: TextStyle(
                                                  fontFamily: 'Inter',
                                                  fontSize: sixteen,
                                                  fontWeight: FontWeight.w600,
                                                  letterSpacing: 0.32,
                                                  color: Colors.white)),
                                        ),
                                      ),
                                    ),
                                    _buildMonthlySaverCard(),
                                    SizedBox(height: screenHeight * 0.08),
                                  ],
                                ),
                              );
                            }

                            if ((chefs == null || chefs.isEmpty)) {
                              return _buildNoDataMessage(
                                  context,
                                  screenWidth,
                                  screenHeight,
                                  'No popular chefs in your area');
                            }

                            return SingleChildScrollView(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const SizedBox(height: 16),
                                  _buildSectionHeader(context, screenWidth,
                                      'Most Popular Near You'),
                                  SizedBox(height: screenHeight * 0.02),
                                  _buildPopularChefs(context, screenWidth,
                                      screenHeight, chefs!),
                                  Padding(
                                    padding: EdgeInsets.only(
                                        left: twenty,
                                        right: twenty,
                                        top: twenty,
                                        bottom: ten),
                                    child: SizedBox(
                                      height: ten * 5.5,
                                      width: double.infinity,
                                      child: ElevatedButton(
                                        onPressed: _submit,
                                        style: ElevatedButton.styleFrom(
                                            backgroundColor:
                                                const Color(0xFF1F2122),
                                            shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(
                                                        ten * 3.2))),
                                        child: Text('View Catering Requests',
                                            style: TextStyle(
                                                fontFamily: 'Inter',
                                                fontSize: sixteen,
                                                fontWeight: FontWeight.w600,
                                                letterSpacing: 0.32,
                                                color: Colors.white)),
                                      ),
                                    ),
                                  ),
                                  _buildMonthlySaverCard(),
                                  SizedBox(height: screenHeight * 0.08),
                                ],
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            floatingActionButton: CartFloatingActionButton(
              itemCount: Initializer.cartCount ?? 0,
              onPressed: _openCart,
            ),
          ),
        ),
      ),
    );
  }

  void _openCart() {
    print('Opening cart');
    Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CartPage(),
        ));
  }

  Widget _buildShimmerLoading(
      BuildContext context, double screenWidth, double screenHeight) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(
                horizontal: screenWidth * 0.04, vertical: screenHeight * 0.02),
            child: Shimmer.fromColors(
              baseColor: Colors.grey[300]!,
              highlightColor: Colors.grey[100]!,
              child: Container(
                width: double.infinity,
                height: screenHeight * 0.06,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(screenWidth * 0.075),
                ),
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
            child: Shimmer.fromColors(
              baseColor: Colors.grey[300]!,
              highlightColor: Colors.grey[100]!,
              child: Container(
                width: screenWidth * 0.4,
                height: screenHeight * 0.03,
                color: Colors.white,
              ),
            ),
          ),
          SizedBox(height: screenHeight * 0.02),
          SizedBox(
            height: screenHeight * 0.25,
            child: ListView.separated(
              padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
              scrollDirection: Axis.horizontal,
              itemCount: 3,
              separatorBuilder: (_, __) => SizedBox(width: screenWidth * 0.03),
              itemBuilder: (_, __) => Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(
                  width: screenWidth * 0.45,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(screenWidth * 0.03),
                  ),
                ),
              ),
            ),
          ),
          SizedBox(height: screenHeight * 0.03),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
            child: Shimmer.fromColors(
              baseColor: Colors.grey[300]!,
              highlightColor: Colors.grey[100]!,
              child: Container(
                width: screenWidth * 0.4,
                height: screenHeight * 0.03,
                color: Colors.white,
              ),
            ),
          ),
          SizedBox(height: screenHeight * 0.02),
          SizedBox(
            height: screenHeight * 0.32,
            child: ListView.separated(
              padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
              scrollDirection: Axis.horizontal,
              itemCount: 3,
              separatorBuilder: (_, __) => SizedBox(width: screenWidth * 0.03),
              itemBuilder: (_, __) => Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(
                  width: screenWidth * 0.6,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(screenWidth * 0.03),
                  ),
                ),
              ),
            ),
          ),
          SizedBox(height: screenHeight * 0.03),
          Padding(
            padding: EdgeInsets.all(screenWidth * 0.04),
            child: Shimmer.fromColors(
              baseColor: Colors.grey[300]!,
              highlightColor: Colors.grey[100]!,
              child: Container(
                width: double.infinity,
                height: screenHeight * 0.4,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(screenWidth * 0.04),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChefShimmerLoading(
      BuildContext context, double screenWidth, double screenHeight) {
    return SizedBox(
      height: ten * 26,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
        itemCount: 3,
        separatorBuilder: (_, __) => SizedBox(width: screenWidth * 0.03),
        itemBuilder: (_, __) => Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Container(
            width: ten * 23,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(screenWidth * 0.03),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLocationHeader(
      BuildContext context, double screenWidth, double screenHeight) {
    // This header now matches Home2 logic and is error-free
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.symmetric(
              horizontal: screenWidth * 0.04, vertical: screenHeight * 0.015),
          child: Row(
            children: [
              GestureDetector(
                onTap: () {
                  setState(() {
                    _showLocationMenu = !_showLocationMenu;
                    _showTimeOptions = false;
                  });
                },
                child: Icon(Icons.menu,
                    size: screenWidth * 0.06,
                    color: const Color.fromRGBO(31, 33, 34, 1),
                    semanticLabel: 'Open location menu'),
              ),
              SizedBox(width: screenWidth * 0.03),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          _showLocationMenu = !_showLocationMenu;
                          _showTimeOptions = false;
                        });
                      },
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.location_on_outlined,
                                  size: screenWidth * 0.035,
                                  color: const Color.fromRGBO(31, 33, 34, 1)),
                              SizedBox(width: screenWidth * 0.01),
                              Expanded(
                                child: _isAddressUpdating
                                    ? SizedBox(
                                        width: screenWidth * 0.6,
                                        child: Row(
                                          children: [
                                            SizedBox(
                                              width: screenWidth * 0.02,
                                              height: screenWidth * 0.02,
                                              child: CircularProgressIndicator(
                                                strokeWidth: 1,
                                                valueColor:
                                                    AlwaysStoppedAnimation<
                                                        Color>(
                                                  const Color(0xFF1F2122),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      )
                                    : Text(
                                        _currentAddressData?.addressText ??
                                            'Loading address...',
                                        style: TextStyle(
                                          fontSize: screenWidth * 0.03,
                                          fontWeight: FontWeight.w500,
                                          fontFamily: 'Inter',
                                        ),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                              ),
                            ],
                          ),
                          SizedBox(height: screenHeight * 0.005),
                          Row(
                            children: [
                              Icon(Icons.access_time,
                                  size: screenWidth * 0.03,
                                  color: const Color.fromRGBO(31, 33, 34, 1)),
                              SizedBox(width: screenWidth * 0.015),
                              InkWell(
                                onTap: () {
                                  setState(() {
                                    _showLocationMenu = true;
                                    _showTimeOptions = true;
                                  });
                                },
                                child: Row(
                                  children: [
                                    Text(
                                      _selectedDeliveryTime,
                                      style: TextStyle(
                                        fontSize: screenWidth * 0.03,
                                        fontWeight: FontWeight.w400,
                                        fontFamily: 'Inter',
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    )
                  ],
                ),
              ),
              GestureDetector(
                onTap: () async {
                  final searchResult = await Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const GlobalSearchScreen(),
                    ),
                  );
                  if (searchResult != null && mounted) {
                    final searchQuery = searchResult['searchQuery'] as String?;
                    final searchData =
                        searchResult['searchData'] as Map<String, dynamic>?;
                    if (searchQuery != null && searchData != null) {
                      setState(() {
                        _isSearchMode = true;
                        _currentSearchQuery = searchQuery;
                      });
                      context
                          .read<HomeBloc>()
                          .add(GetHomeDataEvent(data: searchData));
                      return;
                    }
                  }
                  // Always reload home data if no search was performed (or back pressed)
                  setState(() {
                    _isSearchMode = false;
                    _currentSearchQuery = null;
                  });
                  if (_currentLatitude != null && _currentLongitude != null) {
                    final savedFilters = await Initializer.getAppliedFilters();
                    if (savedFilters != null) {
                      final requestData = <String, dynamic>{
                        ...savedFilters,
                        'latitude': _currentLatitude!,
                        'longitude': _currentLongitude!,
                      };
                      context
                          .read<HomeBloc>()
                          .add(GetHomeDataEvent(data: requestData));
                    } else {
                      context.read<HomeBloc>().add(GetHomeDataEvent(
                            data: <String, dynamic>{
                              'latitude': _currentLatitude!,
                              'longitude': _currentLongitude!,
                            },
                          ));
                    }
                  }
                },
                child: Icon(Icons.search,
                    size: screenWidth * 0.06,
                    color: const Color.fromRGBO(31, 33, 34, 1),
                    semanticLabel: 'Search'),
              ),
            ],
          ),
        ),
        if (_showTimeOptions) ...[
          Container(
            color: Colors.white,
            constraints: BoxConstraints(
              maxHeight: screenHeight * 0.6,
            ),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      IconButton(
                        icon: Icon(Icons.arrow_back,
                            color: const Color(0xFF1F2122),
                            size: screenWidth * 0.05),
                        onPressed: () {
                          setState(() {
                            _showTimeOptions = false;
                          });
                        },
                      ),
                      Expanded(
                        child: Center(
                          child: Text(
                            'Schedule Delivery',
                            style: TextStyle(
                              fontSize: screenWidth * 0.045,
                              fontWeight: FontWeight.w600,
                              color: const Color(0xFF1F2122),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(width: screenWidth * 0.15),
                    ],
                  ),
                  _buildScheduleTimeOptionWidget(
                      context, screenWidth, screenHeight),
                ],
              ),
            ),
          ),
        ] else if (_showLocationMenu) ...[
          Container(
            color: Colors.white,
            constraints: BoxConstraints(
              maxHeight: screenHeight * 0.7,
            ),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding: EdgeInsets.all(screenWidth * 0.04),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Enter Your Street And House Number',
                          style: TextStyle(
                            fontSize: screenWidth * 0.035,
                            fontWeight: FontWeight.w500,
                            color: const Color(0xFF1F2122),
                            fontFamily: 'Inter',
                          ),
                        ),
                        SizedBox(height: screenHeight * 0.015),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Container(
                              decoration: BoxDecoration(
                                border: Border.all(
                                    color: const Color(0xFFE1E3E6), width: 1),
                                borderRadius:
                                    BorderRadius.circular(screenWidth * 0.09),
                              ),
                              padding: EdgeInsets.symmetric(
                                  horizontal: screenWidth * 0.045, vertical: 0),
                              height: screenHeight * 0.055,
                              child: Row(
                                children: [
                                  Expanded(
                                    child: TextField(
                                      controller: _addressController,
                                      onChanged: searchPlaces,
                                      decoration: InputDecoration(
                                        hintText: 'Street, Postal code',
                                        hintStyle: TextStyle(
                                          color: const Color(0xFF66696D),
                                          fontSize: screenWidth * 0.035,
                                          fontWeight: FontWeight.w400,
                                          fontFamily: 'Inter',
                                        ),
                                        border: InputBorder.none,
                                        contentPadding: EdgeInsets.zero,
                                        isDense: true,
                                      ),
                                    ),
                                  ),
                                  TextButton.icon(
                                    onPressed: () async {
                                      setState(() {
                                        _isAddressUpdating = true;
                                        _currentAddressData = null;
                                      });
                                      final position =
                                          await _getCurrentLocation();
                                      if (position != null) {
                                        final List<Placemark> placemarks =
                                            await placemarkFromCoordinates(
                                          position.latitude,
                                          position.longitude,
                                        );
                                        if (placemarks.isNotEmpty) {
                                          final Placemark place = placemarks[0];
                                          final String address =
                                              '${place.street}, ${place.subLocality}, ${place.locality}, ${place.postalCode}';
                                          context.read<AccountBloc>().add(
                                                AddAddressEvent({
                                                  "latitude": position.latitude,
                                                  "longitude":
                                                      position.longitude,
                                                  "address_text": address,
                                                  "is_current": true,
                                                }),
                                              );
                                          setState(() {
                                            _currentLatitude =
                                                position.latitude;
                                            _currentLongitude =
                                                position.longitude;
                                            _showLocationMenu = false;
                                          });
                                          Initializer().setCoordinates(
                                              position.latitude,
                                              position.longitude);
                                          context.read<HomeBloc>().add(
                                                GetHomeDataEvent(
                                                  data: {
                                                    'latitude':
                                                        position.latitude,
                                                    'longitude':
                                                        position.longitude,
                                                  },
                                                ),
                                              );
                                        }
                                      } else {
                                        setState(() {
                                          _isAddressUpdating = false;
                                        });
                                      }
                                    },
                                    icon: Icon(Icons.my_location,
                                        size: screenWidth * 0.05,
                                        color: const Color(0xFF1F2122)),
                                    label: Text(
                                      'Locate me',
                                      style: TextStyle(
                                        fontSize: screenWidth * 0.030,
                                        fontWeight: FontWeight.w600,
                                        fontFamily: 'Inter',
                                        color: const Color(0xFF1F2122),
                                        decoration: TextDecoration.underline,
                                      ),
                                    ),
                                    style: TextButton.styleFrom(
                                      padding: EdgeInsets.zero,
                                      minimumSize: Size.zero,
                                      tapTargetSize:
                                          MaterialTapTargetSize.shrinkWrap,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            if (_isSearching)
                              Padding(
                                padding:
                                    EdgeInsets.only(top: screenHeight * 0.01),
                                child: const Center(
                                    child: CircularProgressIndicator()),
                              )
                            else if (_showPredictions &&
                                _searchResults.isNotEmpty)
                              Container(
                                height: screenHeight * 0.15,
                                margin:
                                    EdgeInsets.only(top: screenHeight * 0.01),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius:
                                      BorderRadius.circular(screenWidth * 0.03),
                                  border: Border.all(
                                      color: const Color(0xFFE1E3E6), width: 1),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.15),
                                      blurRadius: 12,
                                      offset: const Offset(0, 4),
                                    ),
                                  ],
                                ),
                                child: Material(
                                  color: Colors.transparent,
                                  child: ListView.separated(
                                    padding: EdgeInsets.zero,
                                    shrinkWrap: true,
                                    itemCount: _searchResults.length,
                                    separatorBuilder: (_, __) => const Divider(
                                        height: 1, color: Color(0xFFE1E3E6)),
                                    itemBuilder: (_, index) {
                                      final prediction = _searchResults[index];
                                      return ListTile(
                                        dense: true,
                                        contentPadding: EdgeInsets.symmetric(
                                            horizontal: screenWidth * 0.04,
                                            vertical: screenHeight * 0.005),
                                        title: Text(
                                          prediction.description ?? '',
                                          style: TextStyle(
                                            fontSize: screenWidth * 0.035,
                                            fontFamily: 'Inter',
                                          ),
                                        ),
                                        onTap: () => selectPlace(prediction),
                                      );
                                    },
                                  ),
                                ),
                              ),
                          ],
                        ),
                        SizedBox(height: screenHeight * 0.03),
                        Text(
                          'Saved Addresses',
                          style: TextStyle(
                            fontSize: screenWidth * 0.04,
                            fontWeight: FontWeight.w600,
                            fontFamily: 'Inter',
                          ),
                        ),
                        SizedBox(height: screenHeight * 0.02),
                        if (_loadingAddresses)
                          const Center(child: CircularProgressIndicator())
                        else if (_savedAddresses == null ||
                            _savedAddresses!.isEmpty)
                          const Center(child: Text('No saved addresses'))
                        else
                          ...(_savedAddresses!
                              .where((addr) => addr.isCurrent != true)
                              .take(2)
                              .map((address) => _buildAddressListItem(
                                  context, screenWidth, screenHeight, address))
                              .toList()),
                        SizedBox(height: screenHeight * 0.02),
                        InkWell(
                          onTap: () {
                            setState(() {
                              _showTimeOptions = true;
                            });
                          },
                          child: Row(
                            children: [
                              Icon(Icons.schedule, size: screenWidth * 0.06),
                              SizedBox(width: screenWidth * 0.04),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Time preference',
                                      style: TextStyle(
                                        fontSize: screenWidth * 0.035,
                                        fontWeight: FontWeight.w500,
                                        fontFamily: 'Inter',
                                        color: const Color(0xFF1F2122),
                                      ),
                                    ),
                                    Text(
                                      _selectedDeliveryTime,
                                      style: TextStyle(
                                        fontSize: screenWidth * 0.03,
                                        color: Colors.grey,
                                        fontFamily: 'Inter',
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Icon(
                                Icons.arrow_forward_ios,
                                size: screenWidth * 0.03,
                                color: const Color(0xFF1F2122),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (_showTimeOptions) ...[
                    const Divider(),
                    Container(
                      color: const Color(0xFFF6F3EC),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          _buildScheduleTimeOptionWidget(
                              context, screenWidth, screenHeight),
                          Padding(
                            padding: EdgeInsets.all(screenWidth * 0.04),
                            child: Row(
                              children: [
                                Expanded(
                                  child: ElevatedButton(
                                    onPressed: () {
                                      setState(() {
                                        _selectedDeliveryTime =
                                            _currentlySelectedTimeRange;
                                        _showTimeOptions = false;
                                      });
                                    },
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.black,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(
                                            screenWidth * 0.06),
                                      ),
                                    ),
                                    child: Text('Schedule',
                                        style: TextStyle(
                                            fontSize: screenWidth * 0.035)),
                                  ),
                                ),
                                SizedBox(width: screenWidth * 0.03),
                                Expanded(
                                  child: OutlinedButton(
                                    onPressed: () {
                                      setState(() {
                                        _selectedDeliveryTime = 'ASAP';
                                        _showTimeOptions = false;
                                      });
                                    },
                                    style: OutlinedButton.styleFrom(
                                      side: BorderSide(
                                          color: Colors.grey.shade300),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(
                                            screenWidth * 0.06),
                                      ),
                                    ),
                                    child: Text('Deliver Now',
                                        style: TextStyle(
                                            fontSize: screenWidth * 0.035)),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
        const Divider(height: 2, color: Color.fromARGB(255, 219, 212, 212)),
      ],
    );
  }

  Future<void> searchPlaces(String query) async {
    if (query.isEmpty) {
      setState(() {
        _searchResults = [];
        _showPredictions = false;
      });
      return;
    }

    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 300), () async {
      setState(() => _isSearching = true);
      try {
        final url = Uri.parse(
          'https://maps.googleapis.com/maps/api/place/autocomplete/json?input=$query&key=$kGoogleApiKey',
        );
        final response = await http.get(url);
        if (response.statusCode == 200) {
          final json = jsonDecode(response.body);
          if (json['status'] == 'OK') {
            final predictions = json['predictions'] as List;
            setState(() {
              _searchResults = predictions
                  .map((p) => Prediction(
                        description: p['description'],
                        placeId: p['place_id'],
                      ))
                  .toList();
              _showPredictions = true;
            });
          } else {
            throw Exception('Places API error: ${json['status']}');
          }
        } else {
          throw Exception('Failed to fetch places: ${response.statusCode}');
        }
      } catch (e) {
        log('Error searching places: $e');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to search places: $e')),
        );
      } finally {
        setState(() => _isSearching = false);
      }
    });
  }

  String _formatTimeToAMPM(String? time) {
    if (time == null || time.isEmpty) return '';
    try {
      final parts = time.split(':');
      int hour = int.parse(parts[0]);
      final minute = parts[1];
      final period = hour >= 12 ? 'PM' : 'AM';
      hour = hour % 12 == 0 ? 12 : hour % 12;
      return '$hour:$minute $period';
    } catch (e) {
      return time;
    }
  }

  Widget _buildScheduleTimeOptionWidget(
      BuildContext context, double screenWidth, double screenHeight) {
    if (_availableTimings == null || _availableTimings!.isEmpty) {
      return Center(
        child: Padding(
          padding: EdgeInsets.all(screenWidth * 0.04),
          child: Text(
            'No time slots available',
            style: TextStyle(
              fontSize: screenWidth * 0.035,
              fontWeight: FontWeight.w400,
              fontFamily: 'Inter',
              color: const Color(0xFF1F2122),
            ),
          ),
        ),
      );
    }

    return BlocBuilder<MealplanBloc, MealPlanState>(
      builder: (context, state) {
        return Column(
          children: [
            Column(
              children: _availableTimings!.map((timing) {
                final timeSlot =
                    '${_formatTimeToAMPM(timing.startTime)} - ${_formatTimeToAMPM(timing.endTime)}';
                return InkWell(
                  onTap: () {
                    setState(() {
                      _currentlySelectedTimeRange = timeSlot;
                      _selectedTimeId = timing.id;
                    });
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(
                        horizontal: screenWidth * 0.06,
                        vertical: screenHeight * 0.015),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          timeSlot,
                          style: TextStyle(
                            fontSize: screenWidth * 0.035,
                            fontWeight: FontWeight.w400,
                            fontFamily: 'Inter',
                            color: const Color(0xFF1F2122),
                          ),
                        ),
                        if (timeSlot == _currentlySelectedTimeRange)
                          Icon(Icons.check,
                              color: const Color(0xFF1F2122),
                              size: screenWidth * 0.05),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
            Padding(
              padding: EdgeInsets.all(screenWidth * 0.04),
              child: Column(
                children: [
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: state is AddTimePreferencesLoading
                          ? null
                          : () {
                              if (_selectedTimeId != null) {
                                context.read<MealplanBloc>().add(
                                      AddTimePreferences({
                                        "time_preference_id": _selectedTimeId
                                      }),
                                    );
                              } else {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                      content:
                                          Text('Please select a time slot')),
                                );
                              }
                            },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.black,
                        shape: RoundedRectangleBorder(
                            borderRadius:
                                BorderRadius.circular(screenWidth * 0.06)),
                        padding:
                            EdgeInsets.symmetric(vertical: screenWidth * 0.03),
                      ),
                      child: state is AddTimePreferencesLoading
                          ? SizedBox(
                              height: screenWidth * 0.05,
                              width: screenWidth * 0.05,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : Text(
                              'Schedule',
                              style: TextStyle(
                                fontSize: screenWidth * 0.035,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                                fontFamily: 'Inter',
                              ),
                            ),
                    ),
                  ),
                  // SizedBox(height: screenHeight * 0.02),
                  SizedBox(
                    width: double.infinity,
                    child: OutlinedButton(
                      onPressed: () {
                        setState(() {
                          _selectedDeliveryTime = 'ASAP';
                          _showTimeOptions = false;
                          _showLocationMenu = false;
                        });
                      },
                      style: OutlinedButton.styleFrom(
                        backgroundColor: Colors.white,
                        side: const BorderSide(color: Color(0xFFE1E3E6)),
                        shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.circular(screenWidth * 0.06),
                        ),
                        padding:
                            EdgeInsets.symmetric(vertical: screenWidth * 0.03),
                      ),
                      child: Text(
                        'Deliver Now',
                        style: TextStyle(
                          fontSize: screenWidth * 0.035,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF1F2122),
                          fontFamily: 'Inter',
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Future<void> selectPlace(Prediction prediction) async {
    setState(() => _isSearching = true);
    try {
      final url = Uri.parse(
        'https://maps.googleapis.com/maps/api/place/details/json?place_id=${prediction.placeId}&fields=geometry&key=$kGoogleApiKey',
      );
      final response = await http.get(url);
      if (response.statusCode == 200) {
        final json = jsonDecode(response.body);
        if (json['status'] == 'OK') {
          final location = json['result']['geometry']['location'];
          final lat = location['lat'];
          final lng = location['lng'];

          context.read<AccountBloc>().add(
                AddAddressEvent({
                  "latitude": lat,
                  "longitude": lng,
                  "address_text": prediction.description,
                  "is_current": true,
                }),
              );

          setState(() {
            _currentLatitude = lat;
            _currentLongitude = lng;
            _addressController.text = prediction.description ?? '';
            _showPredictions = false;
            _searchResults = [];
            _showLocationMenu = false;
            _addressLoaded = true;
            _loadingChefs = true; // Start loading chefs
          });

          Initializer().setCoordinates(lat, lng);
          context.read<HomeBloc>().add(
                GetHomeDataEvent(
                  data: {
                    'latitude': lat,
                    'longitude': lng,
                  },
                ),
              );
        } else {
          throw Exception('Place details API error: ${json['status']}');
        }
      } else {
        throw Exception(
            'Failed to fetch place details: ${response.statusCode}');
      }
    } catch (e) {
      log('Error selecting place: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to select place: $e')),
      );
    } finally {
      setState(() => _isSearching = false);
    }
  }

  Widget _buildAddressListItem(BuildContext context, double screenWidth,
      double screenHeight, AddressData address) {
    return InkWell(
      onTap: () => _selectAddress(address),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: screenHeight * 0.01),
        child: Row(
          children: [
            Icon(Icons.location_on_outlined, size: screenWidth * 0.06),
            SizedBox(width: screenWidth * 0.04),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    address.addressText ?? 'No address text',
                    style: TextStyle(
                      fontSize: screenWidth * 0.035,
                      fontWeight: FontWeight.w400,
                      fontFamily: 'Inter',
                      color: const Color(0xFF1F2122),
                    ),
                  ),
                  if (address.isCurrent == true)
                    Text(
                      'Current address',
                      style: TextStyle(
                        fontSize: screenWidth * 0.03,
                        color: Colors.green,
                      ),
                    ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: screenWidth * 0.03,
              color: const Color(0xFF1F2122),
            ),
          ],
        ),
      ),
    );
  }

  Future<Position?> _getCurrentLocation() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Location services are disabled')),
        );
        return null;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Location permission denied')),
          );
          return null;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text('Location permission permanently denied')),
        );
        return null;
      }

      return await Geolocator.getCurrentPosition();
    } catch (e) {
      log('Error getting location: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to get location: $e')),
      );
      return null;
    }
  }

  void _selectAddress(AddressData address) async {
    try {
      context.read<AccountBloc>().add(
            EditAddressEvent({
              "id": address.id ?? 0,
              "is_current": true,
            }),
          );
      _pendingLatitude = address.location?.coordinates?[1];
      _pendingLongitude = address.location?.coordinates?[0];
    } catch (e) {
      log('Error selecting address: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to select address: $e')),
      );
    }
  }

  void _toggleSearchPopup() {
    if (_isSearchPopupVisible) {
      _closeSearchPopup();
    } else {
      _showSearchPopup();
    }
  }

  void _showSearchPopup() {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final offset = renderBox.localToGlobal(Offset.zero);

    final searchIconPosition =
        Offset(offset.dx + renderBox.size.width - 40, offset.dy + 45);

    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        top: searchIconPosition.dy,
        left: 10,
        right: 6,
        child: Material(
          elevation: 8,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                  child: Row(
                    children: [
                      Expanded(
                        child: Row(
                          children: [
                            SizedBox(width: 20),
                            Padding(
                              padding: const EdgeInsets.only(right: 4),
                              child: Icon(Icons.search,
                                  color: Color(0xFF414346), size: 20),
                            ),
                            // SizedBox(width: 8),
                            Expanded(
                              child: TextField(
                                decoration: InputDecoration(
                                  hintText: 'Search Dishes, Chefs, cuisines...',
                                  hintStyle: TextStyle(
                                    fontFamily: 'Inter',
                                    fontWeight: FontWeight.w400,
                                    fontSize: 14,
                                    color: Color(0xFF909090),
                                  ),
                                  border: InputBorder.none,
                                  contentPadding: EdgeInsets.zero,
                                  isDense: true,
                                ),
                                autofocus: true,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        margin: const EdgeInsets.only(right: 1),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              padding: EdgeInsets.zero,
                              visualDensity: VisualDensity.compact,
                              constraints: BoxConstraints.tightFor(
                                  width: 20, height: 40),
                              icon: Icon(Icons.cancel_outlined,
                                  color: Color(0xFF1F2122), size: 18),
                              onPressed: () {},
                            ),
                            IconButton(
                              padding: EdgeInsets.zero,
                              visualDensity: VisualDensity.compact,
                              constraints: BoxConstraints.tightFor(
                                  width: 20, height: 40),
                              icon: Icon(Icons.close,
                                  color: Color(0xFF1F2122), size: 25),
                              onPressed: _closeSearchPopup,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Divider(height: 1, thickness: 1),
                SingleChildScrollView(
                  child: Container(
                    constraints: BoxConstraints(maxHeight: 290),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 10),
                          child: Row(
                            children: [
                              _buildSearchTab('All', true),
                              _buildSearchTab('Dishes', false),
                              _buildSearchTab('Chefs', false),
                            ],
                          ),
                        ),
                        ListTile(
                          leading: Image.asset(
                            'assets/icons/recent.png',
                            width: 24,
                            height: 24,
                            color: const Color(0xFF1F2122),
                          ),
                          title: Text('Recent',
                              style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  fontSize: 16,
                                  fontFamily: 'Inter')),
                          dense: true,
                          horizontalTitleGap: 6,
                          contentPadding:
                              EdgeInsets.symmetric(horizontal: 16, vertical: 0),
                        ),
                        _buildSearchItem('Chinese'),
                        _buildSearchItem('Chicken'),
                        _buildSearchItem('Asian'),
                        Padding(
                          padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                          child: Text('Popular searches',
                              style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontWeight: FontWeight.w600,
                                  fontSize: 16,
                                  color: Color(0xFF1F2122))),
                        ),
                        const SizedBox(height: 8),
                        Padding(
                          padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                          child: Wrap(
                            spacing: 8,
                            runSpacing: 8,
                            children: [
                              _buildSearchTag('Pizza'),
                              _buildSearchTag('Burger'),
                              _buildSearchTag('Curry'),
                              _buildSearchTag('Tacos'),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
    _isSearchPopupVisible = true;
  }

  void _closeSearchPopup() {
    if (_overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
      _isSearchPopupVisible = false;
    }
  }

  Widget _buildSearchTab(String title, bool isActive) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 7),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            height: 20,
            alignment: Alignment.center,
            child: Text(
              title,
              style: TextStyle(
                color: isActive ? Color(0xFF1F2122) : Color(0xFF66696D),
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          SizedBox(height: 3),
          Container(
            height: 3,
            width: 20,
            color: isActive ? Colors.amber : Colors.transparent,
          ),
        ],
      ),
    );
  }

  Widget _buildSearchItem(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 14,
          fontFamily: 'Inter',
          fontWeight: FontWeight.w400,
          color: const Color(0xFF1F2122),
        ),
      ),
    );
  }

  Widget _buildSearchTag(String title) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 5),
      decoration: BoxDecoration(
        color: const Color(0xFFE1E3E6),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 14,
          fontFamily: 'Inter',
          fontWeight: FontWeight.w600,
          color: const Color(0xFF1F2122),
        ),
      ),
    );
  }

  Widget _buildFilterButton() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: InkWell(
        // onTap: () => _showFilterBottomSheet(),
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 14),
          decoration: BoxDecoration(
            border: Border.all(
              color: const Color.fromRGBO(31, 33, 34, 1),
              width: 1.5,
            ),
            borderRadius: BorderRadius.circular(30),
            color: const Color.fromRGBO(246, 243, 236, 1),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.tune, size: 18, color: Colors.grey[800]),
              const SizedBox(width: 8),
              Text('View Filters',
                  style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'Inter',
                      color: const Color.fromRGBO(31, 33, 34, 1))),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNoDataMessage(BuildContext context, double screenWidth,
      double screenHeight, String message) {
    return Padding(
      padding: EdgeInsets.symmetric(
          horizontal: screenWidth * 0.04, vertical: screenHeight * 0.02),
      child: Center(
        child: Text(
          message,
          style: TextStyle(
            fontSize: screenWidth * 0.04,
            fontWeight: FontWeight.w400,
            fontFamily: 'Inter',
            color: const Color(0xFF66696D),
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget _buildPopularChefs(BuildContext context, double screenWidth,
      double screenHeight, List<ChefData> chefs) {
    return SizedBox(
      height: ten * 26,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
        itemCount: chefs.length,
        separatorBuilder: (_, __) => SizedBox(width: screenWidth * 0.03),
        itemBuilder: (_, index) {
          final chef = chefs[index];
          final name =
              '${chef.chef?.firstName ?? ''} ${chef.chef?.lastName ?? ''}';
          final availability = chef.chef?.operationDays
                  ?.map((day) => day.day?.name?.substring(0, 1) ?? '')
                  .join(', ') ??
              '';
          final cuisines = chef.searchTags?.join(', ') ?? 'Various Cuisines';
          final distance = _formatDistance(chef.distance);
          final rating = chef.averageRating; // Static rating value
          final prepTime = '30-45 mins'; // Static prep time value

          return _buildChefCard(
            context: context,
            screenWidth: screenWidth,
            screenHeight: screenHeight,
            id: chef.chefId ?? 0,
            image: ServerHelper.imageUrl + (chef.profilePhoto ?? ''),
            name: name,
            cuisines: cuisines,
            rating: rating.toString(),
            distance: distance,
            availability: availability,
            dishImage: ServerHelper.imageUrl + (chef.coverPhoto ?? ''),
            prepTime: prepTime,
          );
        },
      ),
    );
  }

  Widget _buildChefCard({
    required BuildContext context,
    required double screenWidth,
    required double screenHeight,
    required String image,
    required String name,
    required String cuisines,
    required String rating,
    required String distance,
    required String availability,
    required String dishImage,
    required String prepTime,
    required int id,
  }) {
    return GestureDetector(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ViewChef2(
                id: id,
                title: name,
                latitude: _currentLatitude ?? 0,
                longitude: _currentLongitude ?? 0,
                distance: distance,
              ),
            ),
          );
        },
        child: Container(
          width: ten * 23,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(screenWidth * 0.03),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.01),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Stack(
                clipBehavior: Clip.none,
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(screenWidth * 0.03),
                      topRight: Radius.circular(screenWidth * 0.03),
                    ),
                    child: Image.network(
                      dishImage,
                      width: double.infinity,
                      height: ten * 12,
                      fit: BoxFit.cover,
                      loadingBuilder: (context, child, loadingProgress) {
                        if (loadingProgress == null) return child;
                        return Shimmer.fromColors(
                          baseColor: Colors.grey[300]!,
                          highlightColor: Colors.grey[100]!,
                          child: Container(
                            width: double.infinity,
                            height: ten * 12,
                            color: Colors.white,
                          ),
                        );
                      },
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          width: double.infinity,
                          height: ten * 12,
                          color: Colors.grey[300],
                          child: Icon(Icons.image_not_supported,
                              color: Colors.grey[600]),
                        );
                      },
                    ),
                  ),
                  Positioned(
                    top: screenWidth * 0.04,
                    left: screenWidth * 0.04,
                    child: Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: screenWidth * 0.02,
                            vertical: screenWidth * 0.01),
                        decoration: BoxDecoration(
                          color: const Color.fromARGB(0, 0, 0, 0),
                          borderRadius:
                              BorderRadius.circular(screenWidth * 0.03),
                        ),
                        child: SizedBox.shrink()
                        // Row(
                        //   children: [
                        //     Icon(Icons.access_time,
                        //         size: screenWidth * 0.03, color: Colors.white),
                        //     SizedBox(width: screenWidth * 0.01),
                        //     Text(
                        //       prepTime,
                        //       style: TextStyle(
                        //         fontSize: screenWidth * 0.03,
                        //         color: Colors.white,
                        //         fontWeight: FontWeight.w500,
                        //       ),
                        //     ),
                        //   ],
                        // ),

                        ),
                  ),
                  Positioned(
                    left: screenWidth * 0.04,
                    bottom: -screenWidth * 0.05,
                    child: Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 3),
                      ),
                      child: CircleAvatar(
                        radius: screenWidth * 0.075,
                        backgroundImage: NetworkImage(image),
                        onBackgroundImageError: (exception, stackTrace) {
                          print('Error loading chef image: $exception');
                        },
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: screenHeight * 0.03),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      name,
                      style: TextStyle(
                        fontSize: forteen,
                        fontWeight: FontWeight.w500,
                        fontFamily: 'Inter',
                      ),
                    ),
                    SizedBox(height: screenHeight * 0.008),
                    Text(
                      cuisines,
                      style: TextStyle(
                        fontSize: twelve,
                        fontWeight: FontWeight.w400,
                        fontFamily: 'Inter',
                        color: const Color.fromRGBO(65, 67, 70, 1),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: screenHeight * 0.01),
                    Row(
                      children: [
                        Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: screenWidth * 0.015,
                              vertical: screenWidth * 0.005),
                          decoration: BoxDecoration(
                            color: const Color.fromRGBO(225, 227, 230, 1),
                            borderRadius:
                                BorderRadius.circular(screenWidth * 0.03),
                          ),
                          child: Row(
                            children: [
                              Image.asset(
                                'assets/icons/thump.png',
                                width: screenWidth * 0.0275,
                                height: screenWidth * 0.025,
                                color: Colors.black54,
                              ),
                              SizedBox(width: screenWidth * 0.01),
                              Text(
                                rating,
                                style: TextStyle(
                                  fontSize: ten,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: 'Inter',
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(width: screenWidth * 0.02),
                        Row(
                          children: [
                            Icon(Icons.location_on_outlined,
                                size: screenWidth * 0.03,
                                color: const Color(0xFF1F2122)),
                            SizedBox(width: screenWidth * 0.005),
                            Text(
                              distance,
                              style: TextStyle(
                                fontSize: ten,
                                fontWeight: FontWeight.w400,
                                fontFamily: 'Inter',
                                color: const Color(0xFF1F2122),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    SizedBox(height: screenHeight * 0.008),
                    Row(
                      children: [
                        Image.asset(
                          'assets/icons/calender_2.png',
                          width: screenWidth * 0.03,
                          height: screenWidth * 0.0325,
                          color: Colors.black54,
                        ),
                        SizedBox(width: screenWidth * 0.01),
                        Text(
                          availability,
                          style: TextStyle(
                            fontSize: twelve,
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w400,
                            color: Colors.black54,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ));
  }

  Widget _buildSectionHeader(
      BuildContext context, double screenWidth, String title) {
    return Padding(
      padding: EdgeInsets.fromLTRB(screenWidth * 0.04, screenWidth * 0.03,
          screenWidth * 0.04, screenWidth * 0.01),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: twenty,
              fontWeight: FontWeight.w700,
              fontFamily: 'Inter',
              height: 1,
            ),
          ),
          GestureDetector(
            onTap: () {
              if (title == 'Top-Rated Chefs') {
                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const TopRatedChefsPage()));
              } else if (title == 'Recommended For You') {
                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const RecommendedChefsPage()));
              } else if (title == 'Most Popular Near You') {
                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const PopularChefsNearPage()));
              } else if (title == 'Explore Deals') {
                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const DealsOfTheDay()));
              }
            },
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'See All',
                  style: TextStyle(
                    fontSize: twelve,
                    fontWeight: FontWeight.w800,
                    height: 0.9,
                    color: Colors.black,
                  ),
                ),
                SizedBox(height: screenWidth * 0.0025),
                Container(
                  height: 1.2,
                  width: screenWidth * 0.09,
                  color: Colors.black54,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPromoCards() {
    return SizedBox(
      height: 210,
      child: ListView.separated(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        scrollDirection: Axis.horizontal,
        itemCount: 2,
        separatorBuilder: (_, __) => const SizedBox(width: 12),
        itemBuilder: (_, index) {
          if (index == 0) {
            return Container(
              width: 180,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 7,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ClipRRect(
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(12),
                      topRight: Radius.circular(12),
                    ),
                    child: Image.asset(
                      'assets/images/exp_deals.png',
                      height: 100,
                      width: double.infinity,
                      fit: BoxFit.cover,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(10.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('16% off all items',
                            style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 18,
                                height: 1.2)),
                        const SizedBox(height: 12),
                        Row(
                          children: const [
                            CircleAvatar(
                              radius: 10,
                              backgroundImage:
                                  AssetImage('assets/images/chef.png'),
                            ),
                            SizedBox(width: 6),
                            Text('Chef Aziz A.',
                                style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                    color: Color.fromARGB(255, 12, 12, 12))),
                          ],
                        ),
                        const SizedBox(height: 18),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text('View Deal',
                                style: TextStyle(
                                    fontSize: 12, fontWeight: FontWeight.w600)),
                            // const SizedBox(height: 0.5),
                            Container(
                              height: 1.0,
                              width: 50,
                              color: Colors.black54,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          } else {
            return Container(
              width: 180,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ClipRRect(
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(12),
                      topRight: Radius.circular(12),
                    ),
                    child: Image.asset(
                      'assets/images/exp_deals_2.png',
                      height: 100,
                      width: double.infinity,
                      fit: BoxFit.cover,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(10.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('16% off all items',
                            style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 18,
                                height: 1.2)),
                        const SizedBox(height: 12),
                        Row(
                          children: const [
                            CircleAvatar(
                              radius: 10,
                              backgroundImage:
                                  AssetImage('assets/images/chef_3.png'),
                            ),
                            SizedBox(width: 6),
                            Text('Chef Aiko T.',
                                style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                    color: Color.fromARGB(255, 12, 12, 12))),
                          ],
                        ),
                        const SizedBox(height: 18),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text('View Deal',
                                style: TextStyle(
                                    fontSize: 12, fontWeight: FontWeight.w600)),
                            // const SizedBox(height: 0.5),
                            Container(
                              height: 1.0,
                              width: 50,
                              color: Colors.black54,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          }
        },
      ),
    );
  }

  Widget _buildMealPlanCard() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.black,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
              child: Image.asset(
                'assets/images/weekly_plan.png',
                width: double.infinity,
                height: 240,
                fit: BoxFit.cover,
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        decoration: const BoxDecoration(
                          color: Color(0xFFFFBE16),
                          shape: BoxShape.circle,
                        ),
                        width: 32,
                        height: 32,
                        child: Padding(
                          padding: const EdgeInsets.all(8),
                          child: Image.asset(
                            'assets/icons/date_range.png',
                            width: 15,
                            height: 15,
                            fit: BoxFit.contain,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      SizedBox(width: 8),
                      Text('Weekly Meal Plan',
                          style: TextStyle(
                              color: Colors.white,
                              fontSize: 25,
                              fontWeight: FontWeight.w700)),
                    ],
                  ),
                  const SizedBox(height: 12),
                  const Text(
                    'Subscribe to curated dishes delivered weekly.\nDishes starting at \$9, skip or cancel anytime.',
                    style: TextStyle(
                        color: const Color(0xFFAAADB1),
                        fontSize: 14,
                        height: 1.4),
                  ),
                  const SizedBox(height: 16),
                  const Padding(
                    padding: EdgeInsets.only(left: 8),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.circle,
                              size: 6,
                              color: Color(0xFFAAADB1),
                            ),
                            SizedBox(width: 8),
                            Text('Discounted Pricing',
                                style: TextStyle(
                                    color: Color(0xFFAAADB1), fontSize: 14)),
                          ],
                        ),
                        SizedBox(height: 4),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.circle,
                              size: 6,
                              color: Color(0xFFAAADB1),
                            ),
                            SizedBox(width: 8),
                            Text('Free Delivery',
                                style: TextStyle(
                                    color: Color(0xFFAAADB1), fontSize: 14)),
                          ],
                        ),
                        SizedBox(height: 4),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.circle,
                              size: 6,
                              color: Color(0xFFAAADB1),
                            ),
                            SizedBox(width: 8),
                            Text('Top-Rated Chefs',
                                style: TextStyle(
                                    color: Color(0xFFAAADB1), fontSize: 14)),
                          ],
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                  InkWell(
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const ChoosePlan(),
                        ),
                      );
                    },
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(vertical: 14),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: const Color(0xFFAAADB1),
                        ),
                        borderRadius: BorderRadius.circular(30),
                      ),
                      child: const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text('Start A Meal Plan',
                              style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500)),
                          SizedBox(width: 8),
                          Icon(Icons.arrow_forward,
                              color: Colors.white, size: 16),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 12), // Added padding below the button
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMonthlySaverCard() {
    return Padding(
      padding: EdgeInsets.all(screenWidth * 0.04),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.black,
          borderRadius: BorderRadius.circular(screenWidth * 0.04),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(screenWidth * 0.04),
                topRight: Radius.circular(screenWidth * 0.04),
              ),
              child: Image.network(
                'https://images.pexels.com/photos/1128783/pexels-photo-1128783.jpeg',
                width: double.infinity,
                height: screenHeight * 0.2,
                fit: BoxFit.cover,
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Container(
                    width: double.infinity,
                    height: screenHeight * 0.2,
                    color: Colors.grey[300],
                    child: const Center(
                      child: CupertinoActivityIndicator(
                        color: Color(0xFF1F2122),
                        radius: 5,
                      ),
                    ),
                  );
                },
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: double.infinity,
                    height: screenHeight * 0.2,
                    color: Colors.grey[300],
                    child: const Icon(Icons.broken_image, color: Colors.grey),
                  );
                },
              ),
            ),
            Padding(
              padding: EdgeInsets.all(screenWidth * 0.03),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        decoration: const BoxDecoration(
                          color: Color(0xFFFFBE16),
                          shape: BoxShape.circle,
                        ),
                        width: screenWidth * 0.067,
                        height: screenWidth * 0.067,
                        child: Padding(
                          padding: EdgeInsets.all(screenWidth * 0.02),
                          child: Image.asset(
                            'assets/icons/percent.png',
                            width: screenWidth * 0.04,
                            height: screenWidth * 0.04,
                            fit: BoxFit.contain,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      SizedBox(width: 8),
                      Text("Catering Requests",
                          style: TextStyle(
                              color: Colors.white,
                              fontSize: screenWidth * 0.04584,
                              fontWeight: FontWeight.w700)),
                    ],
                  ),
                  SizedBox(height: screenHeight * 0.008),
                  Text(
                    'Customise your dream catering experience with our catering requests. Choose from a variety of options and let us handle the rest.',
                    style: TextStyle(
                        color: const Color(0xFFAAADB1),
                        fontSize: screenWidth * 0.03,
                        height: 1.4),
                  ),
                  SizedBox(height: screenHeight * 0.01),
                  Padding(
                    padding: EdgeInsets.only(left: screenWidth * 0.02),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.circle,
                              size: screenWidth * 0.015,
                              color: Color(0xFFAAADB1),
                            ),
                            SizedBox(width: screenWidth * 0.02),
                            Text('Unlimited Free Delivery (Capped at \$2.0)',
                                style: TextStyle(
                                    color: Color(0xFFAAADB1),
                                    fontSize: screenWidth * 0.03)),
                          ],
                        ),
                        SizedBox(height: screenHeight * 0.01),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.circle,
                              size: screenWidth * 0.015,
                              color: Color(0xFFAAADB1),
                            ),
                            SizedBox(width: screenWidth * 0.02),
                            Text('Up to 30% Off Restaurants',
                                style: TextStyle(
                                    color: Color(0xFFAAADB1),
                                    fontSize: screenWidth * 0.03)),
                          ],
                        ),
                        SizedBox(height: screenHeight * 0.003),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.circle,
                              size: screenWidth * 0.015,
                              color: Color(0xFFAAADB1),
                            ),
                            SizedBox(width: screenWidth * 0.02),
                            Text('Surprise Perks',
                                style: TextStyle(
                                    color: Color(0xFFAAADB1),
                                    fontSize: screenWidth * 0.03)),
                          ],
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: screenHeight * 0.015),
                  GestureDetector(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (_) => WithNavBar(
                            currentIndex: 2,
                            child:
                                SendCateringRequestPage(), // Catering tab index
                          ),
                        ),
                      );
                    },
                    child: Container(
                      width: double.infinity,
                      padding:
                          EdgeInsets.symmetric(vertical: screenWidth * 0.035),
                      decoration: BoxDecoration(
                        color: Colors.black, // Add a background color
                        border: Border.all(
                          color: const Color(0xFFAAADB1),
                        ),
                        borderRadius:
                            BorderRadius.circular(screenWidth * 0.075),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Start A Catering Request',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: screenWidth * 0.03,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          SizedBox(width: screenWidth * 0.02),
                          Icon(Icons.arrow_forward,
                              color: Colors.white, size: screenWidth * 0.04),
                        ],
                      ),
                    ),
                  ),
                  // const SizedBox(height: 12),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void showChefLocationModal(BuildContext context) {
    showDialog(
      context: context,
      builder: (_) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          insetPadding: const EdgeInsets.symmetric(horizontal: 16),
          backgroundColor: Colors.white,
          child: Padding(
            padding: const EdgeInsets.fromLTRB(20, 24, 20, 24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: const BoxDecoration(
                    color: Color(0xFFFFBE16),
                    shape: BoxShape.circle,
                  ),
                  child:
                      const Icon(Icons.search, color: Colors.black, size: 24),
                ),
                const SizedBox(height: 16),
                const Text(
                  'Find chefs in your area',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'Inter',
                    color: const Color(0xFF1F2122),
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'We would like to use your location to show\nall the amazing options in your area',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                    fontFamily: 'Inter',
                    height: 1.5,
                    color: const Color(0xFF414346),
                  ),
                ),
                const SizedBox(height: 18),
                Container(
                  height: 40,
                  decoration: BoxDecoration(
                    border: Border.all(color: const Color(0xFFE1E3E6)),
                    borderRadius: BorderRadius.circular(24),
                    color: Colors.white,
                  ),
                  child: Row(
                    children: [
                      const Expanded(
                        child: Padding(
                          padding: EdgeInsets.only(left: 16),
                          child: TextField(
                            decoration: InputDecoration(
                              border: InputBorder.none,
                              hintText: 'Search for an address',
                              isDense: true,
                              hintStyle: TextStyle(
                                color: const Color(0xFF66696D),
                                fontSize: 16,
                                fontWeight: FontWeight.w400,
                                fontFamily: 'Inter',
                              ),
                            ),
                            style: TextStyle(fontSize: 14),
                          ),
                        ),
                      ),
                      // Locate me button
                      TextButton.icon(
                        onPressed: () {},
                        icon: Image.asset(
                          'assets/icons/my_location.png',
                          width: 21,
                          height: 21,
                          color: const Color(0xFF414346),
                        ),
                        label: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Text(
                              'Locate me',
                              style: TextStyle(
                                color: Color.fromARGB(255, 5, 5, 5),
                                fontWeight: FontWeight.w600,
                                fontFamily: 'Inter',
                                fontSize: 13,
                                letterSpacing: 0.5,
                                height: 0.9,
                              ),
                            ),
                            const SizedBox(height: 1.0),
                            Container(
                              height: 1.5,
                              width: 58,
                              color: Colors.black54,
                            ),
                          ],
                        ),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(horizontal: 12),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 13),
                SizedBox(
                  width: double.infinity,
                  height: 48,
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF151515),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(24),
                      ),
                      elevation: 0,
                    ),
                    child: const Text(
                      'Deliver Here',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        fontFamily: 'Inter',
                        color: Color(0xFFFFFFFF),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget buildBottomBar() {
    return Container(
      height: 58,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildNavItem('assets/icons/home.png', 'Home', true),
          _buildNavItem('assets/icons/orders.png', 'Orders', false),
          _buildNavItem('assets/icons/catering.png', 'Catering', false),
          _buildNavItem('assets/icons/message.png', 'Messages', false),
          _buildNavItem('assets/icons/account.png', 'Account', false),
        ],
      ),
    );
  }

  Widget _buildNavItem(String iconPath, String label, bool isActive) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Image.asset(
          iconPath,
          width: 18,
          height: 20,
          color: const Color.fromRGBO(31, 33, 34, 1),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 10,
            fontFamily: 'Inter',
            fontWeight: isActive ? FontWeight.w400 : FontWeight.w400,
            color: isActive ? const Color(0xFF1F2122) : Colors.grey,
          ),
        ),
      ],
    );
  }

  void _applyFilters() {
    // This is where you would implement the filtering logic
    setState(() {
      // Filter your lists of chefs, dishes, etc. based on the current filterState
      // Update the UI accordingly
    });
  }

  Widget _buildScheduleTimeOption(String timeRange, StateSetter setState) {
    return InkWell(
      onTap: () {
        setState(() {
          _currentlySelectedTimeRange = timeRange;
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 24, vertical: 11),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              timeRange,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                fontFamily: 'Inter',
                color: Color(0xFF1F2122),
              ),
            ),
            if (timeRange == _currentlySelectedTimeRange)
              Icon(Icons.check, color: Color(0xFF1F2122)),
          ],
        ),
      ),
    );
  }
}

class Prediction {
  final String? description;
  final String? placeId;

  Prediction({this.description, this.placeId});
}
