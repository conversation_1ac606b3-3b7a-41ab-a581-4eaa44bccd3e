import 'dart:async';
import 'package:db_eats/bloc/home_bloc.dart';
import 'package:db_eats/bloc/main_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/data/models/deals/dealslistmodel.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geolocator/geolocator.dart';
import 'package:lottie/lottie.dart';

class DealsOfTheDay extends StatefulWidget {
  const DealsOfTheDay({super.key});

  @override
  State<DealsOfTheDay> createState() => _DealsOfTheDayState();
}

class _DealsOfTheDayState extends State<DealsOfTheDay> {
  final ScrollController _scrollController = ScrollController();
  final Color _backgroundColor = const Color(0xFFF6F3EC);
  final Color _cardColor = Colors.white;
  final Color _primaryTextColor = const Color(0xFF1F2122);
  final Color _secondaryTextColor = const Color(0xFF414346);
  final Color _accentColor = const Color(0xFF2E7D32);
  final int _currentPage = 1;
  double? _currentLatitude;
  double? _currentLongitude;
  bool _isFetchingLocation = true;

  @override
  void initState() {
    super.initState();
    _getLocationAndLoadChefs();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _getLocationAndLoadChefs() async {
    try {
      // Check location permissions using Initializer
      bool isPermissionGranted =
          await Initializer.checkLocationPermission(context);
      if (!isPermissionGranted) {
        setState(() {
          _isFetchingLocation = false;
        });
        return;
      }

      // Get latitude and longitude from Initializer
      _currentLatitude = Initializer().getLatitude;
      _currentLongitude = Initializer().getLongitude;

      // Check if coordinates are available
      if (_currentLatitude != null && _currentLongitude != null) {
        context.read<MainBloc>().add(
              DealsListEvent(
                page: _currentPage,
                limit: 10,
                lat: _currentLatitude!,
                lon: _currentLongitude!,
              ),
            );
      } else {
        // Fallback: If coordinates are not available, try to get current position
        final position = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high,
        );
        _currentLatitude = position.latitude;
        _currentLongitude = position.longitude;
        // Update Initializer with new coordinates
        Initializer().setCoordinates(_currentLatitude!, _currentLongitude!);

        context.read<MainBloc>().add(
              DealsListEvent(
                page: _currentPage,
                limit: 10,
                lat: _currentLatitude!,
                lon: _currentLongitude!,
              ),
            );
      }
    } catch (e) {
      print('Error fetching location: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to get location: $e')),
      );
    } finally {
      setState(() {
        _isFetchingLocation = false;
      });
    }
  }

  Future<void> _navigateBack() async {
    final savedFilters = await Initializer.getAppliedFilters();

    // Create request data with coordinates regardless of filter status
    final requestData = <String, dynamic>{
      'latitude': Initializer.latitude,
      'longitude': Initializer.longitude,
    };

    // Add filter data if it exists
    if (savedFilters != null) {
      requestData.addAll(savedFilters);
    }

    // Call the event with either just coordinates or coordinates + filters
    context.read<HomeBloc>().add(GetHomeDataEvent(data: requestData));
    Navigator.pop(context);
  }

  // Helper method to get responsive sizes based on screen width
  double _getResponsiveSize(BuildContext context, double factor) {
    final size = MediaQuery.of(context).size;
    return size.width * factor; // Use percentage of screen width
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isLandscape = size.width > size.height;
    final baseTextSize =
        _getResponsiveSize(context, 0.04); // 4% of screen width
    final padding =
        _getResponsiveSize(context, 0.05); // 5% of screen width for padding

    return Scaffold(
      backgroundColor: _backgroundColor,
      appBar: AppBar(
        backgroundColor: _backgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: _primaryTextColor,
            size: isLandscape ? size.height * 0.04 : baseTextSize * 1.5,
          ),
          onPressed: _navigateBack,
        ),
        title: Text(
          'Deals',
          style: TextStyle(
            color: _primaryTextColor,
            fontSize: isLandscape ? size.height * 0.04 : baseTextSize * 1.4,
            fontWeight: FontWeight.w600,
            fontFamily: 'Inter',
          ),
        ),
      ),
      body: _isFetchingLocation
          ? Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(_accentColor),
              ),
            )
          : BlocConsumer<MainBloc, MainState>(
              listener: (context, state) {
                if (state is DealsListFailed) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Failed to load deals')),
                  );
                }
              },
              builder: (context, state) {
                if (state is DealsListLoading) {
                  return Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(_accentColor),
                    ),
                  );
                } else if (state is DealsListSuccess) {
                  final deals = Initializer.dealslistmodel.data?.deals ?? [];
                  if (deals.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: size.width * 0.3, // 30% of screen width
                            height: size.width * 0.3,
                            child: Lottie.asset(
                              'assets/noorderes.json',
                              fit: BoxFit.contain,
                            ),
                          ),
                          SizedBox(height: padding * 0.5),
                          Text(
                            "No Hot Deals!",
                            style: TextStyle(
                              fontSize: baseTextSize * 1.2,
                              fontWeight: FontWeight.w700,
                              color: _primaryTextColor,
                              fontFamily: 'Inter',
                            ),
                            textAlign: TextAlign.center,
                          ),
                          SizedBox(height: padding * 0.25),
                          Text(
                            "There are currently no hot deals available.\nPlease check back later!",
                            style: TextStyle(
                              fontSize: baseTextSize * 0.9,
                              color: _secondaryTextColor,
                              fontFamily: 'Inter',
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    );
                  }
                  return _buildDealsGrid(deals, padding);
                } else {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'No deals loaded',
                          style: TextStyle(
                            fontSize: baseTextSize * 1.2,
                            color: _secondaryTextColor,
                          ),
                        ),
                        SizedBox(height: padding),
                        ElevatedButton(
                          onPressed: () {
                            if (_currentLatitude != null &&
                                _currentLongitude != null) {
                              context.read<MainBloc>().add(
                                    DealsListEvent(
                                      page: _currentPage,
                                      limit: 10,
                                      lat: _currentLatitude!,
                                      lon: _currentLongitude!,
                                    ),
                                  );
                            }
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: _accentColor,
                            padding: EdgeInsets.symmetric(
                              horizontal: padding * 1.5,
                              vertical: padding * 0.75,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius:
                                  BorderRadius.circular(padding * 0.5),
                            ),
                          ),
                          child: Text(
                            'Refresh Deals',
                            style: TextStyle(
                              fontSize: baseTextSize,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }
              },
            ),
    );
  }

  Widget _buildDealsGrid(List<Deals> deals, double padding) {
    final size = MediaQuery.of(context).size;
    // Adjust crossAxisCount based on screen width
    final crossAxisCount =
        size.width > 600 ? 3 : 2; // 3 columns for larger screens
    final childAspectRatio = size.width > 600 ? 0.8 : 0.75;

    return RefreshIndicator(
      onRefresh: () async {
        if (_currentLatitude != null && _currentLongitude != null) {
          context.read<MainBloc>().add(
                DealsListEvent(
                  page: _currentPage,
                  limit: 10,
                  lat: _currentLatitude!,
                  lon: _currentLongitude!,
                ),
              );
        }
      },
      child: GridView.builder(
        controller: _scrollController,
        padding: EdgeInsets.all(padding),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
          childAspectRatio: childAspectRatio * 1.1,
          crossAxisSpacing: padding,
          mainAxisSpacing: padding,
        ),
        itemCount: deals.length,
        itemBuilder: (context, index) {
          final deal = deals[index];
          return _buildDealCard(deal, padding);
        },
      ),
    );
  }

  Widget _buildDealCard(Deals deal, double padding) {
    final size = MediaQuery.of(context).size;
    final baseTextSize = _getResponsiveSize(context, 0.04);
    final cardWidth = size.width / (size.width > 600 ? 3.5 : 2.5);

    return GestureDetector(
      onTap: () {
        // Handle deal tap
      },
      child: Container(
        width: cardWidth,
        decoration: BoxDecoration(
          color: _cardColor,
          borderRadius: BorderRadius.circular(padding * 0.5),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: padding * 0.5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(padding * 0.5),
                topRight: Radius.circular(padding * 0.5),
              ),
              child: Image.network(
                deal.chef != null && deal.chef!.profile?.coverPhoto != null
                    ? ServerHelper.imageUrl + deal.chef!.profile!.coverPhoto!
                    : 'https://via.placeholder.com/180x100.png?text=Deal',
                height:
                    cardWidth * 0.6, // Image height proportional to card width
                width: double.infinity,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Container(
                  height: cardWidth * 0.6,
                  width: double.infinity,
                  color: Colors.grey[300],
                  child: const Icon(Icons.broken_image, size: 40),
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(
                  left: padding * 0.5,
                  right: padding * 0.5,
                  top: padding * 0.5,
                  bottom: 0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: padding * 0.3),
                  Text(
                    deal.title ?? 'Deal Title',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: baseTextSize * 1.1,
                      height: 1.2,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: padding * 0.5),
                  Row(
                    children: [
                      CircleAvatar(
                        radius: baseTextSize * 0.6,
                        backgroundImage: NetworkImage(
                          deal.chef?.profile?.profilePhoto != null
                              ? ServerHelper.imageUrl +
                                  deal.chef!.profile!.profilePhoto!
                              : 'https://via.placeholder.com/40x40.png?text=Chef',
                        ),
                      ),
                      SizedBox(width: padding * 0.3),
                      Expanded(
                        child: Text(
                          '${(deal.chef?.firstName ?? '').isNotEmpty ? deal.chef!.firstName![0].toUpperCase() + (deal.chef!.firstName!.length > 1 ? deal.chef!.firstName!.substring(1) : '') : ''} ${(deal.chef?.lastName ?? '').isNotEmpty ? deal.chef!.lastName![0].toUpperCase() + (deal.chef!.lastName!.length > 1 ? deal.chef!.lastName!.substring(1) : '') : ''}',
                          style: TextStyle(
                            fontSize: baseTextSize * 0.8,
                            fontWeight: FontWeight.w300,
                            color: _primaryTextColor,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: padding * 0.8),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'View Deal',
                        style: TextStyle(
                          fontSize: baseTextSize * 0.8,
                          fontWeight: FontWeight.w600,
                          color: _secondaryTextColor,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
