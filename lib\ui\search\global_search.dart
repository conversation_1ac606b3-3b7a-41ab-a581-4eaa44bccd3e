import 'dart:async';

import 'package:db_eats/bloc/home_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/data/models/globalsearch/searchdishesmodel.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/ui/catering/dishdetail.dart';
import 'package:db_eats/ui/chef/view_chef2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shimmer/shimmer.dart';

class GlobalSearchScreen extends StatefulWidget {
  const GlobalSearchScreen({super.key});

  @override
  State<GlobalSearchScreen> createState() => _GlobalSearchScreenState();
}

class _GlobalSearchScreenState extends State<GlobalSearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;
  bool _isLoadingRecentSearches = true;
  int _selectedTabIndex = 0;
  final List<String> _tabs = ['All', 'Dishes', 'Chefs'];
  Timer? _debounce;
  bool _showSearchResults = false;

  // Separate data storage for dishes and chefs
  dynamic _dishesSearchData;
  dynamic _chefsSearchData;
  bool _dishesLoaded = false;
  bool _chefsLoaded = false;

  @override
  void initState() {
    super.initState();
    context.read<HomeBloc>().add(
          GetRecentPopularSearchEvent(
            data: {
              'page': 1,
              'limit': 10,
              'search_type': 1,
            },
          ),
        );
  }

  @override
  void dispose() {
    _searchController.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  void _onSearchChanged(String query) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();

    setState(() {});

    _debounce = Timer(const Duration(milliseconds: 300), () {
      if (query.isEmpty) {
        setState(() {
          _isSearching = false;
          _isLoadingRecentSearches = true;
          _showSearchResults = false;
          _dishesSearchData = null;
          _chefsSearchData = null;
          _dishesLoaded = false;
          _chefsLoaded = false;
        });
        context.read<HomeBloc>().add(
              GetRecentPopularSearchEvent(
                data: {
                  'page': 1,
                  'limit': 10,
                  'search_type': _getSearchTypeForCurrentTab(),
                },
              ),
            );
      }
    });
  }

  void _performSearch(String query) {
    print(
        '_performSearch called with query: "$query", selectedTabIndex: $_selectedTabIndex');

    setState(() {
      _isSearching = true;
      _dishesLoaded = false;
      _chefsLoaded = false;
      _dishesSearchData = null;
      _chefsSearchData = null;
      _showSearchResults = false;
    });

    final initializer = Initializer();

    // Always call both events for all tabs to get complete data

    // 1. Call SearchDishesEvent for dishes data
    final dishesSearchData = <String, dynamic>{
      'search_keyword': query,
    };

    if (initializer.getLatitude != null && initializer.getLongitude != null) {
      dishesSearchData['latitude'] = initializer.getLatitude.toString();
      dishesSearchData['longitude'] = initializer.getLongitude.toString();
    }

    print('Dishes search data: $dishesSearchData');
    context.read<HomeBloc>().add(SearchDishesEvent(data: dishesSearchData));

    // 2. Call GetHomeDataEvent for chefs data
    final chefsSearchData = <String, dynamic>{
      'search': query,
      'page': 1,
      'limit': 20,
      'type': 'chefs',
      'search_type': 2, // 2 - Chefs
    };

    if (initializer.getLatitude != null && initializer.getLongitude != null) {
      chefsSearchData['latitude'] = initializer.getLatitude;
      chefsSearchData['longitude'] = initializer.getLongitude;
    }

    chefsSearchData['search_keyword'] = query;

    print('Chefs search data: $chefsSearchData');
    context.read<HomeBloc>().add(GetHomeDataEvent(data: chefsSearchData));
  }

  void _onSearchItemTap(String searchTerm) {
    _searchController.text = searchTerm;
    _performSearch(searchTerm);
  }

  void _loadRecentPopularSearchesForTab() {
    setState(() {
      _isLoadingRecentSearches = true;
    });

    int searchType = _getSearchTypeForCurrentTab();

    print(
        'Loading recent/popular searches for tab: $_selectedTabIndex, search_type: $searchType');

    context.read<HomeBloc>().add(
          GetRecentPopularSearchEvent(
            data: {
              'page': 1,
              'limit': 10,
              'search_type': searchType,
            },
          ),
        );
  }

  int _getSearchTypeForCurrentTab() {
    switch (_selectedTabIndex) {
      case 1: // Dishes
        return 3; // 3 - Dishes
      case 2: // Chefs
        return 2; // 2 - Chefs
      default: // All
        return 1; // 1 - All
    }
  }

  bool _isPanning = false;

  void _handlePanStart(DragStartDetails details) {
    _isPanning = true;
  }

  void _handlePanEnd(DragEndDetails details) {
    if (!_isPanning) return;
    _isPanning = false;

    final double velocity = details.primaryVelocity ?? 0;

    if (velocity.abs() > 300) {
      if (velocity < 0) {
        if (_selectedTabIndex < _tabs.length - 1) {
          setState(() {
            _selectedTabIndex++;
          });
          print(
              'Tab switched to: $_selectedTabIndex (${_tabs[_selectedTabIndex]})');

          _loadRecentPopularSearchesForTab();

          if (_searchController.text.isNotEmpty) {
            _performSearch(_searchController.text);
          } else {}
        }
      } else {
        if (_selectedTabIndex > 0) {
          setState(() {
            _selectedTabIndex--;
          });
          print(
              'Tab switched to: $_selectedTabIndex (${_tabs[_selectedTabIndex]})');

          _loadRecentPopularSearchesForTab();

          if (_searchController.text.isNotEmpty) {
            _performSearch(_searchController.text);
          } else {
            print(
                'Pan swipe: No search text entered - skipping search API call');
          }
        }
      }
    }
  }

  void _handleSwipe(DragEndDetails details) {
    if (details.primaryVelocity! < -500) {
      if (_selectedTabIndex < _tabs.length - 1) {
        setState(() {
          _selectedTabIndex++;
        });
        print(
            'Tab switched to: $_selectedTabIndex (${_tabs[_selectedTabIndex]})');

        _loadRecentPopularSearchesForTab();

        if (_searchController.text.isNotEmpty) {
          print('Performing search with new tab filter');
          _performSearch(_searchController.text);
        }
      }
    } else if (details.primaryVelocity! > 500) {
      if (_selectedTabIndex > 0) {
        setState(() {
          _selectedTabIndex--;
        });
        print(
            'Tab switched to: $_selectedTabIndex (${_tabs[_selectedTabIndex]})');

        _loadRecentPopularSearchesForTab();

        if (_searchController.text.isNotEmpty) {
          _performSearch(_searchController.text);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    return GestureDetector(
      onHorizontalDragEnd: _handleSwipe,
      onPanStart: _handlePanStart,
      onPanEnd: _handlePanEnd,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          backgroundColor: const Color(0xFFF6F3EC),
          elevation: 0,
          scrolledUnderElevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.black),
            onPressed: () => Navigator.pop(context),
          ),
          title: const Text(
            'Search',
            style: TextStyle(
              color: Colors.black,
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        body: Column(
          children: [
            // Search Bar
            Padding(
              padding: EdgeInsets.only(
                  left: screenWidth * 0.04,
                  right: screenWidth * 0.04,
                  top: screenWidth * 0.01,
                  bottom: screenWidth * 0.01),
              child: Container(
                child: TextField(
                  controller: _searchController,
                  onChanged: _onSearchChanged,
                  onSubmitted: (query) {
                    if (query.isNotEmpty) {
                      _performSearch(query);
                    }
                  },
                  textInputAction: TextInputAction.search,
                  decoration: InputDecoration(
                    hintText: 'Search for dishes, chefs...',
                    hintStyle: TextStyle(
                      color: Colors.grey[600],
                      fontSize: screenWidth * 0.04,
                    ),
                    prefixIcon: Icon(
                      Icons.search,
                      color: Colors.grey[600],
                      size: screenWidth * 0.05,
                    ),
                    suffixIcon: _searchController.text.isNotEmpty
                        ? IconButton(
                            icon: Image.asset(
                              'assets/icons/close_2.png',
                              width: screenWidth * 0.05,
                              height: screenWidth * 0.05,
                            ),
                            onPressed: () {
                              _searchController.clear();
                              _onSearchChanged('');
                            },
                          )
                        : null,
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: screenWidth * 0.04,
                      vertical: screenWidth * 0.03,
                    ),
                  ),
                ),
              ),
            ),
            Divider(height: 1, color: Colors.grey[300]), // Search Tabs

            Padding(
              padding: EdgeInsets.only(
                  top: screenWidth * 0.035,
                  bottom: screenWidth * 0.015,
                  left: screenWidth * 0.02,
                  right: screenWidth * 0.02),
              child: _buildSearchTabs(screenWidth),
            ),

            Expanded(
              child: BlocConsumer<HomeBloc, HomeState>(
                listener: (context, state) {
                  if (state is RecentPopularSearchSuccess) {
                    setState(() {
                      _isSearching = false;
                      _isLoadingRecentSearches = false;
                    });
                  } else if (state is HomeDataSuccess) {
                    setState(() {
                      _chefsSearchData = state.data;
                      _chefsLoaded = true;

                      // Check if both searches are complete
                      if (_dishesLoaded && _chefsLoaded) {
                        _isSearching = false;
                        _showSearchResults = true;
                      }
                    });
                  } else if (state is SearchDishesSuccess) {
                    setState(() {
                      _dishesSearchData = state.data;
                      _dishesLoaded = true;

                      // Check if both searches are complete
                      if (_dishesLoaded && _chefsLoaded) {
                        _isSearching = false;
                        _showSearchResults = true;
                      }
                    });
                  } else if (state is HomeDataFailed) {
                    setState(() {
                      _isSearching = false;
                      _isLoadingRecentSearches = false;
                      _showSearchResults = false;
                      _chefsLoaded = true; // Mark as completed even if failed

                      // Check if both searches are complete
                      if (_dishesLoaded && _chefsLoaded) {
                        _showSearchResults = true;
                      }
                    });
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text(state.message)),
                    );
                  } else if (state is SearchDishesFailed) {
                    setState(() {
                      _isSearching = false;
                      _isLoadingRecentSearches = false;
                      _showSearchResults = false;
                      _dishesLoaded = true; // Mark as completed even if failed

                      // Check if both searches are complete
                      if (_dishesLoaded && _chefsLoaded) {
                        _showSearchResults = true;
                      }
                    });
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text(state.message)),
                    );
                  }
                },
                builder: (context, state) {
                  if (_isSearching) {
                    return _buildLoadingWidget();
                  }

                  if (_showSearchResults &&
                      (_dishesSearchData != null || _chefsSearchData != null)) {
                    return _buildSearchResults(context, screenWidth);
                  }

                  if (_searchController.text.isEmpty) {
                    if (_isLoadingRecentSearches) {
                      return _buildShimmerLoading(screenWidth);
                    }
                    return _buildRecentPopularSearches(
                        context, screenWidth, state);
                  }

                  // If we have search text but no results yet, show loading
                  if (_searchController.text.isNotEmpty &&
                      !_showSearchResults) {
                    return _buildLoadingWidget();
                  }

                  return _buildSearchInstructionState(screenWidth);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchTabs(double screenWidth) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
      child: Row(
        children: _tabs.asMap().entries.map((entry) {
          final index = entry.key;
          final title = entry.value;
          final isActive = index == _selectedTabIndex;

          return Padding(
            padding: EdgeInsets.only(right: screenWidth * 0.04),
            child: GestureDetector(
              onTap: () {
                setState(() => _selectedTabIndex = index);

                _loadRecentPopularSearchesForTab();

                if (_searchController.text.isNotEmpty) {
                  _performSearch(_searchController.text);
                }
              },
              child: SizedBox(
                width: screenWidth * 0.1,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        color: isActive ? Colors.black : Colors.grey[600],
                        fontSize: twelve,
                        fontWeight:
                            isActive ? FontWeight.w600 : FontWeight.w400,
                      ),
                    ),
                    Container(
                      height: 2,
                      width: double.infinity,
                      color: isActive ? Color(0xffFFBE16) : Colors.transparent,
                    ),
                  ],
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildRecentPopularSearches(
      BuildContext context, double screenWidth, HomeState state) {
    if (state is RecentPopularSearchSuccess) {
      final data = state.data.data;
      final recentSearches = data?.data ?? [];
      final popularSearches = data?.popularSearches ?? [];

      return SingleChildScrollView(
        padding: EdgeInsets.all(screenWidth * 0.04),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (recentSearches.isNotEmpty) ...[
              _buildSectionHeader('Recent', screenWidth, icon: Icons.history),
              SizedBox(height: screenWidth * 0.02),
              ...recentSearches.take(3).map((search) => _buildSearchItem(
                    context,
                    screenWidth,
                    search.searchQuery ?? '',
                    Icons.history,
                  )),
              SizedBox(height: screenWidth * 0.025),
            ],
            if (popularSearches.isNotEmpty) ...[
              _buildSectionHeader('Popular searches', screenWidth),
              SizedBox(height: screenWidth * 0.03),
              _buildPopularSearchTags(context, screenWidth, popularSearches),
            ],
          ],
        ),
      );
    }

    return _buildEmptyState(screenWidth);
  }

  Widget _buildSectionHeader(String title, double screenWidth,
      {IconData? icon}) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              size: twentyFour,
              color: Color(0xff1F2122),
            ),
            SizedBox(width: screenWidth * 0.02),
          ],
          Text(
            title,
            style: TextStyle(
              fontSize: screenWidth * 0.045,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchItem(
      BuildContext context, double screenWidth, String title, IconData icon) {
    return InkWell(
      onTap: () => _onSearchItemTap(title),
      child: Padding(
        padding: EdgeInsets.symmetric(
            vertical: screenWidth * 0.025, horizontal: screenWidth * 0.05),
        child: Row(
          children: [
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  fontSize: screenWidth * 0.04,
                  color: Colors.black87,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPopularSearchTags(
      BuildContext context, double screenWidth, List<dynamic> popularSearches) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
      child: Wrap(
        spacing: screenWidth * 0.02,
        runSpacing: screenWidth * 0.02,
        children: popularSearches.map((search) {
          final searchTerm = search.searchQuery ?? search.toString();
          return _buildSearchTag(context, screenWidth, searchTerm);
        }).toList(),
      ),
    );
  }

  Widget _buildSearchTag(
      BuildContext context, double screenWidth, String title) {
    return InkWell(
      onTap: () => _onSearchItemTap(title),
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: screenWidth * 0.03,
          vertical: screenWidth * 0.005,
        ),
        decoration: BoxDecoration(
          color: Color(0xffe1e3e6),
          borderRadius: BorderRadius.circular(screenWidth * 0.04),
          border: Border.all(color: Color(0xffe1e3e6)),
        ),
        child: Text(
          title,
          style: TextStyle(
            fontSize: screenWidth * 0.035,
            color: Color(0xff1F2122),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }

  Widget _buildEmptyState(double screenWidth) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search,
            size: screenWidth * 0.15,
            color: Colors.grey[400],
          ),
          SizedBox(height: screenWidth * 0.04),
          Text(
            'Start typing to search',
            style: TextStyle(
              fontSize: screenWidth * 0.04,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchInstructionState(double screenWidth) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search,
            size: screenWidth * 0.15,
            color: Colors.grey[400],
          ),
          SizedBox(height: screenWidth * 0.04),
          Text(
            'Press Enter to search',
            style: TextStyle(
              fontSize: screenWidth * 0.04,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: screenWidth * 0.02),
          Text(
            'Results will be displayed here',
            style: TextStyle(
              fontSize: screenWidth * 0.035,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildShimmerLoading(double screenWidth) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(screenWidth * 0.04),
      child: Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildShimmerItem(screenWidth),
            SizedBox(height: screenWidth * 0.02),
            ...List.generate(3, (index) => _buildShimmerItem(screenWidth)),
            SizedBox(height: screenWidth * 0.04),
            _buildShimmerItem(screenWidth),
            SizedBox(height: screenWidth * 0.02),
            Wrap(
              spacing: screenWidth * 0.02,
              runSpacing: screenWidth * 0.02,
              children:
                  List.generate(4, (index) => _buildShimmerTag(screenWidth)),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShimmerItem(double screenWidth) {
    return Container(
      height: screenWidth * 0.12,
      margin: EdgeInsets.only(bottom: screenWidth * 0.015),
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(screenWidth * 0.02),
      ),
    );
  }

  Widget _buildShimmerTag(double screenWidth) {
    return Container(
      height: screenWidth * 0.08,
      width: screenWidth * 0.2,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(screenWidth * 0.04),
      ),
    );
  }

  Widget _buildSearchResults(BuildContext context, double screenWidth) {
    if (_dishesSearchData == null && _chefsSearchData == null) {
      return _buildEmptySearchResults(screenWidth);
    }

    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Expanded(
          child: SingleChildScrollView(
              child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // All tab - show both dishes and chefs
          if (_selectedTabIndex == 0) ...[
            // Show dishes section if available
            if (_dishesSearchData != null &&
                _dishesSearchData is SearchDishesModel) ...[
              _buildDishesSection(context, screenWidth),
              SizedBox(height: screenHeight * 0.03),
            ],
            // Show chefs section if available
            if (_chefsSearchData != null) ...[
              _buildChefsSection(context, screenWidth),
            ],
            // If no results in either category
            if ((_dishesSearchData == null ||
                    (_dishesSearchData is SearchDishesModel &&
                        (_dishesSearchData as SearchDishesModel)
                                .data
                                ?.dishes
                                ?.isEmpty ==
                            true)) &&
                (_chefsSearchData == null ||
                    _chefsSearchData.data?.topRatedChefs?.isEmpty == true))
              _buildEmptySearchResults(screenWidth),
          ],
          // Dishes tab - show only dishes
          if (_selectedTabIndex == 1) ...[
            if (_dishesSearchData != null &&
                _dishesSearchData is SearchDishesModel) ...[
              _buildDishesSection(context, screenWidth),
            ] else ...[
              _buildEmptySearchResults(screenWidth),
            ],
          ],
          // Chefs tab - show only chefs
          if (_selectedTabIndex == 2) ...[
            if (_chefsSearchData != null) ...[
              _buildChefsSection(context, screenWidth),
            ] else ...[
              _buildEmptySearchResults(screenWidth),
            ],
          ],
        ],
      ))),
    ]);
  }

  Widget _buildDishesSection(BuildContext context, double screenWidth) {
    final dishesData = _dishesSearchData as SearchDishesModel;
    final dishes = dishesData.data?.dishes ?? [];

    if (dishes.isEmpty) {
      return Container(); // Return empty container instead of empty state
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // _buildSectionHeader('Dishes', screenWidth),
        SizedBox(height: screenHeight * 0.02),
        _buildDishesList(context, screenWidth, dishes),
        SizedBox(height: screenHeight * 0.02),
      ],
    );
  }

  Widget _buildChefsSection(BuildContext context, double screenWidth) {
    final data = _chefsSearchData.data;
    if (data == null) {
      return Container(); // Return empty container instead of empty state
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (_selectedTabIndex == 2) ...[
          // Chefs tab - vertical list layout with unique chefs only
          _buildUniqueChefsList(context, screenWidth, screenHeight, data),
        ] else ...[
          // All tab - horizontal list layout
          if (data.topRatedChefs != null && data.topRatedChefs!.isNotEmpty) ...[
            _buildSectionHeader('Popular Chefs', screenWidth),
            SizedBox(height: screenHeight * 0.02),
            _buildChefCategoryList(
              context,
              screenWidth,
              screenHeight,
              data.topRatedChefs!,
            ),
            SizedBox(height: screenHeight * 0.02),
          ],
        ],
      ],
    );
  }

  Widget _buildChefCategoryList(BuildContext context, double screenWidth,
      double screenHeight, List<dynamic> chefs) {
    return SizedBox(
      height: ten * 26,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
        itemCount: chefs.length,
        separatorBuilder: (_, __) => SizedBox(width: screenWidth * 0.03),
        itemBuilder: (_, index) {
          final chef = chefs[index];
          final name =
              '${chef.chef?.firstName ?? ''} ${chef.chef?.lastName ?? ''}';
          final availability = chef.chef?.operationDays
                  ?.map((day) => day.day?.name?.substring(0, 1) ?? '')
                  .join(', ') ??
              '';
          final cuisines = chef.searchTags?.join(', ') ?? 'Various Cuisines';
          final distance = _formatDistance(chef.distance);
          final rating = '${chef.ratingPercentage}% (${chef.totalRatings})';
          final prepTime = '30-45 mins';

          return _buildChefCard(
            context: context,
            screenWidth: screenWidth,
            screenHeight: screenHeight,
            id: chef.chefId ?? 0,
            image: ServerHelper.imageUrl + (chef.profilePhoto ?? ''),
            name: name,
            cuisines: cuisines,
            rating: rating,
            distance: distance,
            availability: availability,
            dishImage: ServerHelper.imageUrl + (chef.coverPhoto ?? ''),
            prepTime: prepTime,
          );
        },
      ),
    );
  }

  Widget _buildChefVerticalList(BuildContext context, double screenWidth,
      double screenHeight, List<dynamic> chefs) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
      child: Column(
        children: chefs.map((chef) {
          final name =
              '${chef.chef?.firstName ?? ''} ${chef.chef?.lastName ?? ''}';
          final availability = chef.chef?.operationDays
                  ?.map((day) => day.day?.name?.substring(0, 1) ?? '')
                  .join(', ') ??
              '';
          final cuisines = chef.searchTags?.join(', ') ?? 'Various Cuisines';
          final distance = _formatDistance(chef.distance);
          final rating = '${chef.ratingPercentage}% (${chef.totalRatings})';
          final prepTime = '30-45 mins';

          return _buildChefVerticalCard(
            context: context,
            screenWidth: screenWidth,
            screenHeight: screenHeight,
            id: chef.chefId ?? 0,
            image: ServerHelper.imageUrl + (chef.profilePhoto ?? ''),
            name: name,
            cuisines: cuisines,
            rating: rating,
            distance: distance,
            availability: availability,
            dishImage: ServerHelper.imageUrl + (chef.coverPhoto ?? ''),
            prepTime: prepTime,
          );
        }).toList(),
      ),
    );
  }

  Widget _buildDishesList(
      BuildContext context, double screenWidth, List<Dishes> dishes) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
      child: ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: dishes.length,
        itemBuilder: (context, index) {
          final dish = dishes[index];
          final minPrice = dish.servingSizePrices?.isNotEmpty == true
              ? dish.servingSizePrices!
                  .map((price) => double.tryParse(price.price ?? '0') ?? 0)
                  .reduce((a, b) => a < b ? a : b)
              : 0.0;

          return _buildDishCard(
            context: context,
            screenWidth: screenWidth,
            id: dish.id ?? 0,
            image: ServerHelper.imageUrl + (dish.photo ?? ''),
            name: dish.name ?? 'Unknown Dish',
            price: '\$${minPrice.toStringAsFixed(2)}',
            servings: dish.servingSizePrices?.first.servingSize?.serves ?? 1,
            rating: 0.0,
            ratingCount: 0,
          );
        },
      ),
    );
  }

  Widget _buildDishCard({
    required BuildContext context,
    required double screenWidth,
    required int id,
    required String image,
    required String name,
    required String price,
    required int servings,
    required double rating,
    required int ratingCount,
  }) {
    final screenHeight = MediaQuery.of(context).size.height;
    final isLargeScreen = screenWidth > 600;

    return GestureDetector(
      onTap: () {
        // Navigate to dish details page
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => DishDetailPage(
              dishId: id.toString(),
              chefId: 0, // We don't have chef ID in search results
            ),
          ),
        );
      },
      child: Container(
        margin: EdgeInsets.symmetric(
          horizontal: screenWidth * 0.02,
          vertical: screenWidth * 0.010,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: const Color(0xFFE1E3E6), width: 0.5),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Dish image
            Expanded(
              flex: 4,
              child: ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  bottomLeft: Radius.circular(12),
                ),
                child: Image.network(
                  image,
                  fit: BoxFit.cover,
                  height: isLargeScreen ? 180 : screenHeight * 0.165,
                  loadingBuilder: (context, child, loadingProgress) {
                    if (loadingProgress == null) return child;
                    return Shimmer.fromColors(
                      baseColor: Colors.grey[300]!,
                      highlightColor: Colors.grey[100]!,
                      child: Container(
                        height: isLargeScreen ? 180 : screenHeight * 0.165,
                        color: Colors.white,
                      ),
                    );
                  },
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      height: isLargeScreen ? 180 : screenHeight * 0.165,
                      color: Colors.grey[200],
                      child: Icon(
                        Icons.restaurant,
                        size: isLargeScreen ? 48 : screenWidth * 0.1,
                        color: Colors.grey,
                      ),
                    );
                  },
                ),
              ),
            ),
            // Content
            Expanded(
              flex: 6,
              child: Padding(
                padding: EdgeInsets.only(
                  left: screenWidth * 0.035,
                  right: screenWidth * 0.035,
                  top: screenHeight * 0.015,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Dish name
                    Text(
                      name,
                      style: TextStyle(
                        fontFamily: 'Inter-medium',
                        fontWeight: FontWeight.w600,
                        fontSize: isLargeScreen ? 20 : screenWidth * 0.04,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: screenHeight * 0.009),
                    SizedBox(height: screenHeight * 0.01),

                    // Servings and rating in a row
                    Row(
                      children: [
                        // Servings
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: screenWidth * 0.015,
                            vertical: screenHeight * 0.002,
                          ),
                          decoration: BoxDecoration(
                            color: const Color.fromRGBO(225, 227, 230, 1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            '$servings Servings',
                            style: TextStyle(
                              fontSize: isLargeScreen ? 14 : screenWidth * 0.03,
                              fontWeight: FontWeight.w500,
                              fontFamily: 'Inter',
                              letterSpacing: 0.2,
                            ),
                          ),
                        ),
                        SizedBox(width: screenWidth * 0.02),
                        // Rating
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: screenWidth * 0.015,
                            vertical: screenHeight * 0.002,
                          ),
                          decoration: BoxDecoration(
                            color: const Color.fromRGBO(225, 227, 230, 1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Image.asset(
                                'assets/icons/thump.png',
                                width: isLargeScreen ? 24 : screenWidth * 0.03,
                                height: isLargeScreen ? 24 : screenWidth * 0.03,
                                color: Colors.black54,
                              ),
                              SizedBox(width: screenWidth * 0.01),
                              Text(
                                '$rating%',
                                style: TextStyle(
                                  fontSize:
                                      isLargeScreen ? 14 : screenWidth * 0.03,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: 'Inter',
                                ),
                              ),
                              SizedBox(width: screenWidth * 0.005),
                              Text(
                                '($ratingCount)',
                                style: TextStyle(
                                  fontSize:
                                      isLargeScreen ? 14 : screenWidth * 0.03,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: 'Inter',
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: screenHeight * 0.009),

                    SizedBox(height: screenHeight * 0.01),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          price,
                          style: TextStyle(
                            fontSize: isLargeScreen ? 18 : screenWidth * 0.035,
                            fontFamily: 'Inter-medium',
                            fontWeight: FontWeight.w500,
                            color: const Color(0xFF1F2122),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDistance(double? distance) {
    if (distance == null) return 'Unknown';
    // Convert meters to kilometers
    final kilometers = distance / 1000;
    // Format to one decimal place
    return '${kilometers.toStringAsFixed(1)} KM';
  }

  Widget _buildChefCard({
    required BuildContext context,
    required double screenWidth,
    required double screenHeight,
    required String image,
    required String name,
    required String cuisines,
    required String rating,
    required String distance,
    required String availability,
    required String dishImage,
    required String prepTime,
    required int id,
  }) {
    return GestureDetector(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ViewChef2(
                id: id,
                title: name,
                latitude: 0,
                longitude: 0,
                distance: distance,
              ),
            ),
          );
        },
        child: Container(
          width: ten * 23,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(screenWidth * 0.03),
            // boxShadow: [
            //   BoxShadow(
            //     color: Colors.black.withOpacity(0.01),
            //     blurRadius: 10,
            //     offset: const Offset(0, 2),
            //   ),
            // ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Stack(
                clipBehavior: Clip.none,
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(screenWidth * 0.03),
                      topRight: Radius.circular(screenWidth * 0.03),
                    ),
                    child: Image.network(
                      dishImage,
                      width: double.infinity,
                      height: ten * 12,
                      fit: BoxFit.cover,
                      loadingBuilder: (context, child, loadingProgress) {
                        if (loadingProgress == null) return child;
                        return Shimmer.fromColors(
                          baseColor: Colors.grey[300]!,
                          highlightColor: Colors.grey[100]!,
                          child: Container(
                            width: double.infinity,
                            height: ten * 12,
                            color: Colors.white,
                          ),
                        );
                      },
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          width: double.infinity,
                          height: ten * 12,
                          color: Colors.grey[300],
                          child: Icon(Icons.image_not_supported,
                              color: Colors.grey[600]),
                        );
                      },
                    ),
                  ),
                  Positioned(
                    top: screenWidth * 0.04,
                    left: screenWidth * 0.04,
                    child: Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: screenWidth * 0.02,
                            vertical: screenWidth * 0.01),
                        decoration: BoxDecoration(
                          color: const Color.fromARGB(0, 0, 0, 0),
                          borderRadius:
                              BorderRadius.circular(screenWidth * 0.03),
                        ),
                        child: SizedBox.shrink()),
                  ),
                  Positioned(
                    left: screenWidth * 0.04,
                    bottom: -screenWidth * 0.05,
                    child: Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 3),
                      ),
                      child: CircleAvatar(
                        radius: ten + twelve,
                        backgroundImage: NetworkImage(image),
                        onBackgroundImageError: (exception, stackTrace) {
                          print('Error loading chef image: $exception');
                        },
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: screenHeight * 0.03),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      name,
                      style: TextStyle(
                        fontSize: forteen,
                        fontWeight: FontWeight.w500,
                        fontFamily: 'Inter',
                      ),
                    ),
                    SizedBox(height: screenHeight * 0.008),
                    Text(
                      cuisines,
                      style: TextStyle(
                        fontSize: twelve,
                        fontWeight: FontWeight.w400,
                        fontFamily: 'Inter',
                        color: const Color.fromRGBO(65, 67, 70, 1),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: screenHeight * 0.01),
                    Row(
                      children: [
                        Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: screenWidth * 0.015,
                              vertical: screenWidth * 0.005),
                          decoration: BoxDecoration(
                            color: const Color.fromRGBO(225, 227, 230, 1),
                            borderRadius:
                                BorderRadius.circular(screenWidth * 0.03),
                          ),
                          child: Row(
                            children: [
                              Image.asset(
                                'assets/icons/thump.png',
                                width: screenWidth * 0.0275,
                                height: screenWidth * 0.025,
                                color: Color(0xff1F2122),
                              ),
                              SizedBox(width: screenWidth * 0.01),
                              Text(
                                rating,
                                style: TextStyle(
                                  fontSize: ten,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: 'Inter',
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(width: screenWidth * 0.02),
                        Row(
                          children: [
                            Icon(Icons.location_on_outlined,
                                size: screenWidth * 0.03,
                                color: const Color(0xFF1F2122)),
                            SizedBox(width: screenWidth * 0.005),
                            Text(
                              distance,
                              style: TextStyle(
                                fontSize: ten,
                                fontWeight: FontWeight.w400,
                                fontFamily: 'Inter',
                                color: const Color(0xFF1F2122),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    SizedBox(height: screenHeight * 0.008),
                    Row(
                      children: [
                        Image.asset(
                          'assets/icons/calender_2.png',
                          width: screenWidth * 0.03,
                          height: screenWidth * 0.0325,
                          color: Colors.black54,
                        ),
                        SizedBox(width: screenWidth * 0.01),
                        Text(
                          availability,
                          style: TextStyle(
                            fontSize: twelve,
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w400,
                            color: Color(0xff414346),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ));
  }

  Widget _buildChefVerticalCard({
    required BuildContext context,
    required double screenWidth,
    required double screenHeight,
    required String image,
    required String name,
    required String cuisines,
    required String rating,
    required String distance,
    required String availability,
    required String dishImage,
    required String prepTime,
    required int id,
  }) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ViewChef2(
              id: id,
              title: name,
              latitude: 0,
              longitude: 0,
              distance: distance,
            ),
          ),
        );
      },
      child: Container(
        width: double.infinity,
        margin: EdgeInsets.only(bottom: screenWidth * 0.03),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(screenWidth * 0.03),
          border: Border.all(color: const Color(0xFFE1E3E6), width: 0.3),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Stack(
              clipBehavior: Clip.none,
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(screenWidth * 0.03),
                    topRight: Radius.circular(screenWidth * 0.03),
                  ),
                  child: Image.network(
                    dishImage,
                    width: double.infinity,
                    height: screenWidth * 0.35,
                    fit: BoxFit.cover,
                    loadingBuilder: (context, child, loadingProgress) {
                      if (loadingProgress == null) return child;
                      return Shimmer.fromColors(
                        baseColor: Colors.grey[300]!,
                        highlightColor: Colors.grey[100]!,
                        child: Container(
                          width: double.infinity,
                          height: screenWidth * 0.45,
                          color: Colors.white,
                        ),
                      );
                    },
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: double.infinity,
                        height: screenWidth * 0.45,
                        color: Colors.grey[300],
                        child: Icon(Icons.image_not_supported,
                            color: Colors.grey[600]),
                      );
                    },
                  ),
                ),
                Positioned(
                  left: screenWidth * 0.03,
                  bottom: -screenWidth * 0.045,
                  child: Container(
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 3),
                    ),
                    child: CircleAvatar(
                      radius: screenWidth * 0.055,
                      backgroundImage: NetworkImage(image),
                      onBackgroundImageError: (exception, stackTrace) {
                        print('Error loading chef image: $exception');
                      },
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: screenWidth * 0.05),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    name,
                    style: TextStyle(
                      fontSize: forteen,
                      fontWeight: FontWeight.w500,
                      fontFamily: 'Inter',
                    ),
                  ),
                  SizedBox(height: screenHeight * 0.008),
                  Text(
                    cuisines,
                    style: TextStyle(
                      fontSize: twelve,
                      fontWeight: FontWeight.w400,
                      fontFamily: 'Inter',
                      color: const Color.fromRGBO(65, 67, 70, 1),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: screenHeight * 0.01),
                  Row(
                    children: [
                      Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: screenWidth * 0.015,
                            vertical: screenWidth * 0.005),
                        decoration: BoxDecoration(
                          color: const Color.fromRGBO(225, 227, 230, 1),
                          borderRadius:
                              BorderRadius.circular(screenWidth * 0.03),
                        ),
                        child: Row(
                          children: [
                            Image.asset(
                              'assets/icons/thump.png',
                              width: screenWidth * 0.0275,
                              height: screenWidth * 0.025,
                              color: Color(0xff1F2122),
                            ),
                            SizedBox(width: screenWidth * 0.01),
                            Text(
                              rating,
                              style: TextStyle(
                                fontSize: ten,
                                fontWeight: FontWeight.w500,
                                fontFamily: 'Inter',
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(width: screenWidth * 0.02),
                      Row(
                        children: [
                          Icon(Icons.location_on_outlined,
                              size: screenWidth * 0.03,
                              color: const Color(0xFF1F2122)),
                          SizedBox(width: screenWidth * 0.005),
                          Text(
                            distance,
                            style: TextStyle(
                              fontSize: ten,
                              fontWeight: FontWeight.w400,
                              fontFamily: 'Inter',
                              color: const Color(0xFF1F2122),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  SizedBox(height: screenHeight * 0.008),
                  Row(
                    children: [
                      Image.asset(
                        'assets/icons/calender_2.png',
                        width: screenWidth * 0.03,
                        height: screenWidth * 0.0325,
                        color: Colors.black54,
                      ),
                      SizedBox(width: screenWidth * 0.01),
                      Text(
                        availability,
                        style: TextStyle(
                          fontSize: twelve,
                          fontFamily: 'Inter',
                          fontWeight: FontWeight.w400,
                          color: Color(0xff414346),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: screenWidth * 0.04),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptySearchResults(double screenWidth) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: screenWidth * 0.15,
            color: Colors.grey[400],
          ),
          SizedBox(height: screenWidth * 0.04),
          Text(
            'No results found',
            style: TextStyle(
              fontSize: screenWidth * 0.045,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: screenWidth * 0.02),
          Text(
            'Try searching with different keywords',
            style: TextStyle(
              fontSize: screenWidth * 0.035,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: screenWidth * 0.04),
          TextButton(
            onPressed: () {
              setState(() {
                _showSearchResults = false;
                _dishesSearchData = null;
                _chefsSearchData = null;
                _dishesLoaded = false;
                _chefsLoaded = false;
                _searchController.clear();
                _isLoadingRecentSearches = true;
              });
              context.read<HomeBloc>().add(
                    GetRecentPopularSearchEvent(
                      data: {
                        'page': 1,
                        'limit': 10,
                        'search_type': _getSearchTypeForCurrentTab(),
                      },
                    ),
                  );
            },
            child: Text(
              'Go back',
              style: TextStyle(
                fontSize: screenWidth * 0.04,
                color: const Color(0xFFFFBE16),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUniqueChefsList(BuildContext context, double screenWidth,
      double screenHeight, dynamic data) {
    List<dynamic> allChefs = [];

    if (data.topRatedChefs != null) {
      allChefs.addAll(data.topRatedChefs!);
    }
    if (data.recommendedChefs != null) {
      allChefs.addAll(data.recommendedChefs!);
    }
    if (data.popularChefsNear != null) {
      allChefs.addAll(data.popularChefsNear!);
    }

    Map<int, dynamic> uniqueChefs = {};
    for (var chef in allChefs) {
      int chefId = chef.chefId ?? 0;
      if (!uniqueChefs.containsKey(chefId)) {
        uniqueChefs[chefId] = chef;
      }
    }

    List<dynamic> uniqueChefsList = uniqueChefs.values.toList();

    if (uniqueChefsList.isEmpty) {
      return _buildEmptySearchResults(screenWidth);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Popular Chefs', screenWidth),
        SizedBox(height: screenHeight * 0.02),
        _buildChefVerticalList(
            context, screenWidth, screenHeight, uniqueChefsList),
        SizedBox(height: screenHeight * 0.08),
      ],
    );
  }
}
