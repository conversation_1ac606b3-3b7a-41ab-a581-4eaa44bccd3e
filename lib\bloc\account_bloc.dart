import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/data/models/account/accountinfomodel.dart' as account;
import 'package:db_eats/data/models/account/listfavouritedishesmodel.dart';
import 'package:db_eats/data/models/cart/listcartmodel.dart';
import 'package:db_eats/data/models/cart/viewcartmodel.dart';
import 'package:db_eats/data/models/chef/listfavouritechefsmodel.dart';
import 'package:db_eats/data/models/data/wallet/wallettransactionlistmodel.dart';
import 'package:db_eats/data/models/data/wallet/walletwithdrawdepositmodel.dart';
import 'package:db_eats/data/models/guesthome/listaddressmodel.dart';
import 'package:db_eats/data/models/guesthome/listsearchedaddressmodel.dart';
import 'package:db_eats/data/models/guesthome/viewaddressmodel.dart';
import 'package:db_eats/data/models/verifyrefreshtokenmodel.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/storage/localstorage.dart';

class AccountBloc extends Bloc<AccountEvent, AccountState> {
  AccountBloc() : super(AccountState()) {
    on<ViewProfileEvent>(_viewProfile);
    on<UpdateProfilePictureEvent>(_updateProfilePicture);
    on<AddFavouritesEvent>(_addFavourites);
    on<AddFavouriteChefEvent>(_addFavouriteChef);
    on<RemoveFavouriteChefEvent>(_removeFavouriteChef);
    on<ListFavoriteChefsEvent>(_listFavoriteChefs);
    on<AddToCartEvent>(_addToCart);
    on<ListAddressesEvent>(_listAddresses);
    on<EditAddressEvent>(_editAddress);
    on<DeleteAddressEvent>(_deleteAddress);
    on<AddAddressEvent>(_addAddress);
    on<AddCurrentAddressEvent>(_addCurrentAddress);
    on<ViewAddressEvent>(_viewAddress);
    on<ListCartEvent>(_listCart);
    on<ViewCartEvent>(_viewCart);
    on<RemoveFromCartEvent>(_removeFromCart);
    on<RemoveCartItemEvent>(_removeCartItem);
    on<UpdateCartItemQuantityEvent>(_updateCartItemQuantity);
    on<ListFavouriteChefsEvent>(_listFavouriteChefs);
    on<ListFavouriteDishes>(_listFavouriteDishes);
    on<GetCartCountEvent>(_getCartCount);
    on<WalletDepositEvent>(_walletDeposit);
    on<WalletWithdrawEvent>(_walletWithdraw);
    on<WalletTransactionListEvent>(_walletTransactionListEvent);
    on<RefreshTokenEvent>(_refreshToken);
    on<AddSearchAddressEvent>(_addSearchAddress);
    on<EditSearchAddressEvent>(_editSearchAddress);
    on<ListSearchAddressesEvent>(_listSearchAddresses);
  }

  Future<void> _viewProfile(
      ViewProfileEvent event, Emitter<AccountState> emit) async {
    emit(ViewProfileLoading());
    try {
      final response =
          await ServerHelper.post1('/v1/customer/profile/view', {});
      log('Profile Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            refreshToken: LocalStorage.getRefreshToken().toString(),
            nextEvent: event));
        return;
      }

      account.AccountInfoModel accountInfoModel =
          account.AccountInfoModel.fromJson(response);

      if (accountInfoModel.status == true) {
        emit(ViewProfileSuccess(accountInfoModel.data!));
      } else {
        emit(ViewProfileFailed(
            accountInfoModel.message ?? 'Failed to load profile'));
      }
    } catch (e) {
      log('Error viewing profile: $e');
      emit(ViewProfileFailed('Error occurred while loading profile'));
    }
  }

  Future<void> _updateProfilePicture(
      UpdateProfilePictureEvent event, Emitter<AccountState> emit) async {
    final currentState = state;
    account.Data? previousProfileData;
    if (currentState is ViewProfileSuccess) {
      previousProfileData = currentState.profileData;
    }

    emit(UpdateProfilePictureLoading(previousProfileData!));
    try {
      final response = await ServerHelper.uploadFile(
        '/v1/customer/profile/add-profile-picture',
        'profile_picture',
        event.imageFile,
      );
      log('Update Profile Picture Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            refreshToken: LocalStorage.getRefreshToken().toString(),
            nextEvent: event));
        return;
      }

      if (response['status'] == true) {
        emit(UpdateProfilePictureSuccess(
            response['message'] ?? 'Profile picture updated'));
        add(ViewProfileEvent());
      } else {
        emit(UpdateProfilePictureFailed(
            response['message'] ?? 'Failed to update profile picture'));
      }
    } catch (e) {
      log('Error updating profile picture: $e');
      emit(UpdateProfilePictureFailed(
          'Error occurred while updating profile picture'));
    }
  }

  Future<void> _addFavourites(
      AddFavouritesEvent event, Emitter<AccountState> emit) async {
    emit(AddFavouritesLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/favourite/add', {'chef_dish_id': event.chef_dish_id});
      log('Add Favourites Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: LocalStorage.getRefreshToken().toString()));
        return;
      }

      if (response['status'] == true) {
        emit(
            AddFavouritesSuccess(response['message'] ?? 'Added to favourites'));
      } else {
        emit(AddFavouritesFailed(
            response['message'] ?? 'Failed to add to favourites'));
      }
    } catch (e) {
      log('Error adding to favourites: $e');
      emit(AddFavouritesFailed('Error occurred while adding to favourites'));
    }
  }

  Future<void> _addFavouriteChef(
      AddFavouriteChefEvent event, Emitter<AccountState> emit) async {
    emit(AddFavouriteChefLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/favourite_chefs/add', {'chef_id': event.chef_id});
      log('Add Favourite Chef Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: LocalStorage.getRefreshToken().toString()));
        return;
      }

      if (response['status'] == true) {
        emit(AddFavouriteChefSuccess(
            response['message'] ?? 'Added to favourites'));
      } else {
        emit(AddFavouriteChefFailed(
            response['message'] ?? 'Failed to add to favourites'));
      }
    } catch (e) {
      log('Error adding to favourites: $e');
      emit(AddFavouriteChefFailed('Error occurred while adding to favourites'));
    }
  }

  Future<void> _removeFavouriteChef(
      RemoveFavouriteChefEvent event, Emitter<AccountState> emit) async {
    emit(RemoveFavouriteChefLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/favourite_chefs/remove', {'chef_id': event.chef_id});
      log('Remove Favourite Chef Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: LocalStorage.getRefreshToken().toString()));
        return;
      }

      if (response['status'] == true) {
        emit(RemoveFavouriteChefSuccess(
            response['message'] ?? 'Removed from favourites'));
      } else {
        emit(RemoveFavouriteChefFailed(
            response['message'] ?? 'Failed to remove from favourites'));
      }
    } catch (e) {
      log('Error removing from favourites: $e');
      emit(RemoveFavouriteChefFailed(
          'Error occurred while removing from favourites'));
    }
  }

  Future<void> _listFavoriteChefs(
      ListFavoriteChefsEvent event, Emitter<AccountState> emit) async {
    emit(ListFavoriteChefsLoading());
    try {
      final response =
          await ServerHelper.get1('/v1/customer/favourite_chefs/list');
      log('List Favorite Chefs Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: LocalStorage.getRefreshToken().toString()));
        return;
      }

      if (response['status'] == true) {
        emit(ListFavoriteChefsSuccess(response['data']));
      } else {
        emit(ListFavoriteChefsFailed(
            response['message'] ?? 'Failed to list favorite chefs'));
      }
    } catch (e) {
      log('Error listing favorite chefs: $e');
      emit(ListFavoriteChefsFailed(
          'Error occurred while listing favorite chefs'));
    }
  }

  Future<void> _listAddresses(
      ListAddressesEvent event, Emitter<AccountState> emit) async {
    emit(ListAddressesLoading());
    try {
      final response = await ServerHelper.get1('/v1/customer/address/list');
      log('List Addresses Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: LocalStorage.getRefreshToken().toString()));
        return;
      }

      ListAddressModel listAddressModel = ListAddressModel.fromJson(response);

      if (listAddressModel.status == true) {
        emit(ListAddressesSuccess(listAddressModel.data));
      } else {
        emit(ListAddressesFailed(
            response['message'] ?? 'Failed to list addresses'));
      }
    } catch (e) {
      log('Error listing addresses: $e');
      emit(ListAddressesFailed('Error occurred while listing addresses'));
    }
  }

  Future<void> _editAddress(
      EditAddressEvent event, Emitter<AccountState> emit) async {
    emit(EditAddressLoading());
    try {
      final response = await ServerHelper.post1(
        '/v1/customer/address/edit',
        event.data,
      );
      log('Edit Address Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: LocalStorage.getRefreshToken().toString()));
        return;
      }

      if (response['status'] == true) {
        AddressData? addressData;
        if (response['data'] != null) {
          addressData = AddressData.fromJson(response['data']);
        }
        emit(EditAddressSuccess(response['message'], addressData: addressData));
      } else {
        emit(
            EditAddressFailed(response['message'] ?? 'Failed to edit address'));
      }
    } catch (e) {
      log('Error editing address: $e');
      emit(EditAddressFailed('Error occurred while editing address'));
    }
  }

  Future<void> _deleteAddress(
      DeleteAddressEvent event, Emitter<AccountState> emit) async {
    emit(DeleteAddressLoading());
    try {
      final response = await ServerHelper.post1(
        '/v1/customer/address/delete',
        {'id': event.addressId},
      );
      log('Delete Address Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: LocalStorage.getRefreshToken().toString()));
        return;
      }

      if (response['status'] == true) {
        emit(DeleteAddressSuccess(response['message']));
        add(ListAddressesEvent());
      } else {
        emit(DeleteAddressFailed(
            response['message'] ?? 'Failed to delete address'));
      }
    } catch (e) {
      log('Error deleting address: $e');
      emit(DeleteAddressFailed('Error occurred while deleting address'));
    }
  }

  Future<void> _addToCart(
      AddToCartEvent event, Emitter<AccountState> emit) async {
    final dishId = event.data['chef_dish_id'] as int;
    emit(AddToCartLoading(dishId));
    try {
      final response = await ServerHelper.post1(
        '/v1/customer/cart/add',
        event.data,
      );
      log('Add To Cart Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: LocalStorage.getRefreshToken().toString()));
        return;
      }

      if (response['status'] == true) {
        emit(AddToCartSuccess(response['message']));
        add(GetCartCountEvent());
        // Trigger view cart after adding
      } else {
        emit(AddToCartFailed(response['message'] ?? 'Failed to add to cart'));
      }
    } catch (e) {
      log('Error adding to cart: $e');
      emit(AddToCartFailed('Error occurred while adding to cart'));
    }
  }

  Future<void> _addAddress(
      AddAddressEvent event, Emitter<AccountState> emit) async {
    emit(AddAddressLoading());
    try {
      final response = await ServerHelper.post1(
        '/v1/customer/address/add',
        event.data,
      );
      log('Add Address Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: LocalStorage.getRefreshToken().toString()));
        return;
      }

      if (response['status'] == true) {
        AddressData? addressData;
        if (response['data'] != null) {
          addressData = AddressData.fromJson(response['data']);
        }
        emit(AddAddressSuccess(response['message'], addressData: addressData));
      } else {
        emit(AddAddressFailed(response['message'] ?? 'Failed to add address'));
      }
    } catch (e) {
      log('Error adding address: $e');
      emit(AddAddressFailed('Error occurred while adding address'));
    }
  }

  Future<void> _addCurrentAddress(
      AddCurrentAddressEvent event, Emitter<AccountState> emit) async {
    emit(AddCurrentAddressLoading());
    try {
      final response = await ServerHelper.post1(
        '/v1/customer/address/add',
        event.data,
      );
      log('Add Current Address Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: LocalStorage.getRefreshToken().toString()));
        return;
      }

      if (response['status'] == true) {
        emit(AddCurrentAddressSuccess(response['message']));
        add(ListAddressesEvent());
      } else {
        emit(AddCurrentAddressFailed(
            response['message'] ?? 'Failed to add current address'));
      }
    } catch (e) {
      log('Error adding current address: $e');
      emit(AddCurrentAddressFailed(
          'Error occurred while adding current address'));
    }
  }

  Future<void> _viewAddress(
      ViewAddressEvent event, Emitter<AccountState> emit) async {
    emit(ViewAddressLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/address/view', {'id': event.addressId});
      log('View Address Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: LocalStorage.getRefreshToken().toString()));
        return;
      }

      if (response['status'] == true) {
        Initializer.viewAddressModel = ViewAddressModel.fromJson(response);
        emit(ViewAddressSuccess(Initializer.viewAddressModel.data));
      } else {
        emit(
            ViewAddressFailed(response['message'] ?? 'Failed to view address'));
      }
    } catch (e) {
      log('Error viewing address: $e');
      emit(ViewAddressFailed('Error occurred while viewing address'));
    }
  }

  Future<void> _listCart(
      ListCartEvent event, Emitter<AccountState> emit) async {
    emit(ListCartLoading());
    try {
      final response = await ServerHelper.get1('/v1/customer/cart/list');
      log('List Cart Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: LocalStorage.getRefreshToken().toString()));
        return;
      }

      ListCartModel listCartModel = ListCartModel.fromJson(response);

      if (listCartModel.status == true) {
        emit(ListCartSuccess(listCartModel.data));
      } else {
        emit(ListCartFailed(response['message'] ?? 'Failed to list cart'));
      }
    } catch (e) {
      log('Error listing cart: $e');
      emit(ListCartFailed('Error occurred while listing cart'));
    }
  }

  Future<void> _viewCart(
      ViewCartEvent event, Emitter<AccountState> emit) async {
    emit(ViewCartLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/cart/view', {'chef_id': event.chef_id});
      log('View Cart Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: LocalStorage.getRefreshToken().toString()));
        return;
      }

      ViewCartModel viewCartModel = ViewCartModel.fromJson(response);

      if (viewCartModel.status == true) {
        emit(ViewCartSuccess(viewCartModel.data));
      } else {
        emit(ViewCartFailed(response['message'] ?? 'Failed to view cart'));
      }
    } catch (e) {
      log('Error viewing cart: $e');
      emit(ViewCartFailed('Error occurred while viewing cart'));
    }
  }

  Future<void> _removeFromCart(
      RemoveFromCartEvent event, Emitter<AccountState> emit) async {
    emit(RemoveFromCartLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/cart/remove', {'chef_ids': event.cart_ids});
      log('Remove From Cart Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: LocalStorage.getRefreshToken().toString()));
        return;
      }

      if (response['status'] == true) {
        emit(RemoveFromCartSuccess(response['message']));
      } else {
        emit(RemoveFromCartFailed(
            response['message'] ?? 'Failed to remove from cart'));
      }
    } catch (e) {
      log('Error removing from cart: $e');
      emit(RemoveFromCartFailed('Error occurred while removing from cart'));
    }
  }

  Future<void> _removeCartItem(
      RemoveCartItemEvent event, Emitter<AccountState> emit) async {
    emit(RemoveCartItemLoading(event.dish_id));
    try {
      final response = await ServerHelper.post1(
        '/v1/customer/cart/remove-item',
        {'dish_id': event.dish_id},
      );
      log('Remove Cart Item Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: LocalStorage.getRefreshToken().toString()));
        return;
      }

      if (response['status'] == true) {
        emit(RemoveCartItemSuccess(response['message']));
      } else {
        emit(RemoveCartItemFailed(
            response['message'] ?? 'Failed to remove cart item'));
      }
    } catch (e) {
      log('Error removing cart item: $e');
      emit(RemoveCartItemFailed('Error occurred while removing cart item'));
    }
  }

  Future<void> _updateCartItemQuantity(
      UpdateCartItemQuantityEvent event, Emitter<AccountState> emit) async {
    emit(UpdateCartItemQuantityLoading(event.cartItemId));
    try {
      final response = await ServerHelper.post1(
        '/v1/customer/cart/update-quantity',
        {
          'cart_item_id': event.cartItemId,
          'quantity': event.quantity,
        },
      );
      log('Update Cart Item Quantity Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: LocalStorage.getRefreshToken().toString()));
        return;
      }

      if (response['status'] == true) {
        ViewCartModel viewCartModel = ViewCartModel.fromJson(response);
        emit(ViewCartSuccess(
            viewCartModel.data)); // Emit view cart success directly
        emit(UpdateCartItemQuantitySuccess(response['message']));
      } else {
        emit(UpdateCartItemQuantityFailed(
            response['message'] ?? 'Failed to update quantity'));
      }
    } catch (e) {
      emit(UpdateCartItemQuantityFailed('Error updating quantity'));
    }
  }

  Future<void> _listFavouriteChefs(
      ListFavouriteChefsEvent event, Emitter<AccountState> emit) async {
    emit(ListFavouriteChefsLoading());
    try {
      final response =
          await ServerHelper.get1('/v1/customer/favourite_chefs/list');
      log('List Favourite Chefs Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: LocalStorage.getRefreshToken().toString()));
        return;
      }

      ListFavouriteChefsModel listFavouriteChefsModel =
          ListFavouriteChefsModel.fromJson(response);

      if (listFavouriteChefsModel.status == true) {
        emit(ListFavouriteChefsSuccess(listFavouriteChefsModel.data));
      } else {
        emit(ListFavouriteChefsFailed(
            response.message ?? 'Failed to list favourite chefs'));
      }
    } catch (e) {
      log('Error listing favourite chefs: $e');
      emit(ListFavouriteChefsFailed(
          'Error occurred while listing favourite chefs'));
    }
  }

  Future<void> _listFavouriteDishes(
      ListFavouriteDishes event, Emitter<AccountState> emit) async {
    emit(ListFavouriteDishesLoading());
    try {
      final response = await ServerHelper.get1('/v1/customer/favourite/list');
      log('List Favourite Dishes Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: LocalStorage.getRefreshToken().toString()));
        return;
      }

      Initializer.listFavouriteDishesModel =
          ListFavouriteDishesModel.fromJson(response);

      if (Initializer.listFavouriteDishesModel.status == true) {
        emit(ListFavouriteDishesSuccess(
            Initializer.listFavouriteDishesModel.data!));
      } else {
        emit(ListFavouriteDishesFailed(
            response['message'] ?? 'Failed to list favourite dishes'));
      }
    } catch (e) {
      log('Error listing favourite dishes: $e');
      emit(ListFavouriteDishesFailed(
          'Error occurred while listing favourite dishes'));
    }
  }

  Future<void> _getCartCount(
      GetCartCountEvent event, Emitter<AccountState> emit) async {
    emit(GetCartCountLoading());
    try {
      final response = await ServerHelper.get1('/v1/customer/cart/count');
      log('Get Cart Count Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: LocalStorage.getRefreshToken().toString()));
        return;
      }

      if (response['status'] == true) {
        final count = response['data']['cart_count'] ?? 0;
        emit(GetCartCountSuccess(count));
        Initializer.cartCount = count;
      } else {
        emit(GetCartCountFailed(
            response['message'] ?? 'Failed to get cart count'));
      }
    } catch (e) {
      log('Error getting cart count: $e');
      emit(GetCartCountFailed('Error occurred while getting cart count'));
    }
  }

  FutureOr<void> _walletDeposit(
      WalletDepositEvent event, Emitter<AccountState> emit) async {
    emit(WalletDepositLoading());
    try {
      final response = await ServerHelper.post1('/v1/customer/wallet/deposit', {
        'amount': event.amount,
      });

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: LocalStorage.getRefreshToken().toString()));
        return;
      }

      Initializer.walletWithdrowDepositModel =
          WalletWithdrowDepositModel.fromJson(response);

      if (Initializer.walletWithdrowDepositModel.status == true) {
        emit(WalletDepositSuccess());
        add(WalletTransactionListEvent(''));
      } else {
        emit(WalletDepositFailed());
      }
    } catch (e) {
      log('Error depositing to wallet: $e');
      emit(WalletDepositFailed());
    }
  }

  FutureOr<void> _walletWithdraw(
      WalletWithdrawEvent event, Emitter<AccountState> emit) async {
    emit(WalletWithdrawLoading());
    try {
      final response =
          await ServerHelper.post1('/v1/customer/wallet/withdraw', {
        'amount': event.amount,
      });

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: LocalStorage.getRefreshToken().toString()));
        return;
      }

      Initializer.walletWithdrowDepositModel =
          WalletWithdrowDepositModel.fromJson(response);
      if (Initializer.walletWithdrowDepositModel.status == true) {
        emit(WalletWithdrawSuccess());
        add(WalletTransactionListEvent(''));
      } else {
        emit(WalletWithdrawFailed());
      }
    } catch (e) {
      log('Error withdrawing from wallet: $e');
      emit(WalletWithdrawFailed());
    }
  }

  FutureOr<void> _walletTransactionListEvent(
      WalletTransactionListEvent event, Emitter<AccountState> emit) async {
    try {
      emit(WalletTransactionListLoading());
      final response = await ServerHelper.get1(
          '/v1/customer/wallet/listAllTransactions?limit=${event.limit}&page=${event.page}');
      log('Wallet Transaction List Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: WalletTransactionListEvent(event.id,
                limit: event.limit, page: event.page),
            refreshToken: LocalStorage.getRefreshToken().toString()));
        return;
      }

      final newTransactions = WalletTransactionListModel.fromJson(response);

      if (event.page == 1) {
        // First page - replace existing data
        Initializer.walletTransactionListModel = newTransactions;
      } else if (newTransactions.data?.transactions != null) {
        // Subsequent pages - append new transactions
        Initializer.walletTransactionListModel.data?.transactions
            ?.addAll(newTransactions.data!.transactions!);
        // Update pagination info
        Initializer.walletTransactionListModel.data?.pagination =
            newTransactions.data?.pagination;
      }

      if (newTransactions.statusCode == 200 && newTransactions.status!) {
        emit(WalletTransactionLoadingSuccess());
      } else {
        emit(WalletTransactionLoadingAFailed());
      }
    } catch (e) {
      emit(WalletTransactionLoadingAFailed());
    }
  }

  FutureOr<void> _refreshToken(
      RefreshTokenEvent event, Emitter<AccountState> emit) async {
    emit(RefreshTokenLoading());
    try {
      Initializer.verifyRefreshTokenModel = VerifyRefreshTokenModel.fromJson(
          await ServerHelper.getrefresh(
              '/v1/customer/auth/verify-refresh-token'));
      if (Initializer.verifyRefreshTokenModel.status == true) {
        await LocalStorage.setAccessToken(
            Initializer.verifyRefreshTokenModel.data?.accessToken ?? "");
        emit(RefreshTokenSuccess());
        if (event.nextEvent != null) {
          add(event.nextEvent!);
        }
      } else if (Initializer.verifyRefreshTokenModel.statusCode == 401) {
        emit(RefreshTokenFailed());
        // Retry the original event
        // await LocalStorage.setAccessToken('');
        // await LocalStorage.setRefreshToken('');
        // Navigator.of(context).pushAndRemoveUntil(
        //   MaterialPageRoute(builder: (context) => const Home()),
        //   (route) => false,
        // );
      }
    } catch (e) {}
  }

  Future<void> _addSearchAddress(
      AddSearchAddressEvent event, Emitter<AccountState> emit) async {
    emit(AddSearchAddressLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/address-search/add', event.data);
      log('Add Search Address Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: LocalStorage.getRefreshToken().toString()));
        return;
      }

      if (response['status'] == true) {
        emit(AddSearchAddressSuccess(response['message']));
      } else {
        emit(AddSearchAddressFailed(
            response['message'] ?? 'Failed to add search address'));
      }
    } catch (e) {
      log('Error adding search address: $e');
      emit(
          AddSearchAddressFailed('Error occurred while adding search address'));
    }
  }

  Future<void> _editSearchAddress(
      EditSearchAddressEvent event, Emitter<AccountState> emit) async {
    emit(EditSearchAddressLoading());
    try {
      final response = await ServerHelper.post1(
          '/v1/customer/address-search/edit', event.data);
      log('Edit Search Address Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: LocalStorage.getRefreshToken().toString()));
        return;
      }

      if (response['status'] == true) {
        emit(EditSearchAddressSuccess(response['message']));
      } else {
        emit(EditSearchAddressFailed(
            response['message'] ?? 'Failed to edit search address'));
      }
    } catch (e) {
      log('Error editing search address: $e');
      emit(EditSearchAddressFailed(
          'Error occurred while editing search address'));
    }
  }

  Future<void> _listSearchAddresses(
      ListSearchAddressesEvent event, Emitter<AccountState> emit) async {
    emit(ListSearchAddressesLoading());
    try {
      final response =
          await ServerHelper.get1('/v1/customer/address-search/list');
      log('List Search Addresses Response: $response');

      if (response['status_code'] == 401) {
        add(RefreshTokenEvent(
            nextEvent: event,
            refreshToken: LocalStorage.getRefreshToken().toString()));
        return;
      }
      Initializer.listSearchedAddressesModel =
          ListSearchedAddressesModel.fromJson(response);

      if (Initializer.listSearchedAddressesModel.status == true) {
        emit(ListSearchAddressesSuccess(
            Initializer.listSearchedAddressesModel.data));
      } else {
        emit(ListSearchAddressesFailed(
            response['message'] ?? 'Failed to list search addresses'));
      }
    } catch (e) {
      log('Error listing search addresses: $e');
      emit(ListSearchAddressesFailed(
          'Error occurred while listing search addresses'));
    }
  }
}

// Events
class AccountEvent {}

class ViewProfileEvent extends AccountEvent {}

class UpdateProfilePictureEvent extends AccountEvent {
  final File imageFile;
  UpdateProfilePictureEvent(this.imageFile);
}

class AddFavouritesEvent extends AccountEvent {
  final int chef_dish_id;
  AddFavouritesEvent(this.chef_dish_id);
}

class AddFavouriteChefEvent extends AccountEvent {
  final int chef_id;
  AddFavouriteChefEvent(this.chef_id);
}

class RemoveFavouriteChefEvent extends AccountEvent {
  final int chef_id;
  RemoveFavouriteChefEvent(this.chef_id);
}

class ListFavoriteChefsEvent extends AccountEvent {}

class AddAddressEvent extends AccountEvent {
  final Map<String, dynamic> data;
  AddAddressEvent(this.data);
}

class AddCurrentAddressEvent extends AccountEvent {
  final Map<String, dynamic> data;
  AddCurrentAddressEvent(this.data);
}

class ViewAddressEvent extends AccountEvent {
  final int addressId;
  ViewAddressEvent(this.addressId);
}

class ListAddressesEvent extends AccountEvent {}

class EditAddressEvent extends AccountEvent {
  final Map<String, dynamic> data;
  EditAddressEvent(this.data);
}

class DeleteAddressEvent extends AccountEvent {
  final int addressId;
  DeleteAddressEvent(this.addressId);
}

class AddToCartEvent extends AccountEvent {
  final Map<String, dynamic> data;

  AddToCartEvent(this.data);

  @override
  List<Object?> get props => [data];
}

class ListCartEvent extends AccountEvent {}

class ViewCartEvent extends AccountEvent {
  final int chef_id;
  ViewCartEvent(this.chef_id);
}

class RemoveFromCartEvent extends AccountEvent {
  final List<int> cart_ids;
  RemoveFromCartEvent(this.cart_ids);
}

class RemoveCartItemEvent extends AccountEvent {
  final int dish_id; // Changed back to dish_id
  RemoveCartItemEvent(this.dish_id);
}

class UpdateCartItemQuantityEvent extends AccountEvent {
  final int cartItemId;
  final int quantity;
  UpdateCartItemQuantityEvent(this.cartItemId, this.quantity);
}

class ListFavouriteChefsEvent extends AccountEvent {}

class ListFavouriteDishes extends AccountEvent {}

// States
class AccountState {}

class ViewProfileLoading extends AccountState {}

class ViewProfileSuccess extends AccountState {
  final account.Data profileData;
  ViewProfileSuccess(this.profileData);
}

class ViewProfileFailed extends AccountState {
  final String message;
  ViewProfileFailed(this.message);
}

class UpdateProfilePictureLoading extends AccountState {
  final account.Data previousProfileData;
  UpdateProfilePictureLoading(this.previousProfileData);
}

class UpdateProfilePictureSuccess extends AccountState {
  final String message;
  UpdateProfilePictureSuccess(this.message);
}

class UpdateProfilePictureFailed extends AccountState {
  final String message;
  UpdateProfilePictureFailed(this.message);
}

class AddFavouritesLoading extends AccountState {}

class AddFavouritesSuccess extends AccountState {
  final String message;
  AddFavouritesSuccess(this.message);
}

class AddFavouritesFailed extends AccountState {
  final String message;
  AddFavouritesFailed(this.message);
}

class AddFavouriteChefLoading extends AccountState {}

class AddFavouriteChefSuccess extends AccountState {
  final String message;
  AddFavouriteChefSuccess(this.message);
}

class AddFavouriteChefFailed extends AccountState {
  final String message;
  AddFavouriteChefFailed(this.message);
}

class RemoveFavouriteChefLoading extends AccountState {}

class RemoveFavouriteChefSuccess extends AccountState {
  final String message;
  RemoveFavouriteChefSuccess(this.message);
}

class RemoveFavouriteChefFailed extends AccountState {
  final String message;
  RemoveFavouriteChefFailed(this.message);
}

class ListFavoriteChefsLoading extends AccountState {}

class ListFavoriteChefsSuccess extends AccountState {
  final dynamic data;
  ListFavoriteChefsSuccess(this.data);
}

class ListFavoriteChefsFailed extends AccountState {
  final String message;
  ListFavoriteChefsFailed(this.message);
}

class AddAddressLoading extends AccountState {}

class AddAddressSuccess extends AccountState {
  final String message;
  final AddressData? addressData;
  AddAddressSuccess(this.message, {this.addressData});
}

class AddAddressFailed extends AccountState {
  final String message;
  AddAddressFailed(this.message);
}

class AddCurrentAddressLoading extends AccountState {}

class AddCurrentAddressSuccess extends AccountState {
  final String message;
  AddCurrentAddressSuccess(this.message);
}

class AddCurrentAddressFailed extends AccountState {
  final String message;
  AddCurrentAddressFailed(this.message);
}

class ListAddressesLoading extends AccountState {}

class ListAddressesSuccess extends AccountState {
  final dynamic data;
  ListAddressesSuccess(this.data);
}

class ListAddressesFailed extends AccountState {
  final String message;
  ListAddressesFailed(this.message);
}

class EditAddressLoading extends AccountState {}

class EditAddressSuccess extends AccountState {
  final String message;
  final AddressData? addressData;
  EditAddressSuccess(this.message, {this.addressData});
}

class EditAddressFailed extends AccountState {
  final String message;
  EditAddressFailed(this.message);
}

class AddToCartLoading extends AccountState {
  final int dishId;

  AddToCartLoading(this.dishId);

  @override
  List<Object?> get props => [dishId];
}

class AddToCartSuccess extends AccountState {
  final String message;
  AddToCartSuccess(this.message);
}

class AddToCartFailed extends AccountState {
  final String message;
  AddToCartFailed(this.message);
}

class ListCartLoading extends AccountState {}

class ListCartSuccess extends AccountState {
  final dynamic data;
  ListCartSuccess(this.data);
}

class ListCartFailed extends AccountState {
  final String message;
  ListCartFailed(this.message);
}

class ViewCartLoading extends AccountState {}

class ViewCartSuccess extends AccountState {
  final dynamic data;
  ViewCartSuccess(this.data);
}

class ViewCartFailed extends AccountState {
  final String message;
  ViewCartFailed(this.message);
}

class RemoveFromCartLoading extends AccountState {}

class RemoveFromCartSuccess extends AccountState {
  final String message;
  RemoveFromCartSuccess(this.message);
}

class RemoveFromCartFailed extends AccountState {
  final String message;
  RemoveFromCartFailed(this.message);
}

class RemoveCartItemLoading extends AccountState {
  final int cartItemId;
  RemoveCartItemLoading(this.cartItemId);
}

class RemoveCartItemSuccess extends AccountState {
  final String message;
  RemoveCartItemSuccess(this.message);
}

class RemoveCartItemFailed extends AccountState {
  final String message;
  RemoveCartItemFailed(this.message);
}

class UpdateCartItemQuantityLoading extends AccountState {
  final int cartItemId;
  UpdateCartItemQuantityLoading(this.cartItemId);
}

class UpdateCartItemQuantitySuccess extends AccountState {
  final String message;
  UpdateCartItemQuantitySuccess(this.message);
}

class UpdateCartItemQuantityFailed extends AccountState {
  final String message;
  UpdateCartItemQuantityFailed(this.message);
}

class ListFavouriteChefsLoading extends AccountState {}

class ListFavouriteChefsSuccess extends AccountState {
  final dynamic data;
  ListFavouriteChefsSuccess(this.data);
}

class ListFavouriteChefsFailed extends AccountState {
  final String message;
  ListFavouriteChefsFailed(this.message);
}

class ListFavouriteDishesLoading extends AccountState {}

class ListFavouriteDishesSuccess extends AccountState {
  final dynamic data;
  ListFavouriteDishesSuccess(this.data);
}

class ListFavouriteDishesFailed extends AccountState {
  final String message;
  ListFavouriteDishesFailed(this.message);
}

// Get Cart Count
class GetCartCountEvent extends AccountEvent {}

class GetCartCountLoading extends AccountState {}

class GetCartCountSuccess extends AccountState {
  final int count;
  GetCartCountSuccess(this.count);
}

class GetCartCountFailed extends AccountState {
  final String message;
  GetCartCountFailed(this.message);
}

//wallet Diposits
class WalletDepositEvent extends AccountEvent {
  final double amount;
  WalletDepositEvent(this.amount);
}

class WalletDepositLoading extends AccountState {}

class WalletDepositSuccess extends AccountState {}

class WalletDepositFailed extends AccountState {}

//Wallet Withdraw
class WalletWithdrawEvent extends AccountEvent {
  final double amount;
  WalletWithdrawEvent(this.amount);
}

class WalletWithdrawLoading extends AccountState {}

class WalletWithdrawSuccess extends AccountState {}

class WalletWithdrawFailed extends AccountState {}

// Wallet Transaction list
class WalletTransactionListEvent extends AccountEvent {
  final String id;
  final int page;
  final int limit;

  WalletTransactionListEvent(this.id, {this.page = 1, this.limit = 10});
}

class WalletTransactionListLoading extends AccountState {}

class WalletTransactionLoadingSuccess extends AccountState {}

class WalletTransactionLoadingAFailed extends AccountState {}

// Refresh Token verification
class RefreshTokenEvent extends AccountEvent {
  final String refreshToken;
  final AccountEvent? nextEvent;

  RefreshTokenEvent({required this.refreshToken, required this.nextEvent});
}

class AddSearchAddressEvent extends AccountEvent {
  final Map<String, dynamic> data;
  AddSearchAddressEvent(this.data);
}

class EditSearchAddressEvent extends AccountEvent {
  final Map<String, dynamic> data;
  EditSearchAddressEvent(this.data);
}

class ListSearchAddressesEvent extends AccountEvent {}

class RefreshTokenLoading extends AccountState {}

class RefreshTokenSuccess extends AccountState {}

class RefreshTokenFailed extends AccountState {}

class ViewAddressLoading extends AccountState {}

class ViewAddressSuccess extends AccountState {
  final dynamic data;
  ViewAddressSuccess(this.data);
}

class ViewAddressFailed extends AccountState {
  final String message;
  ViewAddressFailed(this.message);
}

class DeleteAddressLoading extends AccountState {}

class DeleteAddressSuccess extends AccountState {
  final String message;
  DeleteAddressSuccess(this.message);
}

class DeleteAddressFailed extends AccountState {
  final String message;
  DeleteAddressFailed(this.message);
}

class AddSearchAddressLoading extends AccountState {}

class AddSearchAddressSuccess extends AccountState {
  final String message;
  AddSearchAddressSuccess(this.message);
}

class AddSearchAddressFailed extends AccountState {
  final String message;
  AddSearchAddressFailed(this.message);
}

class EditSearchAddressLoading extends AccountState {}

class EditSearchAddressSuccess extends AccountState {
  final String message;
  EditSearchAddressSuccess(this.message);
}

class EditSearchAddressFailed extends AccountState {
  final String message;
  EditSearchAddressFailed(this.message);
}

class ListSearchAddressesLoading extends AccountState {}

class ListSearchAddressesSuccess extends AccountState {
  final dynamic data;
  ListSearchAddressesSuccess(this.data);
}

class ListSearchAddressesFailed extends AccountState {
  final String message;
  ListSearchAddressesFailed(this.message);
}
